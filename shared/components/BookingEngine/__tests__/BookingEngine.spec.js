import { shallowMount } from "@vue/test-utils"
import { beforeEach, describe, expect, it, vi } from "vitest"
import BookingEngine from "../BookingEngine.vue" // Adjust the path if necessary

// Mock components
vi.mock("@accor/ads-components", () => ({
  AdsButton: { template: "<button @click=\"$emit('click')\"></button>" },
  AdsDatePicker: { template: "<div></div>" },
  AdsInputStepper: { template: '<input type="number" />' },
  AdsSelect: { template: "<select></select>" }
}))

describe("BookingEngine.vue", () => {
  let wrapper

  const mockHotels = [
    { id: "1", name: "Hotel One", country: "France" },
    { id: "2", name: "Hotel Two", country: "Spain" }
  ]

  beforeEach(() => {
    wrapper = shallowMount(BookingEngine, {
      props: {
        title: "Find a Hotel",
        hotels: Promise.resolve(mockHotels)
      }
    })
  })

  it("renders correctly", async () => {
    expect(wrapper.text()).toContain("Find a Hotel")
  })

  it("loads hotels and maps them to options", async () => {
    await wrapper.vm.$nextTick()
    expect(wrapper.vm.hotelOptions).toEqual({
      France: [{ label: "Hotel One", value: "1" }],
      Spain: [{ label: "Hotel Two", value: "2" }]
    })
  })

  it("should have looading false", async () => {
    //await wrapper.vm.$nextTick()
    expect(wrapper.vm.loading).toBe(false)
  })

  it("displays loading message before hotels load", async () => {
    //await wrapper.vm.$nextTick()
    expect(wrapper.text()).not.toContain("Loading hotels...")
  })
})
