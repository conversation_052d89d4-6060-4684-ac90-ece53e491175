import { dirname, resolve } from "node:path"
import { fileURLToPath } from "node:url"
import { defineConfig, mergeConfig } from "vitest/config"
import viteConfig from "./vite.config"

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      environment: "jsdom",
      setupFiles: [resolve(dirname(fileURLToPath(import.meta.url)), "vitest.setup.js")],
      server: {
        deps: {
          inline: ["@accor/ads-components", "@accor/icons", "@accor/shared-utils"]
        }
      }
    }
  })
)
