<!-- TODO to remove DEPRECATED -->
<script setup>
defineProps({
  headerProps: {
    default: () => {},
    type: Object
  },
  links: {
    default: () => [],
    type: Array
  },
  logo: {
    default: "",
    type: String
  },
  title: {
    default: "",
    type: String
  }
})
</script>

<template>
  <header class="Header">
    <div class="Header__content container">
      <div>{{ title }}</div>
      <div class="wrapper">
        <router-link to="/">
          <img alt="Brand Logo" class="svg-logo" :src="logo" width="auto" height="25" />
        </router-link>
      </div>
    </div>
  </header>
</template>

<style lang="scss" scoped>
.Header__content {
  background-color: #272727;
  color: #fff;
  padding: 1.2rem 0;
  margin: 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  /* Optional shadow for visual separation */
}

.svg-logo {
  height: 100%;
  /* Ensure the logo height does not exceed the toolbar height */
  max-height: 35px;
  /* Adjust this value as needed */
  width: auto;
  /* Maintain aspect ratio */
  margin-right: 8px;
  /* Add some space between the logo and the title */
  display: inline-block;
  /* Ensure the logo is displayed inline */
  vertical-align: middle;
  /* Align the logo vertically in the middle */
  color: white;
  filter: invert(1);
  /* Invert colors to make the logo white */
}
</style>
