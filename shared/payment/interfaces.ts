/**
 * @see https://accor-it.atlassian.net/wiki/spaces/PAYM/pages/5649694755/HOW+TO+Payment+Card+Module+User+Guide
 */
export interface PaymentModule {
  initIframe: (options: InitIframeOptions) => void
  onCardModuleEventReceived: (event: PaymentModuleEvent) => void
  postMessageToIframe: (event: PaymentModuleMessage) => void
}

export interface InitIframeOptions {
  params: InitIframeParams
  containerSelector: Parameters<typeof document.querySelector>[0]
  creditCardsConfig?: InitIframeCreditCardsConfig[]
}

export interface InitIframeParams {
  apiKey: string
  tId: string
  cvv?: number
  cb?: number
  wallet?: number
  pay?: number
  dateOut?: string
  lang?: string
  partnerCode?: string
  parcelamento?: number
  obfuscatedCardNumber?: number
}

export interface InitIframeCreditCardsConfig {
  code: string
  walletCard: boolean
  feePercent: number
  installPayment: boolean
  numberOfInstallmentPayments: number
  parentCode: string
}

export type PaymentModuleEvent =
  | PMEGeneric<"cardType", string>
  | PMEGeneric<"clickUI", { interactionType: string; pointerType: string }>
  | PMEGeneric<"resize", { height: number; width: number }>
  | PMEGeneric<"focusField">
  | PMEGeneric<"blurredField">

type PMEGeneric<T extends string, K = undefined> = {
  eventName: T
  data: K
}

export type PaymentModuleMessage = "submit"
