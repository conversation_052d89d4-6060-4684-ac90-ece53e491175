export async function sha256Hash(str: string): Promise<string> {
  const normalized = str.trim().toLowerCase()

  const encoder = new TextEncoder()
  const data = encoder.encode(normalized)

  if (!window.crypto?.subtle) {
    throw new Error("Web Crypto API is not available in this environment.")
  }

  const hashBuffer = await window.crypto.subtle.digest("SHA-256", data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))

  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("")
}
