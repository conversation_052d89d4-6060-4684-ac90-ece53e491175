import { BrandForm } from "./FormStructure"

export interface BrandConfiguration {
  all: {
    account: string
    bestPriceConditions: string
    help: string
    helpAndContact: string
    hostname: string
    privacyPolicy: string
    redirection: string
    travelProUrl: string
  }
  apiHeaders: {
    clientId: string
  }
  apiUrl: string
  app: {
    //basic data for the app
    baseUrl: string
    legalEmail: string
    payApiKey: string
    payModule: string
  }
  appDomain: string
  brandCode: string
  formFieldStructure: BrandForm
  gtmId: string
  kameleoonId: string
  legacy: {
    endpoints: {
      moreNumbers: string
      sitemap: string
      termsAndConditions: string
      webAccessibility: string
    }
    homePage: string
  }
  logo: string
  logoInvert: string
  myBookingsUrl: string
  name: string
  oidc: {
    clientId: string
    logo: string
    params: string
    ui: string
    url: string
  }
  oneTrustId: string
  paymentModuleApiKey: string
  paymentModuleUrl: string
  siteCode: string
  splunk: {
    realm: string
    token: string
    appName: string
    env: string
    domain: string
    leanixId: string
    snowId: string
    itDomain: string
    itDepartment: string
    pci: string
    critical: string
  }
  step3Permalink: {
    client: string
    host: string
    path: string
  }
  theme: object
}

export interface BrandInitConfig {
  //Minimal config needed to init the brand
  logo: string
  logoInvert: string
  theme: object
}
