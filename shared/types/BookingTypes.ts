import { Categories, PricingStayType } from "design-system"

export interface Hotel {
  id: string
  name: string
  location: string
  country: string
  rating: number
}

export interface Offer {
  id: string
  name: string
  price: number
  description: string
  pricing: Pricing
  product: {
    id: string
    label: string
    media?: {
      category: string
      count: number
      formats: Media[]
      medias: {
        category: string
        formats: Media[]
        type: string
      }[]
    }
    keyFeatures: [
      {
        code: string
        label: string
      }
    ]
    bedding: {
      details: [
        {
          label: string
          quantity: number
        }
      ]
    }
  }
  rate: {
    label: string
  }
  occupancy: {
    adults: number
  }
}

export interface Room {
  code: number
  name: string
  description: string
}

export interface User {
  code: number
  firstName: string
  lastName: string
  email: string
}

export interface Identification {
  identificationId: string | null
  affiliation: object | null
}

export interface Media {
  format: string
  generic: boolean
  lastUpdate: string
  url: string
}

export interface Pricing {
  currency: string
  aggregationType: PricingStayType
  formattedAggregationType: string
  alternative: {
    amount: number
    formattedAmount: string
    categories: Categories[]
  }
  main: {
    amount: number
    formattedAmount: string
    categories: Categories[]
  }
}
