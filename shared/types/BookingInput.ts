// src/types/BookingInputs.ts
export interface RoomDetails {
  adults: number // Number of adults in the room
  children: {
    count: number // Number of children
    ages: number[] // Array of ages for children
  }
}

export interface BookingInput {
  hotelId: string // Unique identifier for the hotel
  dates: {
    checkIn: string // Check-in date in ISO format (YYYY-MM-DD)
    checkOut: string // Check-out date in ISO format (YYYY-MM-DD)
  }
  numberOfRooms: number // Total number of rooms being booked
  rooms: RoomDetails[] // Array of room details for each room
}
