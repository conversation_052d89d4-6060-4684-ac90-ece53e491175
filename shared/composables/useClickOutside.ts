export function useClickOutside(
  element: HTMLElement,
  handler: (event: MouseEvent) => void,
  ignoredElement: HTMLElement
) {
  const listener = (event: MouseEvent) => {
    if (
      !element.contains(event.target as Node) &&
      !ignoredElement.contains(event.target as Node) &&
      element !== event.target
    ) {
      handler(event)
    }
  }

  const addEventListener = () => {
    document.addEventListener("mousedown", listener)
  }

  const removeEventListener = () => {
    document.removeEventListener("mousedown", listener)
  }

  return {
    addEventListener,
    removeEventListener
  }
}
