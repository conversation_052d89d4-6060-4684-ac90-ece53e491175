const { execSync } = require("child_process")
const path = require("path")
const fs = require("fs")

// Get the app name from the command line arguments
const appName = process.argv[2]

// Define the root path and apps directory
const rootPath = path.resolve(__dirname)
const appsPath = path.resolve(__dirname, "apps")

// Common function to pack and move an app
const packAndMoveApp = (app, appPath) => {
  const packageJsonPath = path.join(appPath, "package.json")

  // Check if the app has a package.json
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`No package.json found in "${appPath}". Skipping "${app}".`)
    return false
  }

  try {
    console.log(`Packing ${app} ...`)
    // Run the pnpm pack command inside the app directory
    execSync("pnpm pack", { cwd: appPath, stdio: "inherit" })

    // Get the package name and version to determine the .tgz filename
    const packageJson = require(packageJsonPath)
    const packageName = packageJson.name.replace("@", "").replace("/", "-") // Replace scope with hyphen
    const packageVersion = packageJson.version
    const tarballName = `${packageName}-${packageVersion}.tgz`
    const tarballTarget = tarballName.replace("accor-", "")

    console.log(`Successfully packed ${tarballTarget}.`)

    // Define the source and destination paths for the tarball
    const tarballPath = path.join(appPath, tarballName)
    const destinationPath = path.join(rootPath, tarballTarget)

    // Move the tarball to the root directory
    if (fs.existsSync(tarballPath)) {
      fs.renameSync(tarballPath, destinationPath)
      console.log(`Successfully moved ${tarballTarget} to the root of the repository.`)
      return true
    } else {
      console.error(`Failed to find the generated tarball at ${tarballPath}`)
      return false
    }
  } catch (error) {
    console.error(`Failed to pack ${app}:`, error.message)
    return false
  }
}

if (!appName) {
  console.warn("No application name provided, all apps (except Common) will be built")

  // Get all directories in the apps folder, excluding "Common"
  const allApps = fs.readdirSync(appsPath).filter((dir) => {
    const fullPath = path.join(appsPath, dir)
    return fs.statSync(fullPath).isDirectory() && dir.toLowerCase() !== "common"
  })

  allApps.forEach((app) => {
    const appPath = path.resolve(appsPath, app)
    packAndMoveApp(app, appPath)
  })
} else {
  // Define the path to the app
  const appPath = path.resolve(appsPath, appName)

  // Check if the provided app directory exists
  if (!fs.existsSync(appPath)) {
    console.error(`The app directory "${appPath}" does not exist.`)
    process.exit(1)
  }

  const success = packAndMoveApp(appName, appPath)
  if (!success) {
    process.exit(1)
  }
}
