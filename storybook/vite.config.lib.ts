import path, { dirname } from "node:path"
import { defineConfig } from "vite"
import dts from "vite-plugin-dts"
import { fileURLToPath } from "node:url"
import { libInjectCss } from "vite-plugin-lib-inject-css"
import svgLoader from "vite-svg-loader"
import vue from "@vitejs/plugin-vue"

const __dirname = dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  build: {
    cssMinify: true,
    lib: {
      entry: path.resolve(__dirname, "src", "index.ts"),
      fileName: "[name]",
      formats: ["es"]
    },
    minify: true,
    rollupOptions: {
      external: ["vue", "vue-i18n"],
      output: {
        dir: path.join(__dirname, "dist", "lib"),
        esModule: true,
        format: "esm",
        globals: {
          vue: "Vue"
        },
        inlineDynamicImports: false,
        preserveModules: true,
        preserveModulesRoot: path.resolve(__dirname, "src")
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: "legacy",
        includePaths: [path.resolve(__dirname, `src/styles/`)],
        silenceDeprecations: ["legacy-js-api"] // or switch to modern api
      }
    }
  },
  plugins: [
    vue(),
    svgLoader(),
    libInjectCss(),
    dts({ outDir: path.join(__dirname, "dist", "lib"), tsconfigPath: "./tsconfig.lib.json" })
  ],
  resolve: {
    alias: {
      "@sb-base": path.resolve(__dirname, "src/styles/base"),
      "@sb-config": path.resolve(__dirname, "src/styles/config"),
      "@sb-fonts": path.resolve(__dirname, "public/fonts"),
      "@sb-styles": path.resolve(__dirname, "src/styles"),
      "@sb-utilities": path.resolve(__dirname, "src/styles/utilities")
    }
  }
})
