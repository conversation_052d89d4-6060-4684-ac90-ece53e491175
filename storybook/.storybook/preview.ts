import "../src/styles/main.scss"
import type { Preview } from "@storybook/vue3"
import { i18n } from "../src/i18n"
import { setup } from "@storybook/vue3"

// Loads i18n to enable translations (use case: <AdsInput />)
setup((app) => {
  app.use(i18n)
})

const preview: Preview = {
  parameters: {
    backgrounds: {
      options: {
        dark: { name: "Dark", value: "#3A3A3A" },
        light: { name: "Light", value: "#FFFFFF" }
      }
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    },
    fallbackLocale: i18n.fallbackLocale,
    locale: i18n.locale
  },
  tags: ["autodocs"]
}

export default preview
