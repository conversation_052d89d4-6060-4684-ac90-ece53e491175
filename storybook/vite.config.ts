import path, { dirname, resolve } from "node:path"
import { defineConfig } from "vite"
import { fileURLToPath } from "node:url"
import { libInjectCss } from "vite-plugin-lib-inject-css"
import svgLoader from "vite-svg-loader"
import vue from "@vitejs/plugin-vue"

const __dirname = dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  build: {
    cssMinify: true,
    lib: {
      entry: resolve(__dirname, "src"),
      name: "design-system"
    },
    minify: true,
    rollupOptions: {
      external: ["vue"],
      output: {
        globals: {
          vue: "Vue"
        }
      }
    },
    sourcemap: true
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: "legacy",
        includePaths: [path.resolve(__dirname, `src/styles/`)],
        silenceDeprecations: ["legacy-js-api"] // or switch to modern api
      }
    }
  },
  plugins: [vue(), svg<PERSON>oader(), libInjectCss()],
  resolve: {
    alias: {
      "@sb-base": path.resolve(__dirname, "src/styles/base"),
      "@sb-config": path.resolve(__dirname, "src/styles/config"),
      "@sb-fonts": path.resolve(__dirname, "public/fonts"),
      "@sb-styles": path.resolve(__dirname, "src/styles"),
      "@sb-utilities": path.resolve(__dirname, "src/styles/utilities")
    }
  },
  server: {
    allowedHosts: ["local.storybook.fairmont.com"]
  }
})
