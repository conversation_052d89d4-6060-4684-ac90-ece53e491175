{"name": "design-system", "private": true, "version": "0.17.1", "type": "module", "main": "./dist/lib/design-system.js", "module": "./dist/lib/design-system.js", "types": "./dist/lib/index.d.ts", "scripts": {"build": "rm -rf dist/ && vite build --config vite.config.lib.ts", "watch": "vite build --config vite.config.lib.ts --watch", "build:library:types": "vue-tsc -p tsconfig.lib.json", "build-storybook": "storybook build", "dev": "vite", "preview": "vite preview", "storybook": "storybook dev -p 6006 --ci", "test": "vitest run"}, "exports": {".": {"import": "./dist/lib/index.js", "types": "./dist/lib/index.d.ts"}, "./main.scss": {"import": "./src/styles/main.scss"}, "./styles/base/*": {"import": "./src/styles/base/*"}, "./styles/config/*": {"import": "./src/styles/config/*"}, "./styles/fonts/*": {"import": "./public/fonts/*"}, "./styles/utilities/*": {"import": "./src/styles/utilities/*"}}, "sideEffects": false, "dependencies": {"@accor/icons": "^0.2.1", "@storybook/addon-viewport": "^8.6.11", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.4.0", "date-fns": "^4.1.0", "fuse.js": "^7.1.0", "include-media": "^2.0.0", "normalize.css": "^8.0.1", "vite-plugin-lib-inject-css": "^2.2.2", "vue": "^3.5.13", "vue-i18n": "^11.1.7"}, "devDependencies": {"@accor/ads-components": "^2.4.2", "@accor/ads-components-locales": "^0.2.0", "@accor/shared-utils": "^1.2.3", "@chromatic-com/storybook": "^3.2.4", "@storybook/addon-essentials": "^8.5.0", "@storybook/addon-interactions": "^8.5.0", "@storybook/addon-onboarding": "^8.5.0", "@storybook/blocks": "^8.5.0", "@storybook/test": "^8.5.0", "@storybook/vue3": "^8.5.0", "@storybook/vue3-vite": "^8.5.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "glob": "^11.0.3", "sass": "^1.83.4", "storybook": "^8.5.0", "typescript": "~5.6.2", "vite": "^6.1.1", "vite-plugin-dts": "^4.5.4", "vite-svg-loader": "^5.1.0", "vitest": "^3.2.4", "vue-tsc": "^2.2.0"}}