import { AmenitiesColor } from "../../atoms/UiAmenity/enums"
import { Categories } from "../../molecules/UiPrincingSection/enums"
import type { UiRoomCardProps } from "./interface"

export const roomCardMockData: UiRoomCardProps = {
  amenities: [
    {
      accessibilityIconLabel: "Room size:",
      code: "SURFACE_AREA",
      iconName: "size",
      label: "325 sq.ft. / 354 ft",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    },
    {
      accessibilityIconLabel: "Type of bed:",
      code: "BEDDING",
      iconName: "bedDouble",
      label: "One King bed",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    },
    {
      accessibilityIconLabel: "Number of occupants:",
      code: "MAX_PAX",
      iconName: "occupant",
      label: "2 people max",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    },
    {
      accessibilityIconLabel: "View type:",
      code: "VIEWS",
      iconName: "view",
      label: "View on the river",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    },
    {
      accessibilityIconLabel: "Accessible room:",
      code: "ACCESSIBILITY",
      iconName: "wheelchair",
      label: "Accessible room",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    }
  ],
  gallery: { count: 15 },
  image: { src: "https://picsum.photos/360/350?random=1" },
  isOpen: false,
  pricing: {
    currency: "USD",
    expediaCompliant: true,
    formattedAggregationType: "for your stay",
    formattedTaxType: "Taxes and fees included.",
    isLogged: false,
    mainPricing: {
      amount: 640,
      categories: [Categories.STANDARD],
      formattedAmount: "$640"
    }
  },
  productCode: "KGBGS",
  title: "Fairmont 1 king"
}
