import type { UiAmenityProps } from "../../atoms/UiAmenity/interface"
import type { UiImageProps } from "../../atoms/UiImage/interface"
import type { UiPricingSectionProps } from "../../molecules/UiPrincingSection/interface"

export interface UiRoomCardProps {
  /**
   * List of amenities detailing the room's features.
   */
  amenities: UiAmenityProps[]
  /**
   * Bedding inside the room
   */
  bedding?: string
  /**
   * Wether the first room card image loading policy should be eager
   */
  eagerLoading?: boolean
  /**
   * Indicates if the property is compliant with Expedia standards.
   */
  expediaCompliant?: boolean
  /**
   * Gallery properties for displaying multiple images of the room.
   */
  gallery: GalleryType
  /**
   * Image properties for displaying the room's visual representation.
   */
  image: UiImageProps
  /**
   * Image url for card (RoomCard).
   */
  imageCardSrc?: string
  /**
   * Loading state for rate cards
   */
  isLoadingRateCards?: boolean
  /**
   * Indicates if the room card is open or closed.
   */
  isOpen: boolean
  /**
   * The pricing details and configurations for the UI section.
   */
  pricing: UiPricingSectionProps
  /*
   * Code of the offer product.
   */
  productCode: string
  /**
   * The room card's title, indicating the room's name or type.
   */
  title: string
}

export interface GalleryType {
  /**
   * Total number of media items in the gallery
   */
  count: number
  /**
   * Optional array of media objects to display in the gallery
   */
  medias?: UiImageProps[]
}
