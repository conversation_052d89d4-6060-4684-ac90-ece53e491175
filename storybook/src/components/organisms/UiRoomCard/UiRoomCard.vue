<script setup lang="ts">
import { UiImageDisplayMode, UiImageLoading } from "../../atoms/UiImage/enums"
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiAmenitiesCard from "../../molecules/UiAmenitiesCard/UiAmenitiesCard.vue"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import UiImageCount from "../../molecules/UiImageCount/UiImageCount.vue"
import { UiImageCountPosition } from "../../molecules"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import UiModal from "../../molecules/UiModal/UiModal.vue"
import UiModalHeader from "../../molecules/UiModalHeader/UiModalHeader.vue"
import UiPricingSection from "../../molecules/UiPrincingSection/UiPrincingSection.vue"
import { type UiRoomCardProps } from "./interface"
import { useCurrentWindowSize } from "../../../composables/index"

const { isMobile } = useCurrentWindowSize()

withDefaults(defineProps<UiRoomCardProps>(), {
  isLoadingRateCards: false
})

defineEmits([
  "UiRoomCardGalleryButton::click",
  "UiRoomCardDetailsButton::click",
  "UiRoomCardSelectButton::click",
  "UiRoomCardCloseButton::click"
])
</script>

<template>
  <article class="Room-card">
    <div class="Room-card__container">
      <div class="Room-card__image-container">
        <!-- No alt if generic image -->
        <UiImage
          v-bind="image"
          :alt="
            image.src || image.srcSet?.medias.length
              ? $t('ui.organisms.ui_room_card.accessibility.room_image', { room: title })
              : undefined
          "
          :display-mode="UiImageDisplayMode.SMALL"
          :loading="eagerLoading ? UiImageLoading.EAGER : UiImageLoading.LAZY"
        />

        <UiImageCount
          v-if="gallery.count > 1"
          :count="gallery.count"
          :position="isMobile ? UiImageCountPosition.BOTTOM_RIGHT : UiImageCountPosition.BOTTOM_LEFT"
          :aria-label="$t('ui.organisms.ui_room_card.accessibility.gallery_button', { count: gallery.count })"
          class="Room-card__gallery"
          @ui-image-count::click="$emit('UiRoomCardGalleryButton::click')"
        />
      </div>

      <div class="Room-card__content">
        <UiAmenitiesCard
          class="Room-card__amenities-card"
          :title="title"
          :amenities="amenities"
          :button-label="$t('ui.organisms.ui_room_card.button.details_label')"
          @ui-amenities-card-button::click="$emit('UiRoomCardDetailsButton::click')"
        />
      </div>

      <UiDivider v-if="!isMobile" class="Room-card__divider" :direction="DividerDirection.VERTICAL" />

      <UiPricingSection
        :class="['Room-card__pricing-section', { 'Room-card__pricing-section--open': isOpen }]"
        v-bind="pricing"
      />
    </div>

    <div v-if="!isOpen" class="Room-card__action">
      <UiButton
        type="button"
        :text="$t('ui.organisms.ui_room_card.button.select_label')"
        :is-loading="isLoadingRateCards"
        @click="$emit('UiRoomCardSelectButton::click', productCode)"
      />
    </div>

    <template v-if="isMobile">
      <UiModal :is-open="isOpen" @ui-modal::close.self="$emit('UiRoomCardCloseButton::click')">
        <template #header>
          <UiModalHeader
            :caption="$t('ui.organisms.ui_room_card.modal.caption')"
            @ui-modal-header::click="$emit('UiRoomCardCloseButton::click')"
          />
        </template>

        <div class="Room-card__container">
          <div class="Room-card__image-container">
            <UiImage
              v-bind="image"
              :alt="
                image.src || image.srcSet?.medias.length
                  ? $t('ui.organisms.ui_room_card.accessibility.room_image', { room: title })
                  : undefined
              "
              :loading="eagerLoading ? UiImageLoading.EAGER : UiImageLoading.LAZY"
            />

            <UiImageCount
              :count="gallery.count"
              :aria-label="$t('ui.organisms.ui_room_card.accessibility.gallery_button', { count: gallery.count })"
              class="Room-card__gallery"
              @ui-image-count::click="$emit('UiRoomCardGalleryButton::click')"
            />
          </div>

          <div class="Room-card__content">
            <UiAmenitiesCard
              class="Room-card__amenities-card"
              :title="title"
              :amenities="amenities"
              :button-label="$t('ui.organisms.ui_room_card.button.details_label')"
              @ui-amenities-card-button::click="$emit('UiRoomCardDetailsButton::click')"
            />
          </div>
        </div>

        <div class="Room-card__open">
          <div class="Room-card__open-conten">
            <slot name="rate-cards" />
          </div>
        </div>
      </UiModal>
    </template>

    <template v-else>
      <div v-if="isOpen" class="Room-card__open">
        <div class="Room-card__open-content">
          <slot name="rate-cards" />
        </div>

        <div class="Room-card__action-open-content">
          <UiLink
            class="Room-card__close-button"
            type="button"
            :text="$t('ui.organisms.ui_room_card.button.close_button')"
            @click="$emit('UiRoomCardCloseButton::click')"
          >
            <template #append-content>
              <UiIcon class="Room-card__close-button--icon" name="chevronUp" />
            </template>
          </UiLink>
        </div>
      </div>
    </template>
  </article>
</template>

<style lang="scss" scoped>
@use "./UiRoomCard.scss";
</style>
