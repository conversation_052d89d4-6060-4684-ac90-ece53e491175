import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiRoomCard from "./UiRoomCard.vue"
import { ref } from "vue"
import { roomCardMockData } from "./mockData"

const meta = {
  component: UiRoomCard
} satisfies Meta<typeof UiRoomCard>

export default meta
type Story = StoryObj<typeof meta>

export const RoomCard: Story = {
  args: {
    ...roomCardMockData
  },
  render(args) {
    return {
      components: { UiRoomCard },
      setup() {
        const isOpen = ref<boolean>(false)
        const toggleRoomCard = () => {
          isOpen.value = !isOpen.value
        }
        return { args, isOpen, toggleRoomCard }
      },
      template: `
        <UiRoomCard v-bind="args" :is-open="isOpen" @ui-room-card-select-button::click="toggleRoomCard">
          <template #content>
            <h4>Custom Title in Open Variant</h4>
          </template>
        </UiRoomCard>
      `
    }
  }
}
