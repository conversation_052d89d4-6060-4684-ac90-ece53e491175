@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Enrollment-block {
  display: flex;
  flex-direction: column;
  max-width: 36rem;
  gap: map.get(spaces.$sizes, "7");

  &__already-member {
    margin-block-end: map.get(spaces.$sizes, "4");

    :deep(.ads-message__message) {
      @include text.lbf-text("body-02-strong");
    }

    :deep(.ads-message__links) {
      color: map.get(colors.$stratosBlue, "800");
    }
  }

  &_title {
    @include text.lbf-text("body-01-strong");

    color: map.get(colors.$stratosBlue, "800");

    &--thin {
      @include text.lbf-text("body-01");
    }
  }

  &_choices {
    border: 1px solid map.get(colors.$neutral, "200");
  }

  &_choice {
    padding-block: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");

    :deep(.ads-radio .ads-radio__label) {
      @include text.lbf-text("body-02");
      width: fit-content;

      span {
        @include text.lbf-text("body-02-strong");

        &.Enrollment-bloc_reduction {
          color: map.get(colors.$stratosBlue, "800");
        }
      }

      .Enrollment-block_terms-and-conditions {
        span {
          @include text.lbf-text("body-02");
        }

        a {
          text-decoration: underline;
        }
      }
    }

    &:first-child {
      border-bottom: 1px solid map.get(colors.$neutral, "200");
    }
  }
}
