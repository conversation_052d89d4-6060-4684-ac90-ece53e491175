<script setup lang="ts">
import UiRoomBlock from "../UiRoomBlock/UiRoomBlock.vue"
import type { UiRoomsBlockProps } from "./interface"
import { ref } from "vue"

defineEmits(["UiRoomsBlock::loadRoomModal", "UiRoomsBlock::loadRateModal"])
defineProps<UiRoomsBlockProps>()

const currentOpenBlock = ref()

const handleChangeOpenBlock = (roomId?: string) => {
  if (roomId === currentOpenBlock.value) {
    currentOpenBlock.value = null
  } else {
    currentOpenBlock.value = roomId
  }
}
</script>
<template>
  <div>
    <UiRoomBlock
      v-for="(room, index) in rooms"
      :key="`Room_block_${room.id}`"
      :room="room"
      :room-number="index + 1"
      :is-current-room="room.id === currentRoomId"
      :is-open="currentOpenBlock === room.id"
      @ui-room-block::load-rate-modal="$emit('UiRoomsBlock::loadRateModal', $event)"
      @ui-room-block::load-room-modal="$emit('UiRoomsBlock::loadRoomModal', $event)"
      @-ui-room-block::open-block="handleChangeOpenBlock($event)"
    />
  </div>
</template>
