import type { Meta, StoryObj } from "@storybook/vue3"
import { type Ref, ref } from "vue"
import UiDatePickerMobile from "./UiDatePickerMobile.vue"

const meta = {
  component: UiDatePickerMobile
} satisfies Meta<typeof UiDatePickerMobile>

export default meta
type Story = StoryObj<typeof meta>

export const MobileDatePicker: Story = {
  render: (args) => ({
    components: { UiDatePickerMobile },
    setup() {
      const dates: Ref<Date[]> = ref([])

      const handleUpdateDates = (newDates: Date[]) => {
        dates.value = newDates
      }

      const handleClearDate = () => {
        dates.value = []
      }

      return { args, dates, handleClearDate, handleUpdateDates }
    },
    template: `
      <UiDatePickerMobile v-bind="args" :dates=dates @update:dates="handleUpdateDates" @ui-date-picker-mobile::clear="handleClearDate" />
    `
  })
}
