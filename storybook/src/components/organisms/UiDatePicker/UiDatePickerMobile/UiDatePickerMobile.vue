<script setup lang="ts">
import VueDatePicker, { type RangeConfig } from "@vuepic/vue-datepicker"
import { computed, nextTick, ref, watch } from "vue"
import { DateMoment } from "../UiDatePickerDesktop/enums"
import UiButton from "../../../atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "../../../atoms/UiButton/enums"
import { type UiDatePickerMobileProps } from "./interface"
import UiIcon from "../../../atoms/UiIcon/UiIcon.vue"
import { useDatePicker } from "../../../../composables"
import { useI18n } from "vue-i18n"

const emit = defineEmits(["UiDatePickerMobile:changeStartDate", "UiDatePickerMobile:changeEndDate"])
const props = withDefaults(defineProps<UiDatePickerMobileProps>(), {
  defaultDateSelected: DateMoment.START,
  maxDate: 405,
  maxRange: 30
})

const dates = defineModel<Date[]>({
  default: () => [],
  set(val) {
    // If defined, return a sorted array (soonest date first)
    if (Array.isArray(val)) {
      const copy = Array.from(val)
      copy.sort((d1, d2) => d1.getTime() - d2.getTime())
      return copy
    }

    return val
  }
})

const { locale } = useI18n()
const { calendarHeaderFormat, computedDate, handleChangeDate, changeDateAfterInput } = useDatePicker()

const today = computed(() => new Date(new Date().setHours(0, 0, 0, 0)))
const maxDate = computed(() => {
  if (!Number.isFinite(props.maxDate) || props.maxDate < 0) return

  const maxDate = new Date()
  maxDate.setDate(maxDate.getDate() + props.maxDate)
  return maxDate
})

const startDate = ref<string | null>(computedDate(dates.value[0], true))
const endDate = ref<string | null>(computedDate(dates.value[1], true))
const currentFocused = ref<DateMoment | null>(props.defaultDateSelected)
const subComponentDates = ref(dates.value || [])

const rangeConfig = computed<RangeConfig | true>(() => {
  if (!props.maxRange && !props.minRange) return true

  return {
    maxRange: props.maxRange,
    minRange: props.minRange
  }
})

const formattedCalendarHeader = (month?: number, year?: number) => {
  if (month === undefined || year === undefined) return

  const date = new Date()
  date.setMonth(month)
  date.setFullYear(year)

  return date.toLocaleDateString(locale.value, calendarHeaderFormat)
}

const handleClick = (moment: DateMoment) => {
  currentFocused.value = moment
}

const handleRange = (date: Date) => {
  const res = handleChangeDate({
    currentFocused: currentFocused.value,
    date,
    dates: dates.value,
    endDate: endDate.value,
    maxRange: props.maxRange,
    startDate: startDate.value
  })

  startDate.value = res.startDate
  endDate.value = res.endDate
  currentFocused.value = res.currentFocused
  dates.value = res.dates
}

const handleInput = (date: DateMoment, event: Event) => {
  const res = changeDateAfterInput(date, event, maxDate.value, startDate.value, endDate.value, dates.value)

  if (!res) return
  ;(event.target as HTMLInputElement).blur()

  startDate.value = res.startDate
  endDate.value = res.endDate
  dates.value = res.dates
}

const clearDates = () => {
  startDate.value = null
  endDate.value = null
}

const disabledDates = (date: Date) => {
  if (date < today.value) return true
  if (maxDate.value && date > maxDate.value) return true
  if (
    currentFocused.value === DateMoment.END &&
    startDate.value &&
    date.getTime() > dates.value[DateMoment.START].getTime() + props.maxRange * 24 * 60 * 60 * 1000
  )
    return true
  return false
}

watch(
  () => startDate.value,
  () => {
    // "-" string fallback to make sure the param is passed to the validation fn (also matches the strig returned by the datepicker)
    emit("UiDatePickerMobile:changeStartDate", { endDate: endDate.value ?? "-", startDate: startDate.value ?? "-" })
  },
  { immediate: true }
)

watch(
  () => endDate.value,
  () => {
    // "-" string fallback to make sure the param is passed to the validation fn (also matches the strig returned by the datepicker)
    emit("UiDatePickerMobile:changeEndDate", { endDate: endDate.value ?? "-", startDate: startDate.value ?? "-" })
  },
  { immediate: true }
)

watch(
  dates,
  () => {
    nextTick(() => {
      subComponentDates.value = dates.value
    })
  },
  {
    deep: true
  }
)

defineExpose({
  clearDates
})
</script>

<template>
  <div class="Date-picker-mobile">
    <div class="Date-picker-mobile__dates">
      <input
        id="check_in"
        v-model="startDate"
        class="Date-picker-mobile__date"
        :class="{ 'Date-picker-mobile__date--active': currentFocused === DateMoment.START }"
        :aria-label="$t('ui.organisms.ui_date_picker_mobile.startDate')"
        :placeholder="$t('ui.organisms.ui_date_picker_desktop.example')"
        @click="handleClick(DateMoment.START)"
        @search="handleInput(DateMoment.START, $event)"
        @keypress.enter="handleInput(DateMoment.START, $event)"
      />

      <UiIcon class="Date-picker-mobile__arrow" name="arrow" />

      <input
        id="check_out"
        v-model="endDate"
        class="Date-picker-mobile__date"
        :class="{ 'Date-picker-mobile__date--active': currentFocused === DateMoment.END }"
        :aria-label="$t('ui.organisms.ui_date_picker_mobile.endDate')"
        :placeholder="$t('ui.organisms.ui_date_picker_desktop.example')"
        @click="handleClick(DateMoment.END)"
        @search="handleInput(DateMoment.END, $event)"
        @keypress.enter="handleInput(DateMoment.END, $event)"
      />
    </div>

    <VueDatePicker
      v-model="subComponentDates"
      auto-apply
      :clearable="false"
      :disabled-dates="disabledDates"
      :enable-time-picker="false"
      hide-offset-dates
      inline
      :locale="locale"
      :min-date="today"
      :max-date="maxDate"
      prevent-min-max-navigation
      :range="rangeConfig"
      :teleport="false"
      vertical
      @range-start="handleRange"
      @range-end="handleRange"
    >
      <template #month-year="{ month, year, handleMonthYearChange }">
        <div class="Date-picker-mobile__header">
          <UiButton
            class="Date-picker-mobile__previous"
            icon="chevronRight"
            text=""
            :variation="UiButtonVariation.PLAIN"
            @click="handleMonthYearChange?.(false)"
          />

          <p class="Date-picker-mobile__month-year">{{ formattedCalendarHeader(month, year) }}</p>

          <UiButton
            class="Date-picker-mobile__next"
            icon="chevronRight"
            text=""
            :variation="UiButtonVariation.PLAIN"
            @click="handleMonthYearChange?.(true)"
          />
        </div>
      </template>
    </VueDatePicker>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDatePickerMobile.scss";
</style>
