import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import { type Ref, ref } from "vue"
import UiDatePickerDesktop from "./UiDatePickerDesktop.vue"

const meta = {
  component: UiDatePickerDesktop
} satisfies Meta<typeof UiDatePickerDesktop>

export default meta
type Story = StoryObj<typeof meta>

export const DesktopDatePicker: Story = {
  render: (args) => ({
    components: { UiDatePickerDesktop },
    setup() {
      const dates: Ref<Date[]> = ref([])

      const handleUpdateDates = (newDates: Date[]) => {
        dates.value = newDates
      }

      const handleClearDate = () => {
        dates.value = []
      }

      return { args, dates, handleClearDate, handleUpdateDates }
    },
    template: `
      <UiDatePickerDesktop v-bind="args" :dates=dates @update:dates="handleUpdateDates" @ui-date-picker-desktop::clear="handleClearDate" />
    `
  })
}
