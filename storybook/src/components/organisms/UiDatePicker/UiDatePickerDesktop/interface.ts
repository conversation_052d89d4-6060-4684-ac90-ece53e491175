import type { DatePickerVariant } from "./enums"

export interface UiDatePickerDesktopProps {
  /**
   * Array of selected dates
   */
  dates?: Date[] | undefined
  /**
   * Sets the end date input status
   */
  endDateError?: boolean
  /**
   * Error message
   */
  errorMessage?: string
  /**
   * Number of days clickable from today
   */
  maxDate?: number
  /**
   * Maximum number of days for the range
   */
  maxRange?: number
  /**
   * Minimum number of days for the range
   */
  minRange?: number
  /**
   * Sets the start date input status
   */
  startDateError?: boolean
  /**
   * Variant of the date picker
   */
  variant?: DatePickerVariant
}
