<script setup lang="ts">
/**
 * This import allow to fix an issue of bad typings from the datepicker library
 * Do not remove it
 *
 * @see https://github.com/Vuepic/vue-datepicker/issues/1128
 */
import type {} from "date-fns"

import "@vuepic/vue-datepicker/dist/main.css"
import { DateMoment, DatePickerVariant } from "./enums"
import { type Ref, computed, nextTick, ref, useTemplateRef, watch } from "vue"
import VueDatePicker, { type RangeConfig } from "@vuepic/vue-datepicker"
import { addTrapFocusToElement, removeTrapFocusToElement } from "../../../../helpers/accessibility"
import UiButton from "../../../atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "../../../atoms/UiButton/enums"
import { type UiDatePickerDesktopProps } from "./interface"
import UiIcon from "../../../atoms/UiIcon/UiIcon.vue"
import UiMessage from "../../../molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "../../../molecules/UiMessage/enums"
import { onClickOutside } from "@vueuse/core"
import { useDatePicker } from "../../../../composables/useDatePicker"
import { useI18n } from "vue-i18n"

const emit = defineEmits([
  "update:dates",
  "UiDatePickerDesktop::clear",
  "UiDatePickerDesktop::close",
  "UiDatePickerDesktop::changeStartDate",
  "UiDatePickerDesktop::changeEndDate"
])
const props = withDefaults(defineProps<UiDatePickerDesktopProps>(), {
  dates: () => [],
  maxDate: 405,
  maxRange: 30,
  variant: DatePickerVariant.DEFAULT
})

const { locale, t } = useI18n()
const { calendarHeaderFormat, computedDate, dateFormat, handleChangeDate, changeDateAfterInput } = useDatePicker()

const today = ref(new Date(new Date().setHours(0, 0, 0, 0)))
const maxDate = ref(new Date(new Date().setDate(today.value.getDate() + props.maxDate)))
const startDate: Ref<string | null> = ref(computedDate(props.dates[DateMoment.START], true))
const endDate: Ref<string | null> = ref(computedDate(props.dates[DateMoment.END], true))
const currentFocused: Ref<DateMoment | null> = ref(null)
const datePickerDiv = useTemplateRef("desktop-date-picker")
const datePicker = useTemplateRef("datePicker")
const subComponentDates = ref(props.dates || [])

const dates = computed<Date[]>({
  get() {
    return props.dates || []
  },
  set(newDates: Date[]) {
    emit(
      "update:dates",
      newDates.sort((start, end) => start.getTime() - end.getTime())
    )
  }
})

const ariaLabels = {
  day: ({ value }: { value: Date }) => {
    const currentDate = value.toLocaleDateString(locale.value, dateFormat)
    const startDate = dates.value[DateMoment.START]?.toLocaleDateString(locale.value, dateFormat)
    const endDate = dates.value[DateMoment.END]?.toLocaleDateString(locale.value, dateFormat)

    if (Date.parse(startDate) <= Date.parse(currentDate) && Date.parse(currentDate) <= Date.parse(endDate)) {
      return t("ui.organisms.ui_date_picker_desktop.selected", { date: computedDate(value, true) })
    }

    return computedDate(value, true)
  },
  weekDay: ({ value }: { value: Date }) => value
}

const clearDates = () => {
  startDate.value = null
  endDate.value = null
  dates.value = []
  currentFocused.value = DateMoment.START

  emit("UiDatePickerDesktop::clear")
}

const disabledDates = (date: Date) => {
  if (date < today.value) return true
  if (date > maxDate.value) return true
  if (
    currentFocused.value === DateMoment.END &&
    startDate.value &&
    date.getTime() > dates.value[DateMoment.START]?.getTime() + props.maxRange * 24 * 60 * 60 * 1000
  )
    return true
  return false
}

const rangeConfig = computed<RangeConfig | true>(() => {
  if (!props.maxRange && !props.minRange) return true

  return {
    maxRange: props.maxRange,
    minRange: props.minRange
  }
})

const nightsCount = computed(() => {
  if (!dates.value?.[DateMoment.START] || !dates.value?.[DateMoment.END]) return 0

  const timeDiff = Math.abs(dates.value[DateMoment.START].getTime() - dates.value[DateMoment.END].getTime())
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
})

const formattedCalendarHeader = (month?: number, year?: number) => {
  if (month === undefined || year === undefined) return

  const date = new Date()
  date.setMonth(month)
  date.setFullYear(year)

  return date.toLocaleDateString(locale.value, calendarHeaderFormat)
}

const handleBlur = (date: DateMoment, event: Event) => {
  const res = changeDateAfterInput(date, event, maxDate.value, startDate.value, endDate.value, dates.value)

  if (!res) return

  startDate.value = res.startDate
  endDate.value = res.endDate
  dates.value = res.dates
}

const handleOpen = () => {
  nextTick(() => {
    const menu = datePicker.value?.getDpWrapMenuRef().value

    if (menu) {
      addTrapFocusToElement(menu)
    }
  })
}

const handleClose = () => {
  nextTick(() => {
    const menu = datePicker.value?.getDpWrapMenuRef().value

    if (menu) {
      removeTrapFocusToElement(menu)
    }

    emit("UiDatePickerDesktop::close")
  })
}

const handleClick = (moment: DateMoment) => {
  currentFocused.value = moment
}

const handleRange = (date: Date) => {
  const res = handleChangeDate({
    currentFocused: currentFocused.value,
    date,
    dates: dates.value,
    endDate: endDate.value,
    maxRange: props.maxRange,
    startDate: startDate.value
  })

  startDate.value = res.startDate
  endDate.value = res.endDate
  dates.value = res.dates
  currentFocused.value = res.currentFocused

  if (res.dates.length === 2) {
    currentFocused.value = null
    datePicker.value?.closeMenu()
  }
}

const handleFocus = (dateMoment: DateMoment, openMenu: () => void) => {
  openMenu()
  currentFocused.value = dateMoment
}

const openDatePickerDropdown = (moment: DateMoment) => {
  currentFocused.value = moment
  datePicker.value?.openMenu()
}

watch(
  () => startDate.value,
  () => {
    emit("UiDatePickerDesktop::changeStartDate", { endDate: endDate.value, startDate: startDate.value })
  },
  { immediate: true }
)

watch(
  () => endDate.value,
  () => {
    emit("UiDatePickerDesktop::changeEndDate", { endDate: endDate.value, startDate: startDate.value })
  },
  { immediate: true }
)

watch(
  () => props.dates,
  () => {
    nextTick(() => {
      subComponentDates.value = props.dates
    })
  },
  {
    deep: true
  }
)

watch(locale, () => {
  startDate.value = computedDate(props.dates[DateMoment.START], true)
  endDate.value = computedDate(props.dates[DateMoment.END], true)
})

defineExpose({
  openDatePickerDropdown
})

onClickOutside(datePickerDiv, () => (currentFocused.value = null))
</script>

<template>
  <div ref="desktop-date-picker" class="Date-picker-desktop">
    <VueDatePicker
      ref="datePicker"
      v-model="subComponentDates"
      :aria-labels="ariaLabels"
      auto-apply
      :clearable="false"
      :config="{
        closeOnAutoApply: false,
        keepActionRow: true
      }"
      :disabled-dates="disabledDates"
      :enable-time-picker="false"
      hide-offset-dates
      :max-date="maxDate"
      :min-date="today"
      month-name-format="long"
      multi-calendars
      position="left"
      prevent-min-max-navigation
      text-input
      auto-position="bottom"
      :range="rangeConfig"
      :teleport="false"
      @closed="handleClose"
      @open="handleOpen"
      @range-start="handleRange($event)"
      @range-end="handleRange($event)"
    >
      <template #menu-header>
        <div v-if="errorMessage" class="Date-picker-desktop__error">
          <UiIcon name="error" class="Date-picker-desktop__error-icon" />
          <p class="Date-picker-desktop__error-message">{{ errorMessage }}</p>
        </div>
      </template>
      <UiMessage v-if="errorMessage" :description="errorMessage" :variation="UiMessageVariation.DANGER" no-radius />
      <template #dp-input="{ openMenu }">
        <div
          class="Date-picker-desktop__inputs"
          :class="{ 'Date-picker-desktop__inputs--bordered': variant === DatePickerVariant.BORDERED }"
        >
          <div
            class="Date-picker-desktop__input"
            :class="{
              'Date-picker-desktop__input--focused': currentFocused === DateMoment.START,
              'Date-picker-desktop__input--bordered': variant === DatePickerVariant.BORDERED,
              'Date-picker-desktop__input--error': startDateError
            }"
            @click="handleClick(DateMoment.START)"
            @focusin="handleFocus(DateMoment.START, openMenu)"
          >
            <label for="Date-picker-desktop__check-in">{{ $t("ui.organisms.ui_date_picker_desktop.check_in") }}</label>

            <input
              id="Date-picker-desktop__check-in"
              v-model="startDate"
              :placeholder="$t('ui.organisms.ui_date_picker_desktop.example')"
              @blur="handleBlur(DateMoment.START, $event)"
              @keypress.enter.space.prevent="handleBlur(DateMoment.START, $event)"
            />
          </div>

          <UiIcon v-if="variant === DatePickerVariant.DEFAULT" class="Date-picker-desktop__arrow" name="arrow" />

          <div
            class="Date-picker-desktop__input"
            :class="{
              'Date-picker-desktop__input--focused': currentFocused === DateMoment.END,
              'Date-picker-desktop__input--bordered': variant === DatePickerVariant.BORDERED,
              'Date-picker-desktop__input--error': endDateError
            }"
            @click="handleClick(DateMoment.END)"
            @focusin="handleFocus(DateMoment.END, openMenu)"
          >
            <label for="Date-picker-desktop__check-out">
              {{ $t("ui.organisms.ui_date_picker_desktop.check_out") }}
            </label>

            <input
              id="Date-picker-desktop__check-out"
              v-model="endDate"
              :placeholder="$t('ui.organisms.ui_date_picker_desktop.example')"
              @blur="handleBlur(DateMoment.END, $event)"
              @keypress.enter.space.prevent="handleBlur(DateMoment.END, $event)"
            />
          </div>
        </div>
      </template>

      <template #month-year="{ month, year, handleMonthYearChange, instance }">
        <div class="Date-picker-desktop__header">
          <UiButton
            v-if="instance === 0"
            :aria-label="$t('ui.organisms.ui_date_picker_desktop.previous_month')"
            class="Date-picker-desktop__previous"
            icon="chevronRight"
            text=""
            type="button"
            :variation="UiButtonVariation.PLAIN"
            @click="handleMonthYearChange?.(false)"
          />

          <p class="Date-picker-desktop__month-year">{{ formattedCalendarHeader(month, year) }}</p>

          <UiButton
            v-if="instance === 1"
            :aria-label="$t('ui.organisms.ui_date_picker_desktop.next_month')"
            class="Date-picker-desktop__next"
            icon="chevronRight"
            text=""
            type="button"
            :variation="UiButtonVariation.PLAIN"
            @click="handleMonthYearChange?.(true)"
          />
        </div>
      </template>

      <template #action-row>
        <div class="Date-picker-desktop__action-row">
          <button
            :aria-label="$t('ui.organisms.ui_date_picker_desktop.clear_dates')"
            class="Date-picker-desktop__clear"
            :disabled="!dates[DateMoment.START]"
            type="button"
            @click="clearDates"
          >
            {{ $t("ui.organisms.ui_date_picker_desktop.clear") }}
          </button>

          <p v-if="nightsCount" class="Date-picker-desktop__nights">
            <span class="sr-only">
              {{ $t("ui.organisms.ui_date_picker_desktop.nights_selected", { count: nightsCount }) }}
            </span>

            {{ $t("ui.organisms.ui_date_picker_desktop.nights", { count: nightsCount }) }}
          </p>
        </div>
      </template>
    </VueDatePicker>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDatePickerDesktop.scss";
</style>
