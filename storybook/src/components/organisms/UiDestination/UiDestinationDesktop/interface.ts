import type { DestinationVariant } from "./enums"
import type { UiDestinationListItemType } from "../../../molecules/UiDestinationList/types"

export interface UiDestinationDesktopProps {
  /**
   * Wether or not the input has a red border
   */
  errorBorder?: boolean
  /**
   * Value for the error message
   */
  errorMessage?: string
  /**
   * Wether or not the skeletons have to be displayed for the hotels
   */
  isLoading?: boolean
  /**
   * List of items to display in the desktop destination selector
   */
  items: UiDestinationListItemType[]
  /**
   * ID of the currently selected destination (if any)
   */
  selectedValueId?: string
  /**
   * Display name of the currently selected destination (if any)
   */
  selectedValueName?: string
  /**
   * Variant of the destination selector (e.g., simple, advanced)
   */
  variant?: DestinationVariant
}
