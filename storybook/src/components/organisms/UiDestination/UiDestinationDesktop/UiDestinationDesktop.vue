<script setup lang="ts">
import { nextTick, ref, watch } from "vue"
import type { Destination } from "../../../atoms/UiDestinationListItem/interface"
import { DestinationVariant } from "./enums"
import { type UiDestinationDesktopProps } from "./interface"
import UiDestinationList from "../../../molecules/UiDestinationList/UiDestinationList.vue"
import UiDestinationListSkeleton from "../../../skeletons/UiDestinationListSkeleton/UiDestinationListSkeleton.vue"
import UiDropdownModal from "../../../molecules/UiDropdownModal/UiDropdownModal.vue"
import UiInputSearch from "../../../atoms/UiInputSearch/UiInputSearch.vue"
import UiMessage from "../../../molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "../../../molecules/UiMessage/enums"
import { focusFirstElement } from "../../../../helpers/accessibility"
import { getHotelFromDestinationList } from "../../../../helpers/getHotelFromKeyValue"
import { useDebounceFn } from "@vueuse/core"

const props = withDefaults(defineProps<UiDestinationDesktopProps>(), {
  variant: DestinationVariant.DEFAULT
})

const destination = defineModel<Destination>("destination")
const inputValue = defineModel<string>("inputValue")
const isOpen = defineModel<boolean>("is-open", { default: false })
const destinationDesktop = ref<HTMLElement>()
const destinationList = ref<HTMLElement>()
const selectedHotelId = ref<string>(destination.value?.id || "")

const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === "ArrowDown") {
    e.preventDefault()
    focusFirstElement(destinationList.value as HTMLElement)
  } else {
    selectHotelByName()
  }
}

const selectHotelById = (id: string) => {
  selectedHotelId.value = id
  const hotel = getHotelFromDestinationList(props.items, "id", id)

  inputValue.value = hotel?.name || ""
  closeDropdownModal()

  destination.value = hotel
}

// using debounced fn to avoid firing the helper every time the user fires a keydown event
const selectHotelByName = useDebounceFn(() => {
  const hotel = getHotelFromDestinationList(props.items, "name", inputValue.value ?? "")
  selectedHotelId.value = hotel?.id || ""

  destination.value = hotel
}, 300)

const handleInputSearchFocus = () => {
  if (!isOpen.value) {
    openDropdownModal()
  }

  nextTick(() => {
    setTimeout(() => {
      if (destinationDesktop.value) {
        focusFirstElement(destinationDesktop.value)
      }
    }, 10)
  })
}

const openDropdownModal = () => {
  isOpen.value = true
}

const closeDropdownModal = () => {
  isOpen.value = false
}

const handleEscape = () => {
  if (destinationDesktop.value) {
    focusFirstElement(destinationDesktop.value)
    closeDropdownModal()
  }
}

const handleTab = () => {
  closeDropdownModal()
  focusDatePicker()
}

const handleDestinationListSelect = (id: string) => {
  selectHotelById(id)
}

const focusDatePicker = () => {
  document.getElementById("Date-picker-desktop__check-in")?.focus()
}

watch(inputValue, (value) => {
  if (value && !selectedHotelId.value) {
    handleInputSearchFocus()
  }
})

defineExpose({ openDropdownModal })
</script>

<template>
  <div ref="destinationDesktop" class="Destination-desktop">
    <UiDropdownModal
      :is-open="isOpen"
      @ui-dropdown-modal::close="closeDropdownModal"
      @ui-dropdown-modal::escape="handleEscape"
      @ui-dropdown-modal::tab="handleTab"
    >
      <template #activator>
        <UiInputSearch
          v-model="inputValue"
          class="Destination-desktop__search-input"
          :class="{ 'Destination-desktop__search-input--bordered': variant === DestinationVariant.BORDERED }"
          :placeholder="$t('ui.organisms.ui_destination.placeholder')"
          unique-id="destination_desktop_search_id"
          :label="$t('ui.organisms.ui_destination.destination')"
          :error="errorBorder"
          @ui-input-search::keydown="handleKeydown($event)"
          @ui-input-search::clear="selectHotelByName()"
          @ui-input-search::focus="handleInputSearchFocus()"
        />
      </template>

      <template #content>
        <div ref="destinationList" aria-live="polite" :aria-busy="isLoading">
          <UiMessage v-if="errorMessage" :description="errorMessage" :variation="UiMessageVariation.DANGER" no-radius />
          <UiDestinationList
            v-if="!isLoading"
            :destinations="items"
            :selected-hotel-id="selectedHotelId"
            @ui-destination-list::select="handleDestinationListSelect($event)"
          />
          <UiDestinationListSkeleton v-else />
        </div>
      </template>
    </UiDropdownModal>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDestinationDesktop.scss";
</style>
