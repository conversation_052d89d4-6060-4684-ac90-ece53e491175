@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";

.Destination-desktop {
  :deep(.Dropdown-modal) {
    width: unset;
  }

  &__search-input {
    min-width: 11.9rem; // custom size based on figma
    max-width: 21.9rem; // custom size based on figma

    :deep(.Input-search__label) {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: max-content;
    }

    :deep(.Input-search__input) {
      width: 100%;
    }

    &--bordered {
      border: 1px solid map.get(colors.$neutral, "200");
      padding: map.get(spaces.$sizes, "6");
      gap: map.get(spaces.$sizes, "4");

      :deep(.Input-search__button) {
        bottom: 1.7rem;
      }
    }
  }
}
