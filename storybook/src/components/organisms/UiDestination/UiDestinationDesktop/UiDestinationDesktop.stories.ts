import type { Meta, StoryObj } from "@storybook/vue3"
import UiDestinationDesktop from "./UiDestinationDesktop.vue"
import type { UiDestinationListItemType } from "../../../molecules/UiDestinationList/types"
import { storyDataSetDesktop } from "../../UiDestination/storyDataSetDesktop"

const meta: Meta<typeof UiDestinationDesktop> = {
  args: {
    items: storyDataSetDesktop as UiDestinationListItemType[]
  },
  component: UiDestinationDesktop
} satisfies Meta<typeof UiDestinationDesktop>

export default meta

type Story = StoryObj<typeof meta>

export const DestinationDesktop: Story = {}
