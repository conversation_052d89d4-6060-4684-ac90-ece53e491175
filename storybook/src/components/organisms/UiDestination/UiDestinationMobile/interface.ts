import type { UiDestinationListDropdownItem } from "../../../molecules/UiDestinationListDropdown/types"

export interface UiDestinationMobileProps {
  /**
   * Error message to display
   */
  errorMessage?: string
  /**
   * Displays skeleton while true
   */
  isLoading?: boolean
  /**
   * List of dropdown items to display in the destination selector
   */
  items: UiDestinationListDropdownItem[]
  /**
   * ID of the currently selected destination (if any)
   */
  selectedValueId?: string
  /**
   * Display name of the currently selected destination (if any)
   */
  selectedValueName?: string
}
