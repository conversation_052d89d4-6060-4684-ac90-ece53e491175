import type { Meta, StoryObj } from "@storybook/vue3"
import type { UiDestinationListDropdownItem } from "../../../molecules/UiDestinationListDropdown/types"
import UiDestinationMobile from "./UiDestinationMobile.vue"
import { storyDataSetMobile } from "../storyDataSetMobile"

const meta: Meta<typeof UiDestinationMobile> = {
  args: {
    items: storyDataSetMobile as UiDestinationListDropdownItem[]
  },
  component: UiDestinationMobile
} satisfies Meta<typeof UiDestinationMobile>

export default meta

type Story = StoryObj<typeof meta>

export const DestinationMobile: Story = {}
