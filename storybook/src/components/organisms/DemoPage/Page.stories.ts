import type { Meta, StoryObj } from "@storybook/vue3"
import { expect, userEvent, within } from "@storybook/test"

import MyPage from "./Page.vue"

const meta = {
  component: MyPage,
  parameters: {
    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout
    layout: "fullscreen"
  },
  render: () => ({
    components: { MyPage },
    template: "<my-page />"
  }),
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  title: "Example/Page"
} satisfies Meta<typeof MyPage>

export default meta
type Story = StoryObj<typeof meta>

// More on interaction testing: https://storybook.js.org/docs/writing-tests/interaction-testing
export const LoggedIn: Story = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  play: async ({ canvasElement }: any) => {
    const canvas = within(canvasElement)
    const loginButton = canvas.getByRole("button", { name: /Log in/i })
    await expect(loginButton).toBeInTheDocument()
    await userEvent.click(loginButton)
    await expect(loginButton).not.toBeInTheDocument()

    const logoutButton = canvas.getByRole("button", { name: /Log out/i })
    await expect(logoutButton).toBeInTheDocument()
  }
}

export const LoggedOut: Story = {}
