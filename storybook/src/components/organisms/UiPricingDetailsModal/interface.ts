import type { UiPricingDetailsModalContentProps } from "../../molecules/UiPricingDetailsModalContent/interface"

export interface UiPricingDetailsModalProps {
  /**
   * Contains the content details for the room modal
   */
  content?: UiPricingDetailsModalContentProps
  /**
   * Determines if the modal is currently open
   */
  isOpen: boolean
  /**
   * Indicates whether the modal is currently loading data
   */
  isLoading: boolean
}
