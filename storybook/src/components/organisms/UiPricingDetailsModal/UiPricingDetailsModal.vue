<script setup lang="ts">
import UiModal from "../../molecules/UiModal/UiModal.vue"
import UiPricingDetailsModalContent from "../../molecules/UiPricingDetailsModalContent/UiPricingDetailsModalContent.vue"
import type { UiPricingDetailsModalProps } from "./interface"
import UiSplashScreen from "../../molecules/UiSplashScreen/UiSplashScreen.vue"

defineProps<UiPricingDetailsModalProps>()

defineEmits(["PricingDetailsModal::close"])
</script>

<template>
  <div class="Pricing-details-modal" aria-live="polite" :aria-busy="isLoading">
    <UiSplashScreen :is-visible="isLoading && isOpen" src="/booking/splash.svg" :alt="$t('global.brand_logo')" />
    <UiModal
      :is-open="isOpen && !isLoading"
      mobile-full-screen
      with-overlay
      @ui-modal::close="$emit('PricingDetailsModal::close')"
    >
      <UiPricingDetailsModalContent v-if="content" v-bind="content" :is-loading="isLoading" />
    </UiModal>
  </div>
</template>
