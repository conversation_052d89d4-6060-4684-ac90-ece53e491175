@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Footer {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  gap: map.get(spaces.$sizes, "10");
  padding-block-start: map.get(spaces.$sizes, "7");
  padding-block-end: map.get(spaces.$sizes, "10");
  padding-inline: map.get(spaces.$sizes, "6");
  background-color: map.get(colors.$caviarBlack, "900");
  color: map.get(colors.$basics, "white");

  @include mq.media(">=small") {
    padding: map.get(spaces.$sizes, "10") map.get(spaces.$sizes, "8");
  }

  @include mq.media(">=large") {
    padding: map.get(spaces.$sizes, "10") map.get(spaces.$sizes, "11");
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "10");

    @include mq.media(">=small") {
      flex-direction: row;

      gap: map.get(spaces.$sizes, "7");
    }
  }

  &__brand-logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    .Link {
      position: relative;

      :deep(.ads-link) {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }

  &__brand-logo {
    width: 19.6rem;
    height: 11.2rem;
    margin-block-end: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      width: 28.4rem;
      height: 13.6rem;
    }

    img {
      width: 100%;
    }
  }

  &__divider {
    @include mq.media(">=small") {
      display: none;
    }
  }

  &__help-section {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "5");

    @include mq.media(">=small") {
      margin: map.get(spaces.$sizes, "5") 0;
    }

    &--title {
      @include text.lbf-text("heading-03");
    }

    &--content {
      @include text.lbf-text("body-02");
    }

    &--link {
      :deep(.ads-link__content) {
        @include text.lbf-text("body-02");
        text-decoration: underline;
        color: map.get(colors.$basics, "white");
      }
    }
  }

  &__link-list-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: map.get(spaces.$sizes, "7");
  }

  &__link-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: map.get(spaces.$sizes, "6");

    :deep(.ads-link__content) {
      @include text.lbf-text("body-02");
      color: map.get(colors.$basics, "white");
    }
  }

  &__copyright {
    @include text.lbf-text("body-02");
    opacity: 0.8;
  }
}
