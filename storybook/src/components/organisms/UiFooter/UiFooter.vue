<script setup lang="ts">
import { DividerColor, DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiFooterProps } from "./interface"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"

defineProps<UiFooterProps>()
</script>

<template>
  <footer class="Footer">
    <div class="Footer__container">
      <div class="Footer__brand-logo-container">
        <UiLink :href="logoLink" :variant="UiLinkVariant.NEUTRAL" text="">
          <template #preprend-content>
            <img class="Footer__brand-logo" :src="logo" :alt="$t('ui.organisms.ui_footer.accessibility.alt')" />
          </template>
        </UiLink>

        <UiDivider
          class="Footer__divider"
          :color="DividerColor.CAVIAR_BLACK_600"
          :direction="DividerDirection.HORIZONTAL"
        />
      </div>

      <section class="Footer__help-section">
        <h2 class="Footer__help-section--title">{{ helpSection.title }}</h2>

        <div>
          <p class="Footer__help-section--content">{{ helpSection.mainContent }}</p>
          <p class="Footer__help-section--content">{{ helpSection.secondaryContent }}</p>
        </div>

        <UiLink class="Footer__help-section--link" v-bind="helpSection.link" />
      </section>
    </div>

    <UiDivider :color="DividerColor.CAVIAR_BLACK_600" :direction="DividerDirection.HORIZONTAL" />

    <nav class="Footer__link-list-container" :aria-label="$t('ui.organisms.ui_footer.accessibility.footer_label')">
      <ul class="Footer__link-list" :aria-label="$t('ui.organisms.ui_footer.accessibility.footer_link_list')">
        <li v-for="link in links" :key="link.text">
          <UiLink v-bind="link" />
        </li>
      </ul>

      <p class="Footer__copyright" :aria-label="$t('ui.organisms.ui_footer.accessibility.copyright')">
        {{ copyright }} {{ currentYear }}
      </p>
    </nav>
  </footer>
</template>

<style lang="scss" scoped>
@use "./UiFooter.scss";
</style>
