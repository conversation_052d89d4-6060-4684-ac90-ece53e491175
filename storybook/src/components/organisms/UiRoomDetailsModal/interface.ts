import type { UiRoomDetailsModalContentProps } from "../../molecules/UiRoomDetailsModalContent/interface"

export interface UiRoomDetailsModalProps {
  /**
   * Contains the content details for the room modal
   */
  roomDetailsModalContent?: UiRoomDetailsModalContentProps
  /**
   * Determines if the modal is currently open
   */
  isOpen: boolean
  /**
   * Indicates whether the modal is currently loading data
   */
  isLoading: boolean
}
