import type { Meta, StoryObj } from "@storybook/vue3"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiRoomDetailsModal from "./UiRoomDetailsModal.vue"
import { ref } from "vue"
import { uiRoomDetailsModalContentMockData } from "../../molecules/UiRoomDetailsModalContent/mockData"

const meta = {
  component: UiRoomDetailsModal
} satisfies Meta<typeof UiRoomDetailsModal>

export default meta

type Story = StoryObj<typeof meta>

export const RoomDetailsModal: Story = {
  render: () => ({
    components: { UiButton, UiRoomDetailsModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { isOpen, roomDetailsModalContent: uiRoomDetailsModalContentMockData }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiRoomDetailsModal
          :roomDetailsModalContent="roomDetailsModalContent"
          :isOpen="isOpen"
          @room-details-modal::close="isOpen = false"
        />
      </div>
    `
  })
}
