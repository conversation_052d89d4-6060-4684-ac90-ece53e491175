<script setup lang="ts">
import { SPECIAL_RATES } from "./constants"
import { SpecialRates } from "./enums"
import UiFieldset from "../../atoms/UiFieldset/UiFieldset.vue"
import UiInput from "../../atoms/UiInput/UiInput.vue"
import { UiRadioButtonDisplay } from "../../molecules/UiRadioGroup/enums"
import UiRadioGroup from "../../molecules/UiRadioGroup/UiRadioGroup.vue"
import { type UiSpecialRatesProps } from "./interface"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

withDefaults(defineProps<UiSpecialRatesProps>(), {
  isDropdown: false,
  rateSelected: SpecialRates.NONE
})

defineEmits(["SpecialRates::iataBlur"])

const { t } = useI18n()

const iataCode = defineModel<string>("iata-code", { default: "" })
const promoCode = defineModel<string>("promo-code")
const rateSelected = defineModel<SpecialRates>("rate-selected", {
  default: SpecialRates.NONE,
  set: (v) => (v ? v : SpecialRates.NONE)
})

const formatedRates = computed(() => {
  return SPECIAL_RATES.map((rate) => {
    return {
      label: t(rate.label),
      selectedValue: rate.selectedValue
    }
  })
})
</script>

<template>
  <div class="Special-rates__content" :class="[{ 'Section-rates--dropdown': isDropdown }]">
    <div class="Special-rates__items">
      <UiFieldset id="rate_items_fieldset" :label="$t('ui.organisms.ui_special_rates.rate_items.fieldset_label')">
        <template #fieldset-content>
          <UiRadioGroup
            v-model="rateSelected"
            name="rateItems"
            is-title-hidden
            :display="UiRadioButtonDisplay.ROW"
            :title="$t('ui.organisms.ui_special_rates.rate_items.fieldset_label')"
            :items="formatedRates"
          />

          <div class="mt-5">
            <UiInput
              v-if="rateSelected === SpecialRates.PROMO_CODE"
              v-model="promoCode"
              is-label-hidden
              :label="$t('ui.organisms.ui_special_rates.promo_code.label')"
              :placeholder="$t('ui.organisms.ui_special_rates.promo_code.placeholder')"
              :error-message="promoErrorMessage"
              :status="promoCodeStatus"
            />
          </div>
        </template>
      </UiFieldset>
    </div>

    <div class="Special-rates__iata">
      <UiFieldset id="iata_code_fieldset" :label="$t('ui.organisms.ui_special_rates.iata_code.fieldset_label')">
        <template #fieldset-content>
          <UiInput
            v-model="iataCode"
            :force-assistive="!isDropdown ? $t('ui.organisms.ui_special_rates.iata_code.assistive') : undefined"
            :label="$t('ui.organisms.ui_special_rates.iata_code.assistive')"
            :placeholder="$t('ui.organisms.ui_special_rates.iata_code.placeholder')"
            :error-message="iataErrorMessage"
            :status="iataCodeStatus"
            @ui-input::blur="$emit('SpecialRates::iataBlur')"
          />
        </template>
      </UiFieldset>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiSpecialRates.scss";
</style>
