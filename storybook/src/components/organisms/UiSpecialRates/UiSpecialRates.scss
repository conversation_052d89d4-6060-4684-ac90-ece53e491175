@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Special-rates__content {
  display: grid;
  gap: map.get(spaces.$sizes, "7");
  padding-inline: map.get(spaces.$sizes, "7");
  padding-block: map.get(spaces.$sizes, "8");

  @include mq.media(">=medium") {
    padding: 0;
    grid-template-columns: repeat(2, 1fr);
  }

  &.Section-rates--dropdown {
    display: inherit;
    width: 287px;
    grid-template-columns: 1fr;
    padding: map.get(spaces.$sizes, "6");

    .Special-rates__items {
      margin-bottom: map.get(spaces.$sizes, "7");
    }
  }

  @include mq.media("<large") {
    :deep(.Input) {
      input {
        font-size: 1.6rem;
      }
    }
  }
}

.Special-rates__items {
  @include mq.media(">medium") {
    grid-column: 1 / 5;
  }

  :deep(.ads-radio__label) {
    @include text.lbf-text("body-02");
  }
}

.Special-rates__iata {
  @include mq.media(">medium") {
    grid-column: 5 / 9;
  }

  @include mq.media(">large") {
    grid-column: 5 / 8;
  }
}
