import { SpecialRates, SpecialRatesCodes } from "./enums"

export const SPECIAL_RATES_MAPPING = {
  [SpecialRates.AAA_CCA_MEMBER]: SpecialRatesCodes.AAA_CCA_MEMBER.split(","),
  [SpecialRates.GOVERNMENT]: SpecialRatesCodes.GOVERNMENT.split(","),
  [SpecialRates.MILITARY_VETERAN]: SpecialRatesCodes.MILITARY_VETERAN,
  [SpecialRates.SENIOR_DISCOUNT]: SpecialRatesCodes.SENIOR_DISCOUNT,
  [SpecialRates.PROMO_CODE]: null,
  [SpecialRates.NONE]: null
}

export const SPECIAL_RATES = Object.entries(SpecialRates).map(([key, value]) => ({
  label: `ui.organisms.ui_special_rates.rate_items.items.${key}`,
  selectedValue: value
}))

export const CONST = {
  SPECIAL_RATES
}
