import { UiInputStatus } from "../../atoms/UiInput/enums"

export interface UiSpecialRatesProps {
  /**
   * Wether or not to display the iata error message as global or as input
   */
  globalIataError?: boolean
  /**
   * Iata code
   */
  iataCode?: string
  /**
   * Error message if the validation returns an error for iata code
   */
  iataErrorMessage?: string
  /**
   * Status of the iata code
   */
  iataCodeStatus?: UiInputStatus
  /**
   * Modal display
   */
  isDropdown?: boolean
  /**
   * Promo code
   */
  promoCode?: string
  /**
   *  Error message if the validation returns an error for promo code
   */
  promoErrorMessage?: string
  /**
   * Status of promo code
   */
  promoCodeStatus?: UiInputStatus
  /**
   * Pre-select rate
   */
  rateSelected: string | number | boolean
}
