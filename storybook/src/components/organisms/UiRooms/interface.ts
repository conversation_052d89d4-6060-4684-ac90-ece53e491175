import type { RoomType } from "../UiRoom/types"
import type { RoomsDirection } from "./enums"

export interface UiRoomsProps {
  /**
   * Direction of the rooms
   */
  direction?: RoomsDirection
  /**
   * Error message
   */
  errorMessage?: string
  /**
   * Number max of rooms
   */
  maxRooms?: number
  /**
   * Number max of people
   */
  maxPax?: number
  /**
   * Maximum number of adults
   */
  maxAdult?: number
  /**
   * Maximum number of child
   */
  maxChild?: number
  /**
   * Maximum age for children
   */
  maxChildAge?: number
  /**
   * List of rooms
   */
  rooms: RoomType[]
  /**
   * Rooms who have to display the error message
   */
  roomsAndGuestsInError?: number[]
}
