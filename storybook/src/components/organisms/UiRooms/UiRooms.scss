@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Rooms {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;

  &--vertical {
    padding-block-end: map.get(spaces.$sizes, "6");
  }

  &__list {
    display: flex;
    align-items: start;
    flex-wrap: wrap;
    gap: map.get(spaces.$sizes, "7");

    &--vertical {
      justify-content: center;
      flex-direction: column;
      gap: 0.8rem;
      width: 100%;
    }

    &--horizontal {
      display: grid;
      grid-template-columns: repeat(8, 1fr);

      @include mq.media(">=large") {
        grid-template-columns: repeat(9, 1fr);
      }
    }
  }

  &__add-room {
    &--horizontal {
      flex-basis: 100%;
      margin-block-start: map.get(spaces.$sizes, "8");

      :deep(.ads-button) {
        min-height: unset;
        gap: 0.4rem;
      }

      :deep(.Icon) {
        width: 2.4rem;
        height: 2.4rem;
      }
    }

    &--vertical {
      width: 100%;
      padding-inline: map.get(spaces.$sizes, "6");
      margin-block-start: map.get(spaces.$sizes, "4");

      :deep(.ads-button) {
        gap: 0.5rem;
        color: map.get(colors.$caviarBlack, "800");

        span {
          margin-block-start: map.get(spaces.$sizes, "2"); // Mandatory due to misalignment of the type...
        }
      }

      :deep(.Icon) {
        width: 1rem;
        height: 1rem;
      }
    }

    :deep(.ads-button) {
      @include text.lbf-text("body-01-uppercase");
      width: 100%;
    }

    :deep(.ads-button:disabled:not(.ads-button--loading)) {
      background-color: unset;
    }
  }
}
