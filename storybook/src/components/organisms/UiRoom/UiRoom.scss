@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room {
  min-height: 25.4rem;
  width: 100%;

  &--bordered {
    &:nth-child(odd) {
      grid-column: 1 / 5;
    }

    &:nth-child(even) {
      grid-column: 5 / 9;
    }

    border: 1px solid map.get(colors.$neutral, "200");
    border-radius: map.get(boxes.$radii, "soft");

    @include mq.media(">=large") {
      &:nth-child(3n + 1) {
        grid-column: 1 / 4;
      }

      &:nth-child(3n + 2) {
        grid-column: 4 / 7;
      }

      &:nth-child(3n + 3) {
        grid-column: 7 / 10;
      }
    }
  }

  &__title {
    @include text.lbf-text("label-01");
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-block: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "5");
    background-color: map.get(colors.$pearl<PERSON>rey, "200");

    :deep(.Icon) {
      font-size: 1.6rem;
    }
  }

  &__delete {
    display: flex;

    :deep(.ads-button) {
      min-height: initial;
      gap: unset;
    }
  }

  &__steppers {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");
    padding-block-start: map.get(spaces.$sizes, "7");
    padding-block-end: map.get(spaces.$sizes, "6");
  }

  &__accessibility {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__accessibility-section {
    @include text.lbf-text("body-01");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__accessibility-input {
    :deep(.ads-checkbox__label) {
      @include text.lbf-text("body-02");

      color: map.get(colors.$caviarBlack, "700");
    }

    &:deep(input[type="checkbox"]),
    &:deep(.ads-checkbox .ads-icon) {
      margin-top: 0;
      color: map.get(colors.$basics, "white");
    }
  }

  &__children-age {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: map.get(spaces.$sizes, "6");

    & > * {
      min-width: 0;
    }

    :deep(.ads-input__status-icon) {
      display: none;
    }
  }

  &__dropdown-error {
    :deep(.ads-input__input) {
      border-color: map.get(colors.$red, "500");
    }

    :deep(.ads-input__status-icon) {
      display: block;
    }
  }
}
