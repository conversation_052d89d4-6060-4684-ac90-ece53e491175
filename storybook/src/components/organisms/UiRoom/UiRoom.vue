<script setup lang="ts">
import { UiRoomSelectDefault, UiRoomSelectZero } from "../../organisms/UiRoom/constants"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "../../atoms/UiButton/enums"
import UiCheckbox from "../../atoms/UiCheckbox/UiCheckbox.vue"
import UiInputStepper from "../../molecules/UiInputStepper/UiInputStepper.vue"
import UiMessage from "../../molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "../../molecules/UiMessage/enums"
import type { UiRoomProps } from "./interface"
import UiSelect from "../../atoms/UiSelect/UiSelect.vue"
import { computed } from "vue"
import { useVModel } from "@vueuse/core"

const emit = defineEmits(["update:modelValue", "UiRoom::delete-room", "UiRoom::validate-children-age"])
const props = withDefaults(defineProps<UiRoomProps>(), {
  maxAdultsNumber: 6,
  maxChildrenAge: 16,
  maxChildrenNumber: 3,
  maxPax: 6
})

const modelValue = useVModel(props, "modelValue", emit)

const maxOccupancyLeft = computed(() => props.maxPax - modelValue.value.adults - modelValue.value.children)

const options = computed(() => {
  const tempOptions = [
    { label: "--", value: UiRoomSelectDefault },
    { label: "<1", value: UiRoomSelectZero }
  ]
  const maxOptions = [...Array(props.maxChildrenAge + 1).keys()].slice(1).map((age) => ({
    label: age.toString(),
    value: age
  }))
  return [...tempOptions, ...maxOptions]
})

const adults = computed({
  get() {
    return modelValue.value.adults
  },
  set(value) {
    if (maxOccupancyLeft.value <= 0 && value > modelValue.value.adults) return
    if (value > props.maxAdultsNumber) return

    modelValue.value = { ...modelValue.value, adults: value }
  }
})

const children = computed({
  get() {
    return modelValue.value.children
  },
  set(value) {
    if (maxOccupancyLeft.value <= 0 && value > modelValue.value.children) return
    if (value > props.maxChildrenNumber) return

    modelValue.value = { ...modelValue.value, children: value, childrenAges: [] }
  }
})

const updateChildrenAge = (index: number, age: number) => {
  const childrenAges = Array.from(modelValue.value.childrenAges)
  childrenAges.splice(index, 1, age)

  modelValue.value = { ...modelValue.value, childrenAges }
}

const isDropdownInError = (dropdownIndex: number) => {
  return props.dropdownsInError?.includes(dropdownIndex)
}
</script>

<template>
  <div class="Room" :class="{ 'Room--bordered': bordered }">
    <UiMessage
      v-if="displayErrorMessage"
      :description="errorMessage"
      :variation="UiMessageVariation.DANGER"
      no-radius
    />
    <div class="Room__title">
      <p>
        {{ $t("ui.organisms.ui_room.room", { roomNumber }) }}
      </p>

      <UiButton
        v-if="deletable"
        class="Room__delete"
        icon="trash"
        text=""
        :variation="UiButtonVariation.PLAIN"
        @click="$emit('UiRoom::delete-room')"
      />
    </div>
    <div class="Room__container">
      <div class="Room__steppers">
        <UiInputStepper
          v-model="adults"
          :label="$t('ui.organisms.ui_room.adults_label')"
          :min="1"
          :max="Math.min(modelValue.adults + maxOccupancyLeft, maxAdultsNumber)"
          :name="`Room-${roomNumber}-adults-number`"
        />

        <UiInputStepper
          v-model="children"
          :label="$t('ui.organisms.ui_room.children_label')"
          :min="0"
          :max="Math.min(modelValue.children + maxOccupancyLeft, maxChildrenNumber)"
          :name="`Room-${roomNumber}-children-number`"
          :sub-label="$t('ui.organisms.ui_room.children_sublabel', { age: maxChildrenAge })"
        />

        <div v-if="modelValue.children > 0" class="Room__children-age">
          <div
            v-for="(_childrenAge, index) in modelValue.childrenAges"
            :key="`Room-children-age-${index}`"
            :class="{ 'Room__dropdown-error': isDropdownInError(index) }"
          >
            <UiSelect
              :model-value="modelValue.childrenAges[index]"
              required
              :options="options"
              :default-selected="modelValue.childrenAges[index] ? undefined : -1"
              :label="$t('ui.organisms.ui_room.child_age', { number: index + 1 })"
              :error-message="isDropdownInError(index) ? errorMessage : undefined"
              placeholder="-"
              @update:model-value="updateChildrenAge(index, $event as number)"
              @ui-select::blur="$emit('UiRoom::validate-children-age', index)"
            />
          </div>
        </div>
      </div>
      <div class="Room__accessibility">
        <p class="Room__accessibility-section">{{ $t("ui.organisms.ui_room.accessibility_section") }}</p>
        <UiCheckbox
          :id="`Room-${roomNumber}-accessibility`"
          v-model="modelValue.accessibility"
          class="Room__accessibility-input"
          :label="$t('ui.organisms.ui_room.accessibility_label')"
          :name="`Room-${roomNumber}-accessibility`"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiRoom.scss";
</style>
