import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import Ui<PERSON>oom from "./UiRoom.vue"

const meta = {
  component: UiRoom
} satisfies Meta<typeof UiRoom>

export default meta
type Story = StoryObj<typeof meta>

export const Room: Story = {
  args: {
    modelValue: {
      accessibility: false,
      adults: 1,
      children: 0,
      childrenAges: [],
      id: 1
    },
    roomNumber: 1
  },
  render: (args) => ({
    components: { UiRoom },
    setup() {
      return { args }
    },
    template: `
    <div style="width: 300px">
      <UiRoom v-bind="args"  />
    </div>
    `
  })
}
