import type { RoomType } from "./types"

export interface UiRoomProps {
  /**
   * set border on the room card
   */
  bordered?: boolean
  /**
   * Set if the room is deletabled or not
   */
  deletable?: boolean
  /**
   * Displays the error message
   */
  displayErrorMessage?: boolean
  /**
   * Dropdowns in error
   */
  dropdownsInError?: number[]
  /**
   * Error message
   */
  errorMessage?: string
  /**
   * Maximum number of adults
   */
  maxAdultsNumber?: number
  /**
   * Maximum number of children
   */
  maxChildrenNumber?: number
  /**
   * Maximum age of children
   */
  maxChildrenAge?: number
  /**
   * Maximum number of people
   */
  maxPax?: number
  /**
   * Room object to be displayed
   */
  modelValue: RoomType
  /**
   * Number of the room
   */
  roomNumber: number
}
