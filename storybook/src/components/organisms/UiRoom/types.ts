import type { UiImageProps } from "../../atoms/UiImage/interface"
import type { UiPricingDetailsModalContentProps } from "../../molecules/UiPricingDetailsModalContent/interface"
import type { UiRoomDetailsModalContentProps } from "../../molecules/UiRoomDetailsModalContent/interface"

export type RoomType = {
  id: number
  accessibility: boolean
  adults: number
  children: number
  childrenAges: number[]
  hasErrorMessage?: boolean
  dropdownsInError?: number[]
  productCode?: string
  rateId?: string
  rateCode?: string
  classCode?: string
  offerDetails?: RoomOfferDetails
}

export interface RoomOfferDetails {
  rateModalContent?: UiPricingDetailsModalContentProps
  rateTitle?: string
  roomBedding?: string
  roomMedias?: UiImageProps[]
  roomModalContent?: UiRoomDetailsModalContentProps
  roomTitle?: string
}
