<script lang="ts" setup>
import { computed, ref, watch } from "vue"
import { DividerDirection } from "../../atoms/UiDivider/enums"
import { UiButtonType } from "../../atoms/UiButton/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiGallery from "../../molecules/UiGallery/UiGallery.vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import UiImageCount from "../../molecules/UiImageCount/UiImageCount.vue"
import { UiImageCountPosition } from "../../molecules"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import UiPricingDetailsModal from "../UiPricingDetailsModal/UiPricingDetailsModal.vue"
import type { UiRoomBlockProps } from "./interface"
import UiRoomDetailsModal from "../UiRoomDetailsModal/UiRoomDetailsModal.vue"
import { useI18n } from "vue-i18n"

const emit = defineEmits(["UiRoomBlock::loadRoomModal", "UiRoomBlock::loadRateModal", "UiRoomBlock::openBlock"])

const props = defineProps<UiRoomBlockProps>()

const { t } = useI18n()

const isGalleryOpen = ref(false)
const isRoomDetailsModalOpen = ref(false)
const isRateDetailsModalOpen = ref(false)
const isRoomDetailsModalLoading = ref(false)
const isRateDetailsModalLoading = ref(false)

const roomRepartition = computed(() => {
  return `${props.room.rateId ? t("ui.organisms.ui_room_block.room_for") : t("ui.organisms.ui_room_block.choose_room_for")} ${t("ui.organisms.ui_room_block.adult", { count: props.room.adults })}${props.room.children ? t("ui.organisms.ui_room_block.child", { count: props.room.children }) : ""}`
})

const handleRoomDetailsModal = () => {
  if (!props.room.offerDetails?.roomModalContent) {
    isRoomDetailsModalOpen.value = true
    isRoomDetailsModalLoading.value = true
    emit("UiRoomBlock::loadRoomModal", props.room.id)
  } else {
    isRoomDetailsModalOpen.value = true
  }
}

const handleRateDetailsModal = () => {
  if (!props.room.offerDetails?.rateModalContent) {
    isRateDetailsModalOpen.value = true
    isRateDetailsModalLoading.value = true
    emit("UiRoomBlock::loadRateModal", props.room.id)
  } else {
    isRateDetailsModalOpen.value = true
  }
}

watch(
  () => props.room.offerDetails?.roomModalContent,
  () => {
    if (props.room.offerDetails?.roomModalContent && isRoomDetailsModalLoading.value) {
      isRoomDetailsModalLoading.value = false
    }
  }
)

watch(
  () => props.room.offerDetails?.rateModalContent,
  () => {
    if (props.room.offerDetails?.rateModalContent && isRateDetailsModalLoading.value) {
      isRateDetailsModalLoading.value = false
    }
  }
)

watch(
  () => props.room.productCode,
  (newVal) => {
    if (!newVal) emit("UiRoomBlock::openBlock", null)
  }
)
</script>

<template>
  <div class="Room-block" :class="{ 'Room-block--open': isOpen }">
    <div
      class="Room-block__header"
      :class="{ 'Room-block__header--current': isCurrentRoom, 'Room-block__header--openable': room.rateId }"
      @click="room.rateId ? $emit('UiRoomBlock::openBlock', room.id) : null"
    >
      <div class="Room-block__titles">
        <p v-if="roomNumber" class="caption-01-strong color-caviarBlack-700">
          {{ $t("ui.organisms.ui_room_block.room_number", { number: roomNumber }) }}
        </p>

        <p class="body-02-strong color-caviarBlack-700">{{ roomRepartition }}</p>

        <p v-if="room.offerDetails?.rateTitle" class="caption-01 color-caviarBlack-500">
          {{ room.offerDetails?.rateTitle }}
        </p>
      </div>

      <UiIcon v-if="room.rateId" name="chevronDown" />
    </div>
    <div v-if="room.offerDetails" class="Room-block__content">
      <div>
        <div class="Room-block__gallery">
          <UiImage
            v-if="room.offerDetails?.roomMedias?.[0]"
            :src="room.offerDetails?.roomMedias?.[0].src"
            :src-set="room.offerDetails?.roomMedias?.[0].srcSet"
            :alt="room.offerDetails?.roomMedias?.[0].alt"
          />

          <UiImageCount
            v-if="room.offerDetails?.roomMedias?.length && room.offerDetails.roomMedias.length > 1"
            :count="room.offerDetails.roomMedias.length"
            :position="UiImageCountPosition.TOP_RIGHT"
            @ui-image-count::click="isGalleryOpen = true"
          />

          <UiGallery
            v-if="room.offerDetails?.roomMedias"
            :title="$t('components.stay_view.room_card.gallery')"
            :is-open="isGalleryOpen"
            :images="room.offerDetails?.roomMedias"
            @ui-gallery::close="isGalleryOpen = false"
          />
        </div>

        <div class="Room-block__details">
          <div class="Room-block__room">
            <div class="Room-block__room-header">
              <div class="Room-block__infos">
                <p class="body-02-strong color-caviarBlack-700">
                  {{ room.offerDetails?.roomTitle }}
                </p>

                <p v-if="room.offerDetails.roomBedding" class="caption-01 color-caviarBlack-700">
                  {{ room.offerDetails?.roomBedding }}
                </p>
              </div>

              <!-- <UiLink :text="$t('ui.organisms.ui_room_block.edit')" /> (The edit button is not dev in this ticket) -->
            </div>

            <UiLink
              :type="UiButtonType.BUTTON"
              class="Room-block__link"
              :text="$t('ui.organisms.ui_room_block.see_room_details')"
              @click="handleRoomDetailsModal"
            />

            <UiRoomDetailsModal
              :is-loading="isRoomDetailsModalLoading"
              :is-open="isRoomDetailsModalOpen"
              :room-details-modal-content="room.offerDetails?.roomModalContent"
              @room-details-modal::close="isRoomDetailsModalOpen = false"
            />
          </div>

          <UiDivider :direction="DividerDirection.HORIZONTAL" />

          <div class="Room-block__rate">
            <p class="caption-01-strong color-caviarBlack-700">{{ $t("ui.organisms.ui_room_block.rate") }}</p>

            <p class="body-02 color-caviarBlack-700">{{ room.offerDetails?.rateTitle }}</p>

            <UiLink
              :type="UiButtonType.BUTTON"
              class="Room-block__link"
              :text="$t('ui.organisms.ui_room_block.rate_description')"
              @click="handleRateDetailsModal"
            />

            <UiPricingDetailsModal
              :is-loading="isRateDetailsModalLoading"
              :is-open="isRateDetailsModalOpen"
              :content="room.offerDetails?.rateModalContent"
              @pricing-details-modal::close="isRateDetailsModalOpen = false"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiRoomBlock.scss";
</style>
