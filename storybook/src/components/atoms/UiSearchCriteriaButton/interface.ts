import type { UiSearchCriteriaButtonVariant } from "./enums"

export interface UiSearchCriteriaButtonProps {
  /**
   * First line of the button
   */
  criteriaName: string
  /**
   * Second line of the button
   */
  criteriaValue?: string
  /**
   * Error status
   */
  error?: boolean
  /**
   * Set if there is arrow or not
   */
  hasArrow?: boolean
  /**
   * Defines if the button is "focused" or not
   */
  isCriteriaDropdownModalOpen?: boolean
  /**
   * Set the criteria value's color to disable
   */
  isCriteriaValueEmpty?: boolean
  /**
   * But<PERSON>'s variant
   */
  variant?: UiSearchCriteriaButtonVariant
}
