@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Search-criteria-button {
  display: flex;
  flex-direction: column;
  min-width: 15.9rem;
  border: 1px solid transparent;
  text-align: left;
  padding-block: map.get(spaces.$sizes, "4");
  padding-inline: map.get(spaces.$sizes, "6");
  gap: map.get(spaces.$sizes, "2");

  &:focus,
  &--focused {
    border: 1px solid colors.$absoluteBlack;
    background-color: map.get(colors.$pearlGrey, "200");
  }

  &__infos {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "2");
  }

  &__name {
    @include text.lbf-text("caption-01");
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &__value {
    @include text.lbf-text("caption-01-strong");
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    &--empty {
      font-weight: 400; // can't set .caption-01 since the order in the config file make the strong override the regular
      color: map.get(colors.$caviarBlack, "500");
    }
  }

  &--bordered {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border: 1px solid map.get(colors.$neutral, "200");
    padding-block: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "6");

    @include mq.media(">=large") {
      padding-block: map.get(spaces.$sizes, "6");
    }

    &:focus,
    &--focused {
      border: 1px solid map.get(colors.$neutral, "200");
      background-color: transparent;
    }

    .Search-criteria-button__infos {
      display: flex;
      flex-direction: column;
      gap: map.get(spaces.$sizes, "6");
    }

    .Search-criteria-button__name {
      @include text.lbf-text("caption-01-strong");

      text-transform: uppercase;
    }

    .Search-criteria-button__value {
      @include text.lbf-text("body-02");

      &--empty {
        color: map.get(colors.$caviarBlack, "500");
      }
    }

    :deep(.Icon) {
      font-size: 1.6rem;
    }
  }

  &--error {
    border: 1px solid map.get(colors.$red, "500");
  }
}
