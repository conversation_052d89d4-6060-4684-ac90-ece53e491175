<script setup lang="ts">
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiSearchCriteriaButtonProps } from "./interface"
import { UiSearchCriteriaButtonVariant } from "./enums"

withDefaults(defineProps<UiSearchCriteriaButtonProps>(), {
  hasArrow: false,
  variant: UiSearchCriteriaButtonVariant.DEFAULT
})

defineEmits(["UiSearchCriteriaButton::click"])
</script>

<template>
  <button
    class="Search-criteria-button"
    :class="[
      { 'Search-criteria-button--focused': isCriteriaDropdownModalOpen },
      { 'Search-criteria-button--bordered': variant === UiSearchCriteriaButtonVariant.BORDERED },
      { 'Search-criteria-button--error': error }
    ]"
    type="button"
    @click="$emit('UiSearchCriteriaButton::click')"
  >
    <div class="Search-criteria-button__infos">
      <p class="Search-criteria-button__name">
        {{ criteriaName }}
      </p>
      <p
        class="Search-criteria-button__value"
        :class="{
          'Search-criteria-button__value--empty': isCriteriaValueEmpty
        }"
      >
        <slot name="criteria-value">
          {{ criteriaValue }}
        </slot>
      </p>
    </div>
    <UiIcon v-if="hasArrow" name="chevronRight" />
  </button>
</template>

<style lang="scss" scoped>
@use "./UiSearchCriteriaButton.scss";
</style>
