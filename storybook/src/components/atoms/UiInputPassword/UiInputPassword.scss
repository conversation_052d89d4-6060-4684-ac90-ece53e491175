@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Input-password {
  :deep(.ads-input__input) {
    input {
      @include text.lbf-text("body-02");

      &::placeholder {
        @include text.lbf-text("body-02");
      }

      .ads-input--default & {
        padding: 1.4rem 0;
      }

      .ads-input--small & {
        padding: 1.2rem 0;
      }

      .ads-input--large & {
        padding: 1.6rem 0;
      }

      .ads-input--disabled & {
        color: map.get(colors.$caviarBlack, "700");
      }
    }
  }
}

:deep(.ads-input__label) {
  @include text.lbf-text("body-02");

  color: map.get(colors.$caviarBlack, "700");
}

:deep(.ads-input__assistive) {
  @include text.lbf-text("caption-01");

  color: map.get(colors.$caviarBlack, "500");
}

:deep(.ads-input__label-wrapper) {
  margin-block-end: map.get(spaces.$sizes, "4");
}

:deep(.ads-input__label-optional) {
  color: map.get(colors.$caviarBlack, "500");
}

:deep(.ads-input__input) {
  border-radius: 2px;

  border-color: map.get(colors.$caviarBlack, "200");
  background: map.get(colors.$pearlGrey, "200");

  &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
    border-color: map.get(colors.$caviarBlack, "700");
  }

  .ads-input--disabled & {
    background-color: map.get(colors.$neutral, "200");
  }

  .ads-input--success & {
    border-color: map.get(colors.$green, "500");

    &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
      border-color: map.get(colors.$green, "500");
    }
  }

  .ads-input--error & {
    border-color: map.get(colors.$red, "500");

    &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
      border-color: map.get(colors.$red, "500");
    }
  }
}

:deep(.ads-error-message) {
  margin-block-start: map.get(spaces.$sizes, "4");
  @include text.lbf-text("caption-01");
}

:deep(.ads-input__append-inner-icon) {
  flex-direction: row-reverse;
}
