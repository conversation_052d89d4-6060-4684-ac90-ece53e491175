import type { Meta, StoryObj } from "@storybook/vue3"

import UiDestinationListItem from "./UiDestinationListItem.vue"

const meta = {
  args: {
    city: "City",
    country: "Country",
    expediaCompliant: false,
    id: "1",
    name: "Hotel name",
    selected: false,
    selectionId: "1",
    state: "State"
  },
  component: UiDestinationListItem
} satisfies Meta<typeof UiDestinationListItem>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}
