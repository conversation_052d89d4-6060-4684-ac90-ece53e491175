@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Destination-list-item {
  cursor: pointer;
  transition: background-color ease-out 0.1s;

  @include mq.media(">=medium") {
    display: flex;
    flex-direction: column-reverse;
    padding: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "5");
    border-bottom: 1px solid map.get(colors.$neutral, "200");
  }

  &:hover,
  &:focus {
    background-color: map.get(colors.$pearlGrey, "200");
  }
}

.Destination-list-item__name {
  @include text.lbf-text("body-01");

  color: map.get(colors.$caviarBlack, "700");

  @include mq.media(">=medium") {
    font-size: 1.4rem;
  }
}

.Destination-list-item__address {
  @include text.lbf-text("body-02");

  color: map.get(colors.$caviarBlack, "500");

  @include mq.media(">=medium") {
    font-size: 1.4rem;
  }
}

.Destination-list-item__city {
  @include mq.media(">=medium") {
    color: map.get(colors.$caviarBlack, "700");
    text-transform: uppercase;
  }
}
