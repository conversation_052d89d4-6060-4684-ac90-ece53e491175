export interface UiDestinationListItemProps {
  /**
   * City
   */
  city?: string
  /**
   * Country
   */
  country: string
  /**
   * Name of the hotel
   */
  name: string
  /**
   * State
   */
  state?: string
  /**
   * Unique id
   */
  id: string
  /**
   * Unique id for selection
   */
  selectionId: string
  /**
   * Selects the element
   */
  selected?: boolean
  /**
   * is expedia compliant
   */
  expediaCompliant: boolean
}

export type UiDestinationKey = keyof UiDestinationListItemProps

export type Destination = {
  city?: string
  country: string
  expediaCompliant: boolean
  name: string
  state?: string
  id: string
  selected?: boolean
}
