@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Select {
  // Label and assistive
  :deep(.ads-input__label) {
    @include text.lbf-text("body-02");

    color: map.get(colors.$caviarBlack, "700");
  }

  :deep(.ads-input__assistive) {
    @include text.lbf-text("caption-01");
  }

  :deep(.ads-input__label-wrapper) {
    margin-block-end: map.get(spaces.$sizes, "4");
  }

  :deep(.ads-select__select) {
    width: auto;
  }

  // Status
  .ads-select--success {
    :deep(.ads-input__input) {
      border-color: map.get(colors.$green, "500");

      &:focus-within::after {
        border-color: map.get(colors.$green, "500");
      }
    }
  }

  .ads-select--error {
    :deep(.ads-input__input) {
      border-color: map.get(colors.$red, "500");

      &:focus-within::after {
        border-radius: 2px;
        border-color: map.get(colors.$red, "500");
      }
    }
  }

  // Sizes
  :deep(.ads-input--default) {
    .ads-select__select {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }
  }

  :deep(.ads-input--small) {
    .ads-select__select {
      padding-top: 0.6rem;
      padding-bottom: 0.6rem;
    }
  }

  :deep(.ads-input--large) {
    .ads-select__select {
      padding-top: 1.4rem;
      padding-bottom: 1.4rem;
    }
  }

  // Input
  :deep(.ads-input__input) {
    gap: map.get(spaces.$sizes, "4");
    background-color: map.get(colors.$pearlGrey, "200");
    border-color: map.get(colors.$caviarBlack, "200");

    &:focus-within::after {
      border-radius: 2px;
      border: map.get(boxes.$borders, "interactiveSelected");
    }
  }

  // Select
  :deep(.ads-select__select) {
    @include text.lbf-text("body-01");
    padding-inline-start: map.get(spaces.$sizes, "6");

    color: map.get(colors.$caviarBlack, "700");
    max-width: 100%;

    option {
      @include text.lbf-text("body-02");

      color: map.get(colors.$caviarBlack, "700");
    }

    &:hover {
      cursor: pointer;
    }
  }

  // Prepend icon
  :deep(.Select__prepend-icon-scoped) {
    .ads-select__select {
      padding-inline-start: map.get(spaces.$sizes, "10");
    }
  }

  :deep(.ads-input__prepend-inner-icon) {
    position: absolute;
    gap: 8px;
    padding-inline-start: map.get(spaces.$sizes, "6");
    font-size: 2.4rem;
  }

  // Inner icon
  :deep(.ads-input__append-inner-icon) {
    flex-direction: row-reverse;
    padding-inline-end: map.get(spaces.$sizes, "6");

    .ads-input__status-icon {
      font-size: 1.8rem;
    }
  }

  // Error message
  :deep(.ads-error-message) {
    @include text.lbf-text("caption-01");
    margin-block-start: map.get(spaces.$sizes, "4");
  }

  // Disabled
  :deep(.ads-input--disabled) {
    .ads-select__select {
      opacity: 1;
      color: map.get(colors.$caviarBlack, "700");
    }

    .ads-icon-chevron-down {
      color: map.get(colors.$caviarBlack, "700");
    }

    .ads-input__input {
      background-color: map.get(colors.$neutral, "200");
    }
  }
}
