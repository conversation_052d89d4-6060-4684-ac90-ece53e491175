import type { <PERSON>a, StoryObj } from "@storybook/vue3"

import { UiSelectSize, UiSelectStatus } from "./enums"
import UiIcon from "../UiIcon/UiIcon.vue"
import UiSelect from "./UiSelect.vue"

const meta = {
  argTypes: {
    size: {
      control: { type: "select" },
      options: Object.values(UiSelectSize)
    },
    status: {
      control: { type: "select" },
      options: Object.values(UiSelectStatus)
    }
  },
  args: {
    ...UiSelect.args,
    label: "Select label",
    options: [
      { label: "France", value: 3 },
      { label: "Germany", value: 2 },
      { label: "Spain", value: 1 }
    ],
    placeholder: "placeholder-text"
  },
  component: UiSelect
} satisfies Meta<typeof UiSelect>

export default meta
type Story = StoryObj<typeof meta>

export const Select: Story = {}

export const SelectWithIcon: Story = {
  render: (args) => ({
    components: { UiIcon, UiSelect },
    setup() {
      return { args }
    },
    template: `
      <div>
        <UiSelect v-bind="args">
          <template #prepend-inner-icon>
            <ui-icon name="usFlag"></ui-icon>
          </template>
        </UiSelect>
      </div>
    `
  })
}
