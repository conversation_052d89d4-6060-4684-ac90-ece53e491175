@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/boxes";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.ads-checkbox {
  display: flex;
  align-items: flex-start;
  position: relative;
  gap: map.get(spaces.$sizes, "4");

  :deep(.ads-icon) {
    margin-top: 0.3rem;
    position: absolute;
    top: 0;
    left: 0;
  }

  :deep(input[type="checkbox"]) {
    margin-top: 0.3rem;
    border: 0.1rem solid map.get(colors.$neutral, "500");
    flex-shrink: 0;
    color: map.get(colors.$basics, "white");
    border-radius: map.get(boxes.$radii, "soft");

    &:checked {
      background-color: map.get(colors.$caviarBlack, "700");
      border: none;
    }

    &:focus-visible {
      outline: 0.2rem solid map.get(colors.$basics, "black");
      outline-offset: 0.2rem;
    }

    &:hover {
      border: 0.1rem solid map.get(colors.$neutral, "600");
    }

    &:disabled {
      border: 0.2rem solid map.get(colors.$neutral, "200");
      background-color: map.get(colors.$neutral, "100");

      &:checked {
        border: none;
        background-color: map.get(colors.$neutral, "200");
      }
    }
  }
}

.Checkbox {
  .ads-checkbox {
    &.ads-checkbox--disabled {
      :deep(.ads-icon) {
        color: map.get(colors.$basics, "white");
      }
    }
  }
}

.ads-checkbox--error {
  :deep(input[type="checkbox"]) {
    outline-offset: 0.2rem;
  }
}

:deep(.ads-checkbox__label) {
  @include text.lbf-text("body-01-strong");

  color: map.get(colors.$caviarBlack, "700");
}

:deep(.ads-checkbox__description) {
  @include text.lbf-text("body-01");
  color: map.get(colors.$caviarBlack, "700");
}

:deep(.ads-error-message) {
  @include text.lbf-text("caption-01");
}
