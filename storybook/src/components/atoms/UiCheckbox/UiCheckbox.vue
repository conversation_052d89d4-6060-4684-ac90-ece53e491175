<script setup lang="ts">
import { type UiCheckboxEventValue, type UiCheckboxProps } from "./interface"
import { AdsCheckbox } from "@accor/ads-components"

const props = withDefaults(defineProps<UiCheckboxProps>(), { selectedValue: true })
const emit = defineEmits(["UiCheckbox::change"])

const model = defineModel<UiCheckboxProps["selectedValue"]>()

const handleEvent = (value: UiCheckboxEventValue) => {
  emit("UiCheckbox::change", value)
  model.value = value.isChecked ? value.value : undefined
}
</script>

<template>
  <div class="Checkbox">
    <AdsCheckbox v-model="model" v-bind="props" :aria-checked="undefined" @change="handleEvent" />
  </div>
</template>

<style lang="scss" scoped>
@use "./UiCheckbox.scss";
</style>
