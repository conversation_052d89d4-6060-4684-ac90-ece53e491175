import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3"
import UiCheckbox from "./UiCheckbox.vue"
import { ref } from "vue"

const meta = {
  component: UiCheckbox
} satisfies Meta<typeof UiCheckbox>

export default meta
type Story = StoryObj<typeof meta>

export const Checkbox: Story = {
  args: {
    id: "checkbox_input_id",
    name: "checkbox-input-name"
  },
  render: (args) => ({
    components: { UiCheckbox },
    setup() {
      const value = ref<boolean>(false)

      return { args, value }
    },
    template: `
      <form>
        <UiCheckbox v-model="value" v-bind="args" />
      </form>
    `
  })
}

export const WithLabel: Story = {
  args: {
    ...Checkbox.args,
    label: "Checkbox label"
  },
  render: Checkbox.render
}

export const WithDescription: Story = {
  args: {
    ...Checkbox.args,
    description: "Checkbox description"
  },
  render: Checkbox.render
}

export const WithLabelAndDescription: Story = {
  args: {
    ...Checkbox.args,
    description: "Checkbox description",
    label: "Checkbox label"
  },
  render: Checkbox.render
}

export const Disabled: Story = {
  args: {
    ...Checkbox.args,
    disabled: true
  },
  render: Checkbox.render
}

export const WithError: Story = {
  args: {
    ...Checkbox.args,
    errorMessage: "Error message",
    label: "Checkbox label"
  },
  render: Checkbox.render
}
