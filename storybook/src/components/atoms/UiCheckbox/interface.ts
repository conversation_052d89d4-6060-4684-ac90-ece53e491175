export interface UiCheckboxProps {
  /**
   * Input field aria-checked
   */
  ariaChecked?: boolean
  /**
   * Input field aria-describedby
   */
  ariaDescribedby?: string
  /**
   * Input field aria-disabled
   */
  ariaDisabled?: boolean
  /**
   * Input field aria-labelledby
   */
  ariaLabelledby?: string
  /**
   * Input field description
   */
  description?: string
  /**
   * Input field disabled state
   */
  disabled?: boolean
  /**
   * Input field error message
   */
  errorMessage?: string
  /**
   * Input unique id
   */
  id: string
  /**
   * Input field label
   */
  label?: string
  /**
   * Input field form name
   */
  name: string
  /**
   * Input field modelValue
   */
  selectedValue?: string | boolean | number | object | (string | boolean | number | object)[] | undefined
}

export interface UiCheckboxEventValue {
  value: UiCheckboxProps["selectedValue"]
  isChecked: boolean
}
