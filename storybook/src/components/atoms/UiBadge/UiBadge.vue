<script setup lang="ts">
import { UiBadgeSize, UiBadgeVariant } from "./enums"
import { type UiBadgeProps } from "./interface"

withDefaults(defineProps<UiBadgeProps>(), {
  size: UiBadgeSize.DEFAULT,
  variant: UiBadgeVariant.DEFAULT
})
</script>

<template>
  <div class="Badge" :class="[`Badge--color-${variant}`, `Badge--size-${size}`]">
    <span class="Badge__label">{{ label }}</span>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiBadge.scss";
</style>
