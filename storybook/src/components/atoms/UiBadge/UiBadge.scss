@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 32px;
  width: auto;

  @include text.lbf-text("caption-01-uppercase");

  &--color-loyalty {
    background-color: map.get(colors.$velvet, "100");
    color: map.get(colors.$stratosBlue, "800");
  }

  &--color-default {
    background-color: map.get(colors.$neutral, "200");
    color: map.get(colors.$caviarBlack, "800");
  }

  &--size-default {
    padding: map.get(spaces.$sizes, "3") map.get(spaces.$sizes, "6");
  }
}
