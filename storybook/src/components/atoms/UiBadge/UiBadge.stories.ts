import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3"
import { UiBadgeSize, UiBadgeVariant } from "./enums"
import UiBadge from "./UiBadge.vue"

const meta: Meta<typeof UiBadge> = {
  argTypes: {
    size: {
      control: { type: "select" },
      options: Object.values(UiBadgeSize)
    },
    variant: {
      control: { type: "select" },
      options: Object.values(UiBadgeVariant)
    }
  },
  args: {
    label: "Member discount applied"
  },
  component: UiBadge
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const Loyalty: Story = {
  args: {
    variant: UiBadgeVariant.LOYALTY
  }
}
