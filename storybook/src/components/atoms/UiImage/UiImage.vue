<script setup lang="ts">
import { UiImageDisplayMode, UiImageFetchPriority, UiImageLoading, UiImagefallBackImage } from "./enums"
import { computed, ref } from "vue"
import { type UiImageMedia } from "./types"
import { type UiImageProps } from "./interface"
import { generateSrcSet } from "../../../helpers/srcSet"
import logo from "../../../assets/logos/fallBackLogo.svg?url"
import logoWithBackground from "../../../assets/logos/fallBackLogoWithBackground.svg?url"
import { useI18n } from "vue-i18n"

const props = withDefaults(defineProps<UiImageProps>(), {
  displaymode: "",
  fallBackImage: UiImagefallBackImage.DEFAULT,
  fetchPriority: UiImageFetchPriority.AUTO,
  height: 260, // this is the usual smallest size that will be loaded on mobiles.
  loading: UiImageLoading.LAZY,
  width: 346 // this is the usual smallest size that will be loaded on mobiles.
})

const { t } = useI18n()

const activeImageSrc = ref(props.src)

const activeAlt = ref(props.alt)

const handleError = () => {
  if (logo) {
    activeImageSrc.value = props.fallBackImage === UiImagefallBackImage.DEFAULT ? logo : logoWithBackground
  } else {
    activeImageSrc.value = ""
    activeAlt.value = t("ui.atoms.ui_image.missing")
  }
}

const generatedSrcSet = props.srcSet?.medias ? generateSrcSet(props.srcSet as UiImageMedia) : undefined

// this is the defaut value that tells the browser:
// "when you use an image, it’s going to be the entire width of the viewport".
const generatedSizes = "100vw"

const adaptiveSizes = computed(() => {
  switch (props.displayMode) {
    case UiImageDisplayMode.SMALL:
      return "(max-width: 480px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 400px"

    default:
      return generatedSizes
  }
})
</script>

<template>
  <picture class="Image">
    <source v-if="generatedSrcSet && adaptiveSizes" :srcset="generatedSrcSet" :sizes="adaptiveSizes" />
    <img
      class="Image__img"
      :alt="activeAlt"
      :fetchpriority="fetchPriority"
      :height="height"
      :loading="loading"
      :src="activeImageSrc"
      :width="width"
      @error="handleError"
    />
  </picture>
</template>

<style lang="scss" scoped>
@use "./UiImage.scss";
</style>
