import type { UiImageDisplayMode, UiImageFetchPriority, UiImageLoading, UiImagefallBackImage } from "./enums"
import type { UiImageMedia } from "./types"

export interface UiImageProps {
  /**
   * Alternate text for accessibility.
   */
  alt?: string
  /**
   * Mode of display for the image.
   */
  displayMode?: UiImageDisplayMode
  /**
   * The fallback image type, either "DEFAULT" or "WITH_BACKGROUND".
   */
  fallBackImage?: UiImagefallBackImage
  /**
   * Defines the priority of image fetching.
   */
  fetchPriority?: UiImageFetchPriority
  /**
   * Image height
   */
  height?: number
  /**
   * Loading attribute
   */
  loading?: UiImageLoading
  /**
   * Enables preloading of the image
   */
  preload?: boolean
  /**
   * Sizes attribute for responsive images
   */
  sizes?: string
  /**
   * Source set for responsive image switching.
   */
  srcSet?: UiImageMedia
  /**
   * The URL of the main image.
   */
  src?: string
  /**
   * Image width
   */
  width?: number
}
