<script setup lang="ts">
import { AdsSkeleton } from "@accor/ads-components"
import type { UiSkeletonProps } from "../../atoms/UiSkeleton/interface"

withDefaults(defineProps<UiSkeletonProps>(), {
  hasAnimation: true,
  height: "10rem",
  width: "100%"
})
</script>

<template>
  <div class="Skeleton" :class="{ 'Skeleton--no-animation': !hasAnimation }">
    <AdsSkeleton />
  </div>
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/colors";

.Skeleton {
  line-height: 0;

  :deep(.ads-skeleton) {
    height: v-bind("height");
    width: v-bind("width");
    border-radius: v-bind("borderRadius");
    background: linear-gradient(
        -70deg,
        map.get(colors.$customs, "skeleton-1") 0%,
        map.get(colors.$customs, "skeleton-1") 40%,
        map.get(colors.$customs, "skeleton-2") 50%,
        map.get(colors.$customs, "skeleton-1") 60%,
        map.get(colors.$customs, "skeleton-1") 100%
      )
      repeat;
    background-size: 400% 100%;
    animation: sliding-background 1.7s ease infinite;
  }

  &--no-animation {
    :deep(.ads-skeleton) {
      animation: none;
    }
  }
}
</style>
