@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.List-item {
  @include text.lbf-text("body-01");
  display: flex;
  gap: map.get(spaces.$sizes, "6");

  &__icon {
    flex-shrink: 0;

    font-size: map.get(text.$sizes, "xl");
    color: map.get(colors.$caviarBlack, "800");
  }

  &__text {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }

  &__subtitle {
    color: map.get(colors.$caviarBlack, "500");
  }
}
