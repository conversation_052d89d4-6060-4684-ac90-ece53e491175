<script setup lang="ts">
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiListItemProps } from "./interface"

defineProps<UiListItemProps>()
</script>

<template>
  <div class="List-item">
    <UiIcon class="List-item__icon" name="listIcon" />

    <div class="List-item__text">
      <p class="List-item__title">
        {{ title }}
      </p>

      <p v-if="subtitle" class="List-item__subtitle">
        {{ subtitle }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiListItem.scss";
</style>
