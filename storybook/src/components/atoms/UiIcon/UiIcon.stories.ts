import type { Meta, StoryObj } from "@storybook/vue3"

import UiIcon from "./UiIcon.vue"
import { iconMapping } from "../../../assets/icons"

const meta = {
  argTypes: {
    name: {
      control: "select",
      options: Object.keys(iconMapping)
    }
  },
  component: UiIcon,
  decorators: [() => ({ template: '<div style="font-size: 5rem;"><story/></div>' })],
  parameters: {
    layout: "centered"
  }
} satisfies Meta<typeof UiIcon>

export default meta
type Story = StoryObj<typeof meta>

export const Icon: Story = {
  args: {
    name: "close"
  }
}
