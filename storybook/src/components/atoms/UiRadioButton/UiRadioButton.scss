@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";

.Radio-button,
.Radio-group {
  :deep(.ads-radio) {
    cursor: pointer;
    gap: map.get(spaces.$sizes, "4");

    .ads-radio__input {
      cursor: pointer;

      padding: 3px 0;
      border: 1px solid map.get(colors.$caviarBlack, "500");

      &:hover {
        border: 1px solid map.get(colors.$neutral, "600");
      }

      &:focus-visible {
        outline: 2px solid map.get(colors.$basics, "black");
      }

      &:checked {
        border: 1px solid map.get(colors.$caviarBlack, "700");
      }

      &::before {
        box-shadow: inset 1em 1em map.get(colors.$basics, "black");
      }
    }

    .ads-radio__label {
      @include text.lbf-text("body-01-strong");

      cursor: pointer;

      color: map.get(colors.$caviarBlack, "700");
    }

    .ads-radio__description {
      @include text.lbf-text("body-01");
    }
  }
}
