import type { Meta, StoryObj } from "@storybook/vue3"
import UiRadioButton from "./UiRadioButton.vue"

const meta: Meta<typeof UiRadioButton> = {
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The label for the radio button"
    },
    selectedValue: {
      control: { type: "text" },
      description: "The value selected for the radio button"
    }
  },
  args: {
    label: "",
    selectedValue: "0"
  },
  component: UiRadioButton
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const WithCustomLabel: Story = {
  args: {
    label: "Custom Radio Label"
  }
}
