@use "sass:map";
@use "@sb-config/spaces";

.Loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: map.get(spaces.$sizes, "2");

  &__dot {
    width: 0.6rem;
    height: 0.6rem;
    border-radius: 50%;
    opacity: 0.3;
    animation: blink 1s infinite ease-in-out;
  }
}

@keyframes blink {
  0%,
  80%,
  100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}

@for $i from 0 through 3 {
  .Loading-dots__dot:nth-child(3n + #{$i}) {
    animation-delay: #{$i * 0.2}s;
  }
}
