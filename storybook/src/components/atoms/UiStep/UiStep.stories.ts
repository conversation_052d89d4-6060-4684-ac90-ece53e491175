import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiStep from "./UiStep.vue"

const meta: Meta<typeof UiStep> = {
  args: {
    href: "https://www.google.fr/",
    isCompleted: false,
    isCurrent: true,
    step: 1,
    text: "My link"
  },
  component: UiStep
}

export default meta

type Story = StoryObj<typeof meta>

export const Step: Story = {
  args: {
    isCurrent: true
  }
}
