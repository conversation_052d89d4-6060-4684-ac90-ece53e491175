@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Step {
  @include text.lbf-text("caption-01");
  cursor: pointer;

  .Link {
    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    :deep(.ads-link) {
      @include text.lbf-text("caption-01");
      text-transform: uppercase;

      color: map.get(colors.$caviarBlack, "500");
    }
  }

  &--current,
  &--completed {
    .Link {
      :deep(.ads-link) {
        @include text.lbf-text("caption-01-strong");

        color: map.get(colors.$caviarBlack, "700");
      }
    }
  }

  &__number {
    @include text.lbf-text("caption-01");
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 1.8rem;
    border: 0.1rem solid map.get(colors.$caviarBlack, "500");
    color: map.get(colors.$caviarBlack, "500");
    border-radius: map.get(boxes.$radii, "rounded");

    &--current,
    &--completed {
      border-color: colors.$absoluteBlack;
      background-color: colors.$absoluteBlack;
      color: map.get(colors.$porcelainWhite, "100");
    }
  }
}
