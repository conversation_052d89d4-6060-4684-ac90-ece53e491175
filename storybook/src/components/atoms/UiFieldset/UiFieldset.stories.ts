import type { Meta, StoryObj } from "@storybook/vue3"
import UiFieldset from "./UiFieldset.vue"
import UiRadioGroup from "../../molecules/UiRadioGroup/UiRadioGroup.vue"

const meta: Meta<typeof UiFieldset> = {
  args: {
    label: "Fieldset"
  },
  component: UiFieldset
}

export default meta

type Story = StoryObj<typeof meta>

export const Fieldset: Story = {
  args: {
    label: "Fieldset"
  },
  render: (args) => ({
    components: { UiFieldset, UiRadioGroup },
    setup() {
      return { args }
    },
    template: `
      <UiFieldset v-bind="args">
        <template #fieldset-content>
          <UiRadioGroup 
            is-title-hidden
            modelValue="1"
            title="radioGroup"
            name="radioGroup"
            :items="[
               { label: 'Label 1', selectedValue: 1 },
                { label: 'Label 2', selectedValue: 2 },
                { label: 'Label 3', selectedValue: 3 }
            ]"
          />
        </template>
      </UiFieldset>
    `
  })
}
