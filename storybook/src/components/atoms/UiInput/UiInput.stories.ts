import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import { UiInputSize, UiInputStatus } from "./enums"
import { ref, watch } from "vue"
import UiInput from "./UiInput.vue"

const meta = {
  argTypes: {
    size: {
      control: { type: "select" },
      options: Object.values(UiInputSize)
    },
    status: {
      control: { type: "select" },
      options: Object.values(UiInputStatus)
    }
  },
  args: {
    label: "Label",
    ...UiInput.args
  },
  component: UiInput
} satisfies Meta<typeof UiInput>

export default meta
type Story = StoryObj<typeof meta>

export const UiInputComplete: Story = {
  args: {
    modelValue: ""
  },
  render: (args) => ({
    components: { UiInput },
    setup() {
      // modelValue is a props created by defineModel, see doc at: https://vuejs.org/guide/components/v-model.html#under-the-hood
      const model = ref<string>(args.modelValue as string)

      watch(
        () => args?.modelValue,
        (val) => {
          model.value = val as string
        }
      )

      return { args, model }
    },
    template: `
      <div>
        <UiInput v-bind="args" v-model="model" />
      </div>
    `
  })
}
