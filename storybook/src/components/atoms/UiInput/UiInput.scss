@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Input {
  position: relative;
  font-size: 2.8rem;

  &--hidden-label {
    :deep(.ads-input__label-wrapper) {
      // sr-only
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }
  }

  :deep(.ads-input__input) {
    input {
      @include text.lbf-text("body-02");

      padding: 1.2rem 0;

      &::placeholder {
        @include text.lbf-text("body-02");
      }

      .ads-input--small & {
        padding: 1.2rem 0;
      }

      .ads-input--large & {
        padding: 1.6rem 0;
      }

      .ads-input--disabled & {
        color: map.get(colors.$caviarBlack, "700");
      }
    }
  }
}

.Input__button {
  font-size: 2.4rem;
  line-height: 0;

  padding-inline-end: map.get(spaces.$sizes, "3");
  color: map.get(colors.$caviarBlack, "800");
}

:deep(.ads-input__label) {
  @include text.lbf-text("body-02");

  color: map.get(colors.$caviarBlack, "700");
}

:deep(.ads-input__assistive) {
  @include text.lbf-text("caption-01");

  color: map.get(colors.$caviarBlack, "500");
}

:deep(.ads-input__label-wrapper) {
  margin-block-end: map.get(spaces.$sizes, "4");
}

:deep(.ads-input__label-optional) {
  color: map.get(colors.$caviarBlack, "500");
}

:deep(.ads-input__input) {
  border-color: map.get(colors.$caviarBlack, "200");
  background-color: map.get(colors.$pearlGrey, "200");

  &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
    border-color: map.get(colors.$caviarBlack, "700");
  }

  .ads-input--disabled & {
    background-color: map.get(colors.$neutral, "200");
  }

  .ads-input--success & {
    border-color: map.get(colors.$green, "500");

    &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
      border-color: map.get(colors.$green, "500");
    }
  }

  .ads-input--error & {
    border-color: map.get(colors.$red, "500");

    &:focus-within:not(:has(.ads-input__password-toggle:focus-visible))::after {
      border-color: map.get(colors.$red, "500");
    }
  }
}

:deep(.ads-input__prepend-inner-icon) {
  padding-inline: 1rem 0;
}

:deep(.ads-error-message) {
  margin-block-start: map.get(spaces.$sizes, "4");
  @include text.lbf-text("caption-01");
}
