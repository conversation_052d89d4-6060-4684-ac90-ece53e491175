import type { UiButtonSize, UiButtonTarget, UiButtonType, UiButtonVariation } from "./enums"
import type { Icon } from "../../../assets/icons"

export interface UiButtonProps {
  /**
   * But<PERSON>'s aria label
   */
  ariaLabel?: string
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * External link URL
   */
  href?: string
  /**
   * icon prop
   */
  icon?: Icon
  /**
   * Loading state enabled
   */
  isLoading?: boolean
  /**
   * Size prop
   */
  size?: UiButtonSize
  /**
   * Target prop
   */
  target?: UiButtonTarget
  /**
   * But<PERSON> text
   */
  text: string
  /**
   * Internal link URL
   */
  to?: string
  /**
   * Component type
   */
  type?: UiButtonType | string
  /**
   * Variation prop
   */
  variation?: UiButtonVariation
}
