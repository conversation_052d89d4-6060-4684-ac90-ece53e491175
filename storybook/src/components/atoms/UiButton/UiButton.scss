@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/fonts";
@use "@sb-config/text";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Button .ads-button {
  @include text.lbf-text("body-01-uppercase");

  width: 100%;
  min-height: 48px;
  padding: 1.2rem 2.4rem;
  border: 0;
  border-radius: 2px;

  &--size-large {
    min-height: 56px;
    padding: 1.6rem 2.4rem;
  }

  &--size-small {
    min-height: 40px;
    padding: 0.8rem 2.4rem;
  }

  &--plain {
    background-color: transparent;
    color: inherit;
    box-shadow: none;
    border: none;
    padding: 0;
  }

  // Primary settings
  &--primary {
    color: map.get(colors.$basics, "white");
    background-color: map.get(colors.$caviarBlack, "900");

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "700");
      }
    }

    // Disable hover for touch device
    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$caviarBlack, "800");
          color: map.get(colors.$basics, "white");
        }
      }
    }
  }

  // Secondary settings
  &--secondary {
    color: map.get(colors.$basics, "white");
    background-color: map.get(colors.$caviarBlack, "800");

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "600");
      }
    }

    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$caviarBlack, "700");
          color: map.get(colors.$white, "90");
        }
      }
    }
  }

  // Tertiary settings
  &--tertiary {
    outline: 1px solid map.get(colors.$caviarBlack, "800");

    &:not(:active, :disabled) {
      color: map.get(colors.$caviarBlack, "800");
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "700");
      }
    }

    &.ads-button--loading {
      color: map.get(colors.$caviarBlack, "800");
      background-color: unset;
    }

    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$caviarBlack, "800");
          color: map.get(colors.$white, "90");
        }
      }
    }
  }

  // Destructive primary
  &--destructive-primary {
    background-color: map.get(colors.$red, "500");

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "700");
      }
    }

    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$red, "600");
          color: map.get(colors.$white, "90");
        }
      }
    }
  }

  // Destructive secondary
  &--destructive-secondary {
    background-color: map.get(colors.$basics, "white");

    &:not(:hover, :active, :disabled) {
      outline: 1px solid map.get(colors.$red, "500");
      color: map.get(colors.$red, "500");
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$red, "600");
      }
    }

    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$red, "500");
          color: map.get(colors.$white, "90");
        }
      }
    }
  }

  //Disabled
  &:disabled {
    &:not(.ads-button--loading) {
      background-color: map.get(colors.$neutral, "200");
      color: map.get(colors.$caviarBlack, "500");
    }
  }

  //Loading
  &--loading {
    pointer-events: auto;
    cursor: not-allowed;
  }

  // Focus
  &:focus-visible::after {
    border-radius: 2px;
    border: map.get(boxes.$borders, "interactiveSelected");
  }

  // Loyalty
  &--loyalty {
    color: map.get(colors.$basics, "white");
    background-color: map.get(colors.$stratosBlue, "800");

    &:hover {
      &:not(:disabled, :active) {
        background-color: map.get(colors.$stratosBlue, "700");
      }
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$stratosBlue, "600");
      }
    }
  }

  // Loyalty secondary
  &--loyalty-secondary {
    outline: 1px solid map.get(colors.$stratosBlue, "800");

    &:not(:active, :disabled) {
      color: map.get(colors.$stratosBlue, "800");
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$stratosBlue, "800");
      }
    }

    &:hover {
      &:not(:disabled, :active) {
        background-color: map.get(colors.$stratosBlue, "800");
        color: map.get(colors.$white, "90");
      }
    }

    &.ads-button--loading {
      color: map.get(colors.$stratosBlue, "800");

      :deep(circle) {
        stroke: currentColor;
      }
    }
  }

  // Workaround for browser who don't support focus-visible
  @supports not selector(:focus-visible) {
    &:focus {
      border-radius: 2px;
      outline: 2px solid map.get(colors.$basics, "black");
      outline-offset: 2px;
    }
  }
}
