import type { <PERSON>a, <PERSON>Obj } from "@storybook/vue3"
import { UiButtonSize, UiButtonVariation } from "./enums"
import UiButton from "./UiButton.vue"

const meta = {
  argTypes: {
    size: {
      control: { type: "select" },
      options: Object.values(UiButtonSize)
    },
    variation: {
      control: { type: "select" },
      options: Object.values(UiButtonVariation)
    }
  },
  args: {
    text: "Button Label"
  },
  component: UiButton
} satisfies Meta<typeof UiButton>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}

export const secondary: Story = {
  args: {
    variation: UiButtonVariation.SECONDARY
  }
}

export const tertiary: Story = {
  args: {
    variation: UiButtonVariation.TERTIARY
  }
}

export const destructive: Story = {
  args: {
    variation: UiButtonVariation.DESTRUCTIVE
  }
}

export const destructive_secondary: Story = {
  args: {
    variation: UiButtonVariation.DESTRUCTIVE_SECONDARY
  }
}

export const internalLink: Story = {
  args: {
    to: "/"
  }
}

export const externalLink: Story = {
  args: {
    href: "https://www.google.com"
  }
}

export const disabled: Story = {
  args: {
    disabled: true
  }
}

export const loading: Story = {
  args: {
    isLoading: true,
    text: ""
  }
}

export const loyalty: Story = {
  args: {
    variation: UiButtonVariation.LOYALTY
  }
}

export const loyaltySecondary: Story = {
  args: {
    variation: UiButtonVariation.LOYALTY_SECONDARY
  }
}
