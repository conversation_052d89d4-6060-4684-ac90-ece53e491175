export enum UiButtonSize {
  DEFAULT = "default",
  LARGE = "large",
  SMALL = "small"
}

export enum UiButtonVariation {
  DESTRUCTIVE = "destructive",
  DESTRUCTIVE_SECONDARY = "destructive-secondary",
  LOYALTY = "loyalty",
  LOYALTY_SECONDARY = "loyalty-secondary",
  PLAIN = "plain",
  PRIMARY = "primary",
  SECONDARY = "secondary",
  TERTIARY = "tertiary"
}

export enum UiButtonType {
  BUTTON = "button",
  SUBMIT = "submit"
}

export enum UiButtonTarget {
  BLANK = "_blank",
  SELF = "_self"
}
