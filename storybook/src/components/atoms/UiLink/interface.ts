import type { UiButtonType } from "../UiButton/enums"
import type { UiLinkVariant } from "./enums"

export interface UiLinkProps {
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * External link URL
   */
  href?: string
  /**
   * Link target attribute
   */
  target?: string
  /**
   * Button text
   */
  text: string
  /**
   * Internal link URL
   */
  to?: string
  /**
   * Component type
   */
  type?: UiButtonType | string
  /**
   * If external link
   */
  external?: boolean
  /**
   * Uppercase or not
   */
  uppercase?: boolean
  /**
   * Variant
   */
  variant?: UiLinkVariant
}
