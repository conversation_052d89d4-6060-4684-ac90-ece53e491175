@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.ads-link {
  @include text.lbf-text("body-01-underline");

  &:hover,
  &:focus {
    opacity: 0.8;
  }
}

.Link {
  &--default {
    .ads-link {
      color: map.get(colors.$loyaltyBlack, "900");
    }
  }

  &--text {
    .ads-link {
      color: map.get(colors.$loyaltyBlack, "900");

      &:hover,
      &:focus {
        color: map.get(colors.$caviarBlack, "800");
      }
    }
  }

  &--specific {
    .ads-link {
      color: map.get(colors.$caviarBlack, "500");

      &:hover,
      &:focus {
        color: map.get(colors.$loyaltyBlack, "900");
      }
    }
  }

  &--neutral {
    .ads-link {
      @include text.lbf-text("caption-01");

      color: map.get(colors.$loyaltyBlack, "900");
      text-decoration: none;
    }
  }

  &--strong-hover {
    .ads-link {
      @include text.lbf-text("body-01");

      color: map.get(colors.$caviarBlack, "900");

      &:hover,
      &:focus {
        @include text.lbf-text("body-01-strong");
      }
    }
  }

  &--uppercase {
    text-transform: uppercase;
  }
}
