<script setup lang="ts">
import { computed, resolveComponent } from "vue"
import { AdsLink } from "@accor/ads-components"
import { type UiLinkProps } from "./interface"
import { UiLinkVariant } from "./enums"

const props = withDefaults(defineProps<UiLinkProps>(), {
  variant: UiLinkVariant.DEFAULT
})

const routerLink = resolveComponent("router-link")

const computedTag = computed(() => {
  if (props.to) return routerLink
  if (props.href) return "a"
  return "button"
})

const isLink = computed(() => computedTag.value === "a")
</script>

<template>
  <div class="Link" :class="[`Link--${variant}`, { 'Link--uppercase': uppercase }]">
    <slot name="preprend-content" />

    <!-- We remove specific from the v-bind to be sure they don't appear inside the dom -->
    <AdsLink
      v-bind="{ disabled, href, target, text, to, type, external, variant: isLink ? variant : undefined }"
      :as="computedTag"
    />

    <slot name="append-content" />
  </div>
</template>

<style lang="scss" scoped>
@use "./UiLink.scss";
</style>
