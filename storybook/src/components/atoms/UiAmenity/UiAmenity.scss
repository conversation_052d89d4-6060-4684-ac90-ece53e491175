@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-config/fonts";
@use "@sb-config/text";

.Amenity {
  @include text.lbf-text("body-01");

  display: flex;
  flex-shrink: 0;
  align-items: flex-start;
  gap: map.get(spaces.$sizes, "6");

  &.Amenity--cancellation-policy {
    gap: map.get(spaces.$sizes, "4");
  }

  &__icon {
    flex-shrink: 0;
    font-size: 2.4rem;
  }

  &__label {
    flex-grow: 0;
  }
}
