import type { AmenitiesColor } from "./enums"
import type { Icon } from "../../../assets/icons"

export interface UiAmenityProps {
  /**
   * Label for screen readers and accessibility.
   */
  accessibilityIconLabel?: string
  /**
   * Amenity code to set an ordered list of amenities
   */
  code?: string
  /**
   * Optional color for the icon.
   */
  iconColor?: AmenitiesColor
  /**
   * Name of the icon to display.
   */
  iconName: Icon
  /**
   * Optional variant to manage gap.
   */
  isCancellationPolicy?: boolean
  /**
   * Display label for the amenity.
   */
  label: string
  /**
   * Optional color for the label text.
   */
  labelColor?: AmenitiesColor
}
