<script setup lang="ts">
import { type UiAmenityProps } from "./interface"
import UiIcon from "../UiIcon/UiIcon.vue"

defineProps<UiAmenityProps>()
</script>

<template>
  <div :class="['Amenity', { 'Amenity--cancellation-policy': isCancellationPolicy }]">
    <span v-if="accessibilityIconLabel" class="sr-only">{{ accessibilityIconLabel }}</span>

    <UiIcon :class="['Amenity__icon', `color-${iconColor}`]" :name="iconName" />

    <p :class="['Amenity__label body-02-strong', `color-${labelColor}`]">
      <slot name="label">{{ label }}</slot>
    </p>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiAmenity.scss";
</style>
