@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-utilities/mq";

.Room-card-skeleton {
  border: 0.1rem solid map.get(colors.$neutral, "200");
  color: map.get(colors.$caviarBlack, "700");

  &__container {
    @include mq.media(">=large") {
      display: flex;
    }
  }

  &__image-container {
    position: relative;
    aspect-ratio: 2;

    @include mq.media(">=large") {
      width: 36rem;
      aspect-ratio: 1.33;
    }
  }

  &__image {
    width: 100%;
    height: 100%;

    :deep(.Skeleton) {
      height: 100%;
    }
  }

  &__gallery {
    position: absolute;
    right: map.get(spaces.$sizes, "4");
    bottom: map.get(spaces.$sizes, "4");
    width: 5.6rem;
    height: 4rem;

    @include mq.media(">=small") {
      right: initial;
      left: map.get(spaces.$sizes, "4");
    }
  }

  &__gallery-text {
    position: absolute;
    top: 1.4rem;
    left: 1.2rem;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
    padding-block: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "7");

    @include mq.media(">=large") {
      flex-grow: 1;
    }
  }

  &__content-title {
    padding-top: 0.2rem;

    @include mq.media(">=large") {
      margin-bottom: map.get(spaces.$sizes, "3");
    }
  }

  &__amenities {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "3");

    @include mq.media(">=small", "<large") {
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "8");
    }

    @include mq.media(">=large") {
      gap: map.get(spaces.$sizes, "4");
    }
  }

  &__amenity {
    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "6");
    height: 2.4rem;

    &:deep(.Skeleton) {
      @include mq.media(">=small", "<large") {
        padding: 0 map.get(spaces.$sizes, "3");
      }
    }
  }

  &__divider {
    display: none;

    @include mq.media(">=large") {
      display: block;
      height: unset !important; // overwrite component property
    }
  }

  &__pricing-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: map.get(spaces.$sizes, "6");
    padding: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=large") {
      min-width: 20rem;
      display: flex;
      justify-content: center;
      background-color: colors.$absoluteWhite;
    }
  }

  &__pricing-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    @include mq.media(">=large") {
      margin-top: 50%;
    }
  }

  &__pricing-bottom {
    width: auto;
    align-self: center;
    @include mq.media(">=large") {
      margin-top: auto;
    }
  }

  &__pricing-large {
    display: none;

    @include mq.media(">=large") {
      display: block;
    }
  }

  &__pricing-text {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__action {
    position: relative;
    padding-block-start: map.get(spaces.$sizes, "1");
    padding-block-end: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$customs, "skeleton-3");

    @include mq.media(">=large") {
      display: flex;
      justify-content: end;
      padding: map.get(spaces.$sizes, "6");
    }
  }

  &__action-group {
    position: relative;
  }

  &__action-button {
    width: 100%;

    @include mq.media(">=large") {
      width: unset;
      min-width: 29.4rem;
    }

    :deep(.Skeleton) {
      min-height: 5.6rem;
    }
  }

  &__action-text {
    position: absolute;
    top: 2.2rem;
    left: calc(50% - 4rem);
  }
}
