<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiSkeleton from "../../atoms/UiSkeleton/UiSkeleton.vue"
import { useCurrentWindowSize } from "../../../composables"

const { isMobile } = useCurrentWindowSize()
</script>

<template>
  <div class="Room-card-skeleton">
    <div class="Room-card-skeleton__container">
      <div class="Room-card-skeleton__image-container">
        <div class="Room-card-skeleton__image">
          <UiSkeleton width="100%" height="100%" />
        </div>
        <div class="Room-card-skeleton__gallery">
          <UiSkeleton width="5.6rem" height="4rem" :has-animation="false" />
          <UiSkeleton
            width="3.2rem"
            height="1.2rem"
            :border-radius="isMobile ? '0.6rem' : '0.2rem'"
            class="Room-card-skeleton__gallery-text"
          />
        </div>
      </div>
      <div class="Room-card-skeleton__content">
        <UiSkeleton width="80%" height="2.2rem" border-radius=".3rem" class="Room-card-skeleton__content-title" />

        <ul class="Room-card-skeleton__amenities">
          <li v-for="index in isMobile ? 3 : 5" :key="index" class="Room-card-skeleton__amenity">
            <UiSkeleton width="1.7rem" height="1.7rem" border-radius="50%" />
            <UiSkeleton width="14.6rem" height="1.2rem" border-radius=".3rem" />
          </li>
        </ul>

        <UiSkeleton width="10rem" height="1.2rem" border-radius=".3rem" class="mt-3 mb-4" />
      </div>

      <UiDivider class="Room-card-skeleton__divider" :direction="DividerDirection.VERTICAL" />

      <div class="Room-card-skeleton__pricing-section">
        <div class="Room-card-skeleton__pricing-top">
          <UiSkeleton :width="isMobile ? '5rem' : '7rem'" height="1rem" border-radius=".3rem" />
          <UiSkeleton width="11.5rem" height="2.2rem" border-radius=".3rem" class="Room-card-skeleton__pricing-large" />
          <UiSkeleton width="15rem" height="2.2rem" border-radius=".3rem" />
        </div>

        <UiSkeleton
          width="18rem"
          height="1rem"
          border-radius=".3rem"
          clas="mt-5"
          class="Room-card-skeleton__pricing-bottom"
        />
      </div>
    </div>
    <div class="Room-card-skeleton__action">
      <div class="Room-card-skeleton__action-group">
        <UiSkeleton
          width="100%"
          height="5.6rem"
          :has-animation="false"
          class="Room-card-skeleton__action-button"
          darker
        />
        <UiSkeleton
          width="8rem"
          height="1.2rem"
          :border-radius="isMobile ? '0.4rem' : '0.2rem'"
          class="Room-card-skeleton__action-text"
          darker
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiRoomCardSkeleton.scss";
</style>
