<script setup lang="ts">
import { Categories } from "./enums"
import { type UiPricingSectionProps } from "./interface"
import { computed } from "vue"

const props = defineProps<UiPricingSectionProps>()

const isMemberPrice = computed(() => props.mainPricing.categories?.includes(Categories.MEMBER_RATE) && props.isLogged)
</script>

<template>
  <div class="Pricing-section">
    <div class="Pricing-section__princing-content">
      <p class="Pricing-section__label">
        {{ $t("ui.molecules.ui_pricing_section.label") }}
      </p>
      <span class="Pricing-section__price-container">
        <span
          :class="[
            'Pricing-section__current-price',
            {
              'Pricing-section__member-price': isMemberPrice
            }
          ]"
        >
          <span class="sr-only">{{ $t("ui.molecules.ui_pricing_section.accessibility.main_price") }}</span>
          <p class="Pricing-section__main-price">
            {{ mainPricing.formattedAmount }}
            <span class="Pricing-section__main-price--currency">{{ currency }}</span>
          </p>
        </span>
      </span>
      <p class="Pricing-section__caption">{{ formattedAggregationType }}</p>
    </div>
    <p class="Pricing-section__caption Pricing-section__caption--no-max-width">{{ formattedTaxType }}</p>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiPrincingSection.scss";
</style>
