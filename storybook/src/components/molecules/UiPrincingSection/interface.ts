import type { PricingType } from "./types"

export interface UiPricingSectionProps {
  /**
   * The currency in which prices are displayed.
   */
  currency: string
  /**
   * A string representing the formatted version of the aggregation type.
   */
  formattedAggregationType: string
  /**
   * A string representing the formatted version of the tax type.
   */
  formattedTaxType: string
  /**
   * Indicates if the user is logged in.
   */
  isLogged: boolean
  /**
   * The main pricing details for the offer.
   */
  mainPricing: PricingType
}
