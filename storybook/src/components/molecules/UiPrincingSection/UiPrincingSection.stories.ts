import type { Meta, StoryObj } from "@storybook/vue3"
import { Categories } from "./enums"
import UiPricingSection from "./UiPrincingSection.vue"

const meta = {
  component: UiPricingSection
} satisfies Meta<typeof UiPricingSection>

export default meta
type Story = StoryObj<typeof meta>

export const StandardPricing: Story = {
  args: {
    currency: "USD",
    expediaCompliant: true,
    formattedAggregationType: "for your stay",
    formattedTaxType: "Taxes and fees included.",
    isLogged: false,
    mainPricing: {
      amount: 640,
      categories: [Categories.STANDARD],
      formattedAmount: "$640"
    }
  }
}

export const MemberAndAlternativePricing: Story = {
  args: {
    alternativePricing: {
      amount: 640,
      categories: [Categories.STANDARD],
      formattedAmount: "$640"
    },
    currency: "USD",
    expediaCompliant: true,
    formattedAggregationType: "for your stay",
    formattedTaxType: "Taxes and fees included.",
    isLogged: false,
    mainPricing: {
      amount: 580,
      categories: [Categories.MEMBER_RATE],
      formattedAmount: "$580"
    }
  }
}
