@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Hotel-heading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 100.6rem;
  gap: map.get(spaces.$sizes, "6");

  & > * {
    text-align: center;
  }
}

.Hotel-heading__category {
  @include text.lbf-text("label-01");

  color: map.get(colors.$caviarBlack, "500");
}

.Hotel-heading__title {
  @include text.lbf-text("exp-heading-04-alt");

  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-block-end: map.get(spaces.$sizes, "4");

  @include mq.media(">=small") {
    font-size: map.get(spaces.$sizes, "8");
  }

  @include mq.media(">=medium") {
    font-size: map.get(spaces.$sizes, "10");
  }
}

.Hotel-heading__description {
  @include text.lbf-text("exp-subheading-02");
  color: map.get(colors.$caviarBlack, "700");
}
