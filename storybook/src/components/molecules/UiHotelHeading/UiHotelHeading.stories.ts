import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiHotelHeading from "./UiHotelHeading.vue"

const meta: Meta<typeof UiHotelHeading> = {
  args: {
    category: "Category title",
    description:
      "Lorem ipsum dolor sit amet consectetur. Vitae eu varius sem amet bibendum diam tortor leo porttitor. Et mauris volutpat turpis tortor tellus erat. Interdum consectetur neque vitae vivamus aenean faucibus hac. Sed ut pellentesque pretium nisl ligula.",
    title: "Hotel heading title 1"
  },
  component: UiHotelHeading
}

export default meta

type Story = StoryObj<typeof meta>

export const HotelHeading: Story = {}
