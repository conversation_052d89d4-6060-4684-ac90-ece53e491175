@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Destination-list {
  position: relative;
  width: 30.4rem;
  max-height: 36rem;
  overflow-y: auto;
}

.Destination-title {
  @include text.lbf-text("label-01");
  position: sticky;
  top: 0;

  padding-inline: map.get(spaces.$sizes, "5");
  padding-block: map.get(spaces.$sizes, "6");
  background-color: map.get(colors.$pearlGrey, "200");
}
