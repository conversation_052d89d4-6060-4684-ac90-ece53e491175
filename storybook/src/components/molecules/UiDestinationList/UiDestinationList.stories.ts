import type { Meta, StoryObj } from "@storybook/vue3"
import UiDestinationList from "./UiDestinationList.vue"
import { type UiDestinationListItemType } from "./types"
import { storyDataSetDesktop } from "../../organisms/UiDestination/storyDataSetDesktop"

const meta = {
  args: {
    destinations: storyDataSetDesktop as UiDestinationListItemType[]
  },
  component: UiDestinationList
} satisfies Meta<typeof UiDestinationList>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}
