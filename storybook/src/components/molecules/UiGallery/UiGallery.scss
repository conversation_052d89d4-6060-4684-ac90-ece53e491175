@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-utilities/mq";

.Gallery-room {
  :deep(.Modal) {
    @include mq.media(">=small") {
      border-radius: 0.6rem;
    }
  }

  :deep(.ads-carousel__gallery-item) {
    aspect-ratio: 1.22;

    @include mq.media(">=small") {
      aspect-ratio: 1.54;
    }

    @include mq.media(">=medium") {
      aspect-ratio: 2.27;
    }
  }
}

.Gallery-room__content {
  display: flex;
  flex-direction: column;
  padding-top: 0;
  overflow: hidden;
  gap: map.get(spaces.$sizes, "6");
  padding: map.get(spaces.$sizes, "6");

  @include mq.media(">=small") {
    padding: 0 map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "7");
  }
}
