import type { Meta, StoryObj } from "@storybook/vue3"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiGallery from "./UiGallery.vue"
import { ref } from "vue"
import { storyImages } from "../../molecules/UiSlider/storyDataSet"

const meta: Meta<typeof UiGallery> = {
  args: {
    images: storyImages
  },
  component: UiGallery
}

export default meta

type Story = StoryObj<typeof meta>

export const UiGalleryStory: Story = {
  render: (args) => ({
    components: { UiButton, UiGallery },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div style="min-height: 40rem;">
        <UiButton @click="isOpen = !isOpen" text="Toggle Gallery"/>
        <UiGallery v-bind="args" :is-open="isOpen" @ui-room-gallery::close="isOpen = false"/>
      </div>
    `
  })
}
