import type { UiAmenitiesCardProps } from "./interface"

export const amenitiesCardMockdata: UiAmenitiesCardProps = {
  amenities: [
    { accessibilityIconLabel: "Room size:", code: "SURFACE_AREA", iconName: "size", label: "325 sq.ft. / 354 ft" },
    { accessibilityIconLabel: "Type of bed:", code: "BEDDING", iconName: "bedDouble", label: "One King bed" },
    {
      accessibilityIconLabel: "Number of occupants:",
      code: "MAX_PAX",
      iconName: "occupant",
      label: "2 people max"
    },
    { accessibilityIconLabel: "View type:", code: "VIEWS", iconName: "view", label: "View on the river" },
    {
      accessibilityIconLabel: "Accessible room:",
      code: "ACCESSIBILITY",
      iconName: "wheelchair",
      label: "Accessible room"
    }
  ],
  buttonLabel: "Room details",
  title: "Fairmont 1 king"
}
