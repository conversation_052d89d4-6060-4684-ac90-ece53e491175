<script setup lang="ts">
import { type UiAmenitiesCardProps } from "./interface"
import UiAmenity from "../../atoms/UiAmenity/UiAmenity.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { computed } from "vue"

const props = defineProps<UiAmenitiesCardProps>()
defineEmits(["UiAmenitiesCardButton::click", "UiAmenitiesCardSignIn::click"])

const lowerCasedTitle = computed(() => props.title.toLocaleLowerCase())
</script>

<template>
  <div class="Amenities-card">
    <h3 class="Amenities-card__title">
      {{ lowerCasedTitle }}
    </h3>

    <ul class="Amenities-card__amenities-list">
      <li v-for="amenity in amenities" :key="amenity.accessibilityIconLabel">
        <template v-if="amenity.hasSignInLink && amenity.labelParts">
          <UiAmenity v-bind="amenity">
            <template #label>
              {{ amenity.labelParts.before }}
              <a :href="signInUri" class="Amenities-card__sign-in-link" @click="$emit('UiAmenitiesCardSignIn::click')">
                {{ amenity.labelParts.signIn }}
              </a>
              {{ amenity.labelParts.after }}
            </template>
          </UiAmenity>
        </template>

        <UiAmenity v-else v-bind="amenity" />
      </li>
    </ul>

    <UiLink
      class="Amenities-card__details-button"
      type="button"
      :text="buttonLabel"
      @click="$emit('UiAmenitiesCardButton::click')"
    />
  </div>
</template>

<style lang="scss" scoped>
@use "./UiAmenitiesCard.scss";
</style>
