import type { UiAmenityProps } from "../../atoms/UiAmenity/interface"

export interface UiAmenitiesCardProps {
  /**
   * List of amenities available in the room.
   */
  amenities: ProcessedAmenity[]
  /**
   * Label for the details button.
   */
  buttonLabel: string
  /**
   * URI for user sign-in
   */
  signInUri?: string
  /**
   * Title of the Amenities card.
   */
  title: string
}

/**
 * Extended amenity type with sign-in link handling
 */
export interface ProcessedAmenity extends UiAmenityProps {
  /**
   * Flag indicating if this amenity contains a sign-in link
   */
  hasSignInLink?: boolean
  /**
   * Parts of the label when it contains a sign-in link
   */
  labelParts?: {
    /**
     * Text before the sign-in link
     */
    before: string
    /**
     * Text of the sign-in link
     */
    signIn: string
    /**
     * Text after the sign-in link
     */
    after: string
  }
}
