@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Amenities-card {
  @include mq.media(">=large") {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  &__title {
    @include text.lbf-text("heading-03");
    color: map.get(colors.$basics, "black");

    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__amenities-list {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-block: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "4");

    @include mq.media(">=small") {
      gap: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "8");
    }

    @include mq.media(">=large") {
      flex-direction: column;
      gap: map.get(spaces.$sizes, "4");
    }
  }

  &__details-button {
    :deep(.ads-link) {
      @include text.lbf-text("body-01-underline");
      color: colors.$absoluteBlack;
    }
  }

  &__sign-in-link {
    text-decoration: underline;
    cursor: pointer;
    color: inherit;
  }
}
