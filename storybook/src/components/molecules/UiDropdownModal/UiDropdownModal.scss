@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/boxes";
@use "@sb-utilities/z-index";

.Dropdown-modal {
  position: relative;
  width: max-content;

  &__content {
    z-index: z-index.z("Dropdown-modal");
    position: absolute;
    top: calc(100% + (spaces.$size-base-unit * 7));
    overflow: hidden;
    border-radius: map.get(boxes.$borders, "soft");
    background-color: map.get(colors.$basics, "white");
    box-shadow: map.get(boxes.$shadows, "bottom");

    &--left {
      left: 0;
    }

    &--right {
      right: 0;
    }
  }
}
