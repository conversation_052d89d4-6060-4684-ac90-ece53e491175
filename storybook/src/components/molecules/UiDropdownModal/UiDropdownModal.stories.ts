import type { <PERSON>a, <PERSON>Obj } from "@storybook/vue3"
import { Alignment } from "./enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiCheckbox from "../../atoms/UiCheckbox/UiCheckbox.vue"
import UiDropdownModal from "./UiDropdownModal.vue"
import { ref } from "vue"

const meta = {
  component: UiDropdownModal
} satisfies Meta<typeof UiDropdownModal>

export default meta
type Story = StoryObj<typeof meta>

export const DropdownModal: Story = {
  argTypes: {
    alignment: {
      control: { type: "select" },
      options: Object.values(Alignment)
    }
  },
  args: {
    alignment: Alignment.LEFT,
    isOpen: false
  },
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiDropdownModal },
    setup() {
      const isOpen = ref(false)

      const buttonArgs = {
        id: "button-toggle",
        text: "Toggle dropdown"
      }

      return { args, buttonArgs, isOpen }
    },
    template: `
      <UiDropdownModal v-bind="args" :isOpen="isOpen" @ui-dropdown-modal::close="isOpen = false">
        <template #activator>
          <UiButton @click="isOpen = !isOpen" v-bind="buttonArgs" />
        </template>
        <template #content>
          <ul>
            <li>
              <UiCheckbox name="example" label="Example" id="1" />
            </li>
            <li>
              <UiCheckbox name="example" label="Example" id="2" />
            </li>
            <li>
              <UiCheckbox name="example" label="Example" id="3" />
            </li>
            <li>
              <UiCheckbox name="example" label="Example" id="4" />
            </li>
          </ul>
        </template>
      </UiDropdownModal>
    `
  })
}
