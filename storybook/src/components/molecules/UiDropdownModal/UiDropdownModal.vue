<script setup lang="ts">
import { addTrapFocusToElement, focusFirstElement, removeTrapFocusToElement } from "../../../helpers/index"
import { nextTick, onUnmounted, useTemplateRef, watch } from "vue"
import { Alignment } from "./enums"
import { type UiDropdownModalProps } from "./interface"
import { onClickOutside } from "@vueuse/core"

const props = withDefaults(defineProps<UiDropdownModalProps>(), {
  alignment: Alignment.LEFT
})
const emit = defineEmits(["UiDropdownModal::close", "UiDropdownModal::escape", "UiDropdownModal::tab"])

const dropdownModal = useTemplateRef("dropdownModal")
const dropdownModalContent = useTemplateRef("dropdownModalContent")

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyPress)

  if (dropdownModalContent.value) {
    removeTrapFocusToElement(dropdownModalContent.value)
  }
})

watch(dropdownModalContent, (value) => {
  if (value) {
    addTrapFocusToElement(value)
  }
})

watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen) {
      nextTick(() => {
        if (dropdownModalContent.value) {
          focusFirstElement(dropdownModalContent.value)
        }
      })

      document.addEventListener("keydown", handleKeyPress)
    } else {
      document.removeEventListener("keydown", handleKeyPress)

      nextTick(() => {
        if (dropdownModalContent.value) {
          removeTrapFocusToElement(dropdownModalContent.value)
        }
      })
    }
  }
)

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    event.preventDefault()
    emit("UiDropdownModal::escape")
  }

  if (event.key === "Tab") {
    event.preventDefault()
    emit("UiDropdownModal::tab")
  }
}

const handleClickOutside = () => {
  if (props.isOpen) {
    emit("UiDropdownModal::close")
  }
}

onClickOutside(dropdownModal, handleClickOutside)
</script>

<template>
  <div ref="dropdownModal" class="Dropdown-modal">
    <slot name="activator"></slot>

    <div
      v-show="isOpen"
      ref="dropdownModalContent"
      class="Dropdown-modal__content"
      :class="`Dropdown-modal__content--${props.alignment}`"
    >
      <slot name="content"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDropdownModal.scss";
</style>
