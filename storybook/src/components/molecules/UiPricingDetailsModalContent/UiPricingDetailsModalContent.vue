<script setup lang="ts">
import { DividerColor, DividerDirection } from "../../atoms/UiDivider/enums"
import UiBadge from "../../atoms/UiBadge/UiBadge.vue"
import { UiBadgeVariant } from "../../atoms/UiBadge/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiPricingDetailsModalContentProps } from "./interface"
import { useDate } from "../../../composables/useDate"

defineProps<UiPricingDetailsModalContentProps>()

const { formatDateToLocale } = useDate()

const formatDailyRateDate = (date: string) => {
  return formatDateToLocale(date, {
    day: "numeric",
    month: "short",
    weekday: "long",
    year: "numeric"
  })
}
</script>

<template>
  <div class="Pricing-details-modal">
    <h2 class="Pricing-details-modal__title">{{ $t("ui.molecules.ui_pricing_details_modal.title") }}</h2>

    <div class="Pricing-details-modal__header">
      <h3 class="Pricing-details-modal__section-title">
        {{ $t("ui.molecules.ui_pricing_details_modal.rate_title") }}
      </h3>
      <h4 class="Pricing-details-modal__section-subtitle">{{ rate.title }}</h4>
      <p class="Pricing-details-modal__section-description">
        {{ rate.description }}
      </p>
    </div>

    <div class="Pricing-details-modal__content">
      <div class="Pricing-details-modal__rates-header">
        <h3 class="Pricing-details-modal__section-title">
          {{ $t("ui.molecules.ui_pricing_details_modal.daily_title") }}
        </h3>

        <UiBadge
          v-if="isLoggedIn && memberPrice"
          :label="$t('ui.molecules.ui_pricing_details_modal.member_discount_applied')"
          :variant="UiBadgeVariant.LOYALTY"
        />
      </div>

      <ul class="Pricing-details-modal__rates">
        <li v-for="(dailyRate, index) in dailyRates" :key="`Pricing-details-modal-rate-${index}`">
          <div class="Pricing-details-modal__rate-item">
            <div class="Pricing-details-modal__rate-row">
              <p>
                {{ formatDailyRateDate(dailyRate.date) }}
              </p>
              <p>
                {{ dailyRate.price }}
              </p>
            </div>
          </div>

          <UiDivider :direction="DividerDirection.HORIZONTAL" :color="DividerColor.CAVIAR_BLACK_200" />
        </li>
      </ul>

      <div class="Pricing-details-modal__total">
        <p class="Pricing-details-modal__total-label">
          {{ $t("ui.molecules.ui_pricing_details_modal.total_rate") }}
        </p>

        <div class="Pricing-details-modal__total-price-container">
          <div v-if="isLoggedIn && memberPrice" class="Pricing-details-modal__logged-in-member-price">
            <span class="Pricing-details-modal__price--strikethrough">
              {{ publicPrice }}
            </span>

            <span class="Pricing-details-modal__member-price">
              {{ memberPrice }}
            </span>
          </div>

          <template v-else>
            <div class="Pricing-details-modal__total-price">
              <span v-if="memberPrice">{{ $t("ui.molecules.ui_pricing_details_modal.public_price") }}</span>
              {{ publicPrice }}
            </div>

            <div v-if="memberPrice" class="Pricing-details-modal__member-price">
              <span>{{ $t("ui.molecules.ui_pricing_details_modal.member_price") }}</span>
              {{ memberPrice }}
            </div>
          </template>

          <p class="Pricing-details-modal__total-tax">
            {{ taxLabel }}
          </p>
        </div>
      </div>
    </div>

    <div class="Pricing-details-modal__policies">
      <h3 class="Pricing-details-modal__section-title">
        {{ $t("ui.molecules.ui_pricing_details_modal.policies") }}
      </h3>

      <ul class="Pricing-details-modal__policies-list">
        <li
          v-for="(policie, index) in policies"
          :key="`Pricing-details-modal-policie-${index}`"
          class="Pricing-details-modal__policies-item"
        >
          <p class="Pricing-details-modal__policies-label">{{ policie.label }}</p>
          <p class="Pricing-details-modal__policies-text">{{ policie.text }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiPricingDetailsModalContent.scss";
</style>
