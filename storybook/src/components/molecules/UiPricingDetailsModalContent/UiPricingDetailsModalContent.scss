@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Pricing-details-modal {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "7");

  &__title {
    @include text.lbf-text("heading-01");
    padding-inline: map.get(spaces.$sizes, "6");

    @include mq.media(">=medium") {
      padding-inline: map.get(spaces.$sizes, "7");
    }
  }

  &__header {
    display: flex;
    flex-direction: column;
    padding: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=small") {
      padding: map.get(spaces.$sizes, "7");
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    padding-inline: map.get(spaces.$sizes, "6");

    @include mq.media(">=medium") {
      padding-inline: map.get(spaces.$sizes, "7");
    }
  }

  &__section-title {
    @include text.lbf-text("heading-03");
  }

  &__section-subtitle {
    @include text.lbf-text("body-02-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__section-description {
    @include text.lbf-text("body-02");

    color: map.get(colors.$caviarBlack, "500");
  }

  &__rates {
    display: flex;
    flex-direction: column;
  }

  &__rates-header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: start;
    gap: map.get(spaces.$sizes, "7");
    padding-block-end: map.get(spaces.$sizes, "4");

    @include mq.media(">=small") {
      flex-direction: row;
      align-items: center;
    }
  }

  &__rate-item {
    padding-block: map.get(spaces.$sizes, "6");
  }

  &__rate-row {
    @include text.lbf-text("body-02");
    display: flex;

    justify-content: space-between;
    color: map.get(colors.$caviarBlack, "700");
  }

  &__total {
    display: flex;
    justify-content: space-between;
    margin-top: map.get(spaces.$sizes, "6");
    margin-bottom: map.get(spaces.$sizes, "6");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__total-label {
    @include text.lbf-text("label-01");
  }

  &__total-price-container {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: map.get(spaces.$sizes, "3");
  }

  &__total-price {
    @include text.lbf-text("body-01-strong");
  }

  &__logged-in-member-price {
    display: flex;
    gap: map.get(spaces.$sizes, "4");
    align-items: center;
  }

  &__member-price {
    @include text.lbf-text("body-01-strong");
    color: map.get(colors.$stratosBlue, "800");
  }

  &__price--strikethrough {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
    text-decoration: line-through;
  }

  &__total-tax {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
  }

  &__policies {
    display: flex;
    flex-direction: column;
    padding: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "7");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=medium") {
      padding: map.get(spaces.$sizes, "7");
    }
  }

  &__policies-list {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");
  }

  &__policies-item {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__policies-label {
    @include text.lbf-text("label-01");
  }

  &__policies-text {
    @include text.lbf-text("body-02");
  }
}
