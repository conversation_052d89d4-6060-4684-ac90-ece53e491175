import type { Meta, StoryObj } from "@storybook/vue3"
import UiPricingDetailsModalContent from "./UiPricingDetailsModalContent.vue"
import { pricingDetailsModalContentMockData } from "./mockData"

const meta: Meta<typeof UiPricingDetailsModalContent> = {
  component: UiPricingDetailsModalContent
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: pricingDetailsModalContentMockData
}
