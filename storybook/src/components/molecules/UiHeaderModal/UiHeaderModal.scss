@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-config/colors";
@use "@sb-utilities/mq";
@use "@sb-utilities/z-index";

.Header-modal {
  z-index: z-index.z("Header-modal");
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;

  &__content {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-block: map.get(spaces.$sizes, "11");
    padding-inline: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$basics, "white");

    @include mq.media(">=small") {
      padding: map.get(spaces.$sizes, "12") 0;
    }
  }

  &__overlay {
    position: absolute;
    top: 100%;
    height: 100dvh;
    width: 100%;
    z-index: z-index.z("Header-modal__overlay");
    background-color: map.get(colors.$black, "70");
  }
}
