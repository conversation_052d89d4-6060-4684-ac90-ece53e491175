import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiCheckbox from "../../atoms/UiCheckbox/UiCheckbox.vue"
import UiHeaderModal from "./UiHeaderModal.vue"
import { ref } from "vue"

const modalContent: string = `
  <div class="pa-6" style="min-height: 100%; display: flex; flex-direction: column; justify-content: space-between">
    <UiCheckbox id="1" label="Example Checkbox" name="example"/>
    <UiCheckbox id="2" label="Example Checkbox" name="example"/>
    <UiCheckbox id="3" label="Example Checkbox" name="example"/>
    <UiCheckbox id="4" label="Example Checkbox" name="example"/>
    <UiCheckbox id="5" label="Example Checkbox" name="example"/>
    <UiCheckbox id="6" label="Example Checkbox" name="example"/>
    <UiCheckbox id="7" label="Example Checkbox" name="example"/>
    <UiCheckbox id="8" label="Example Checkbox" name="example"/>
    <UiCheckbox id="9" label="Example Checkbox" name="example"/>
    <UiCheckbox id="10" label="Example Checkbox" name="example"/>
    <UiCheckbox id="11" label="Example Checkbox" name="example"/>
    <UiCheckbox id="12" label="Example Checkbox" name="example"/>
    <UiCheckbox id="13" label="Example Checkbox" name="example"/>
    <UiCheckbox id="14" label="Example Checkbox" name="example"/>
    <UiCheckbox id="15" label="Example Checkbox" name="example"/>
    <UiCheckbox id="16" label="Example Checkbox" name="example"/>
  </div>
`

const meta = {
  component: UiHeaderModal
} satisfies Meta<typeof UiHeaderModal>

export default meta
type Story = StoryObj<typeof meta>

export const ModalHeader: Story = {
  args: {
    activator: null,
    isOpen: false
  },
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiHeaderModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div style="position: relative">
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiHeaderModal :isOpen="isOpen" @ui-header-modal::close="isOpen = false" >
          ${modalContent}
        </UiModal>
      </div>
    `
  })
}
