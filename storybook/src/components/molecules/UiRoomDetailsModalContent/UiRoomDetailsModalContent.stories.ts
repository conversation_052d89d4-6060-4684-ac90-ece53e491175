import type { Meta, StoryObj } from "@storybook/vue3"
import UiRoomDetailsModalContent from "./UiRoomDetailsModalContent.vue"
import { storyImages } from "../../molecules/UiSlider/storyDataSet"
import { uiRoomDetailsModalContentMockData } from "./mockData"

const meta = {
  component: UiRoomDetailsModalContent
} satisfies Meta<typeof UiRoomDetailsModalContent>

export default meta

type Story = StoryObj<typeof meta>

export const WithCarousel: Story = {
  args: {
    ...uiRoomDetailsModalContentMockData,
    images: storyImages
  }
}

export const Default: Story = {
  args: {
    ...uiRoomDetailsModalContentMockData
  }
}
