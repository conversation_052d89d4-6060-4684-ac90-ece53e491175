export const uiRoomDetailsModalContentMockData = {
  amenities: [
    {
      id: 1,
      items: [
        {
          items: [
            { id: 1, subtitle: "Additional charge", title: "Emergency info in rooms" },
            { id: 2, title: "Audible smoke alarms in rooms" }
          ],
          label: "Accessibility And Security"
        },
        {
          items: [{ id: 3, title: "Opening windows" }],
          label: "Comfort Features"
        }
      ],
      name: "SERVICE_AND_EQUIPMENT",
      title: "Service And Equipment"
    },
    {
      id: 2,
      items: [
        {
          items: [
            { id: 1, title: "Emergency info in rooms" },
            { id: 2, title: "Audible smoke alarms in rooms" }
          ],
          label: "Accessibility And Security"
        },
        {
          items: [
            { id: 1, title: "Emergency info in rooms" },
            { id: 2, title: "Audible smoke alarms in rooms" }
          ],
          label: "Accessibility And Security"
        },
        {
          items: [
            { id: 1, title: "Emergency info in rooms" },
            { id: 2, title: "Audible smoke alarms in rooms" }
          ],
          label: "Accessibility And Security"
        },
        {
          items: [{ id: 3, title: "Opening windows" }],
          label: "Comfort Features"
        }
      ],
      name: "test",
      title: "test"
    }
  ],
  chapo: "fairmont gold",
  classCode: "CLASS_CODE",
  description:
    "Lorem ipsum dolor sit amet consectetur. Vitae eu varius sem amet bibendum diam tortor leo porttitor. Et mauris volutpat turpis tortor tellus erat. Interdum consectetur neque vitae vivamus aenean faucibus hac. Sed ut pellentesque pretium nisl ligula.",
  images: [
    { src: "https://picsum.photos/500/300?random=1" },
    { src: "https://picsum.photos/500/300?random=2" },
    { src: "https://picsum.photos/500/300?random=3" }
  ],
  keyFeatures: [
    { description: "225 sq.ft. / 21 sq.m", label: "Size" },
    { description: "2 people", label: "Maximum capacity" },
    { description: "King size bed(s) x1 Complementary bed on demand", label: "Bed Type" },
    { description: "Atque aliquam eos occaecati a maiores adipisci sunt repudiandae", label: "View" },
    { description: "Accessible room", label: "Accessibility" }
  ],
  title: "FAIRMONT 1 KING"
}
