<script setup lang="ts">
import { ref, watch } from "vue"
import { AdsAccordionGroup } from "@accor/ads-components"
import { type UiAccordionGroupProps } from "./interface"
import { UiAccordionGroupVariation } from "./enums"

const currentValue = ref<string[]>([])

const props = withDefaults(defineProps<UiAccordionGroupProps>(), {
  allowMultiple: false,
  variation: UiAccordionGroupVariation.DEFAULT
})

watch(
  () => props.openedItems,
  (newVal) => {
    if (newVal) {
      currentValue.value = newVal
    }
  }
)
</script>

<template>
  <div :class="['Accordion-group', { 'Accordion-group--thin': variation === UiAccordionGroupVariation.THIN }]">
    <AdsAccordionGroup v-model="currentValue" v-bind="props">
      <template v-for="item in items" :key="item.id" #[`content-${item.name}`]>
        <slot :name="`content-${item.name}`"></slot>
      </template>
    </AdsAccordionGroup>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiAccordionGroup.scss";
</style>
