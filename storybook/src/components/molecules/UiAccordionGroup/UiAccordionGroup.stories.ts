import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiAccordionGroup from "./UiAccordionGroup.vue"
import { UiAccordionGroupVariation } from "./enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"

const meta = {
  component: UiAccordionGroup
} satisfies Meta<typeof UiAccordionGroup>

export default meta
type Story = StoryObj<typeof meta>

export const AccordionGroup: Story = {
  argTypes: {
    variation: {
      control: { type: "select" },
      options: Object.values(UiAccordionGroupVariation)
    }
  },
  args: {
    items: [
      {
        id: 1,
        name: "first-accordion",
        title: "First accordion"
      },
      {
        id: 2,
        name: "second-accordion",
        title: "Second accordion"
      },
      {
        id: 3,
        name: "third-accordion",
        title: "Third accordion"
      }
    ]
  },
  render: (args) => ({
    components: { UiAccordionGroup, UiButton },
    setup() {
      return { args }
    },
    template: `
      <UiAccordionGroup v-bind="args">
        <template #content-first-accordion>
          First accordion content
          <UiButton text="button 1" type="button" />
          <br/>
          <UiButton text="button 2" type="button" />
        </template>
        <template #content-second-accordion>
          Second accordion content
        </template>
        <template #content-third-accordion>
          Third accordion content
        </template>
      </UiAccordionGroup>
    `
  })
}
