import type { UiAccordionGroupVariation } from "./enums"

export interface UiAccordionGroupProps {
  /**
   * Allow multiple items to be open at the same time
   */
  allowMultiple?: boolean
  /**
   * Accordion group items
   */
  items: UiAccordionGroupItem[]
  /**
   * List of IDs of currently opened items
   */
  openedItems?: string[]
  /**
   * Variation of the accordion group
   */
  variation?: UiAccordionGroupVariation
}

export interface UiAccordionGroupItem {
  /**
   * Unique identifier for the accordion item
   */
  id: number | string
  /**
   * Name of the accordion item
   */
  name: string
  /**
   * Title displayed for the accordion item
   */
  title: string
}
