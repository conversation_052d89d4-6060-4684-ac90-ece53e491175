import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiTextarea from "./UiTextarea.vue"
import { ref } from "vue"

const meta: Meta<typeof UiTextarea> = {
  args: {
    additionalMessage: "Please provide your feedback",
    assistive: "This is an optional field",
    characterLimit: 250,
    label: "Feedback",
    minRows: 3,
    placeholder: "Type here...",
    required: true
  },
  component: UiTextarea,
  render: (args) => ({
    components: { UiTextarea },
    setup() {
      const text = ref<string>("")

      return { args, text }
    },
    template: `
        <div style="width: 100%; max-width: 600px;">
          <UiTextarea v-bind="args" v-model="text" />
        </div>
      `
  })
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}
