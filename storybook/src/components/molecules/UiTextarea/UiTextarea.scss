@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Textarea {
  :deep(.ads-input) {
    .ads-textarea {
      @include text.lbf-text("body-02");
      resize: vertical;
      height: auto;
      max-height: 17.8rem;
      background-color: map.get(colors.$pearlGrey, "200");
      padding-inline: map.get(spaces.$sizes, "6");
      padding-block: map.get(spaces.$sizes, "7");

      &::placeholder {
        @include text.lbf-text("body-02");
        color: map.get(colors.$caviarBlack, "500");
      }
    }

    .ads-input__label {
      @include text.lbf-text("body-02");
    }

    .ads-input__assistive,
    .ads-input__additional-message {
      @include text.lbf-text("caption-01");

      color: map.get(colors.$caviarBlack, "500");
    }

    .ads-input__character-limit {
      display: none;
    }
  }
}
