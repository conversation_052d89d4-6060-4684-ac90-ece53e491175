export interface UiTextareaProps {
  /**
   * Additional message to be displayed below the textarea.
   */
  additionalMessage?: string
  /**
   * Assistive text for the textarea.
   */
  assistive?: string
  /**
   * Maximum number of characters allowed in the textarea.
   */
  characterLimit?: number
  /**
   * Whether the textarea is disabled.
   */
  diasbled?: boolean
  /**
   * Error message to display if the textarea is invalid.
   */
  errorMessage?: string
  /**
   * Label for the textarea.
   */
  label?: string
  /**
   * Minimum and maximum number of rows for the textarea.
   */
  minRows?: number
  maxRows?: number
  /**
   * Placeholder text for the textarea.
   */
  placeholder?: string
  /**
   * Whether the textarea is required.
   */
  required?: boolean
}
