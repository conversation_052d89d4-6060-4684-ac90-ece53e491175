import { AmenitiesColor } from "../../atoms/UiAmenity/enums"
import type { UiCancellationPolicyProps } from "./interface"

export const uiCancellationPolicyMockDataMonoRoom: UiCancellationPolicyProps = {
  roomsCancellationPolicy: [
    {
      cancellationPolicy: {
        accessibilityIconLabel: "Cancellation policy:",
        iconColor: AmenitiesColor.GREEN_500,
        iconName: "time",
        isCancellationPolicy: true,
        label: "Free cancellation until 6PM 12/08/2024",
        labelColor: AmenitiesColor.GREEN_500
      },
      description:
        "Cancellation free of charge up to 3 days before arrival, 16:00 (local time). After that, the hotel will charge for the first night.",
      id: "1"
    }
  ]
}

export const uiCancellationPolicyMockDataMultiRoom: UiCancellationPolicyProps = {
  roomsCancellationPolicy: [
    {
      cancellationPolicy: {
        accessibilityIconLabel: "Cancellation policy:",
        iconColor: AmenitiesColor.CAVIAR_BLACK_700,
        iconName: "time",
        isCancellationPolicy: true,
        label: "Non-refundable",
        labelColor: AmenitiesColor.CAVIAR_BLACK_700
      },
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer vel lectus consequat, rhoncus ligula sit amet, pulvinar nisl. Nunc viverra vehicula dui ut scelerisque.",
      id: "2"
    },
    {
      cancellationPolicy: {
        accessibilityIconLabel: "Cancellation policy:",
        iconColor: AmenitiesColor.CAVIAR_BLACK_700,
        iconName: "time",
        isCancellationPolicy: true,
        label: "Restricted cancellation",
        labelColor: AmenitiesColor.CAVIAR_BLACK_700
      },
      description:
        "Cancellation free of charge up to 3 days before arrival, 16:00 (local time). After that, the hotel will charge for the first night.",
      id: "3"
    }
  ]
}
