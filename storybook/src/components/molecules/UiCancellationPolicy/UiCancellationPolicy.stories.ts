import type { Meta, StoryObj } from "@storybook/vue3"
import { uiCancellationPolicyMockDataMonoRoom, uiCancellationPolicyMockDataMultiRoom } from "./mockData"
import UiCancellationPolicy from "./UiCancellationPolicy.vue"

const meta: Meta<typeof UiCancellationPolicy> = {
  component: UiCancellationPolicy
}

export default meta

type Story = StoryObj<typeof meta>

export const MonoRoom: Story = {
  args: {
    ...uiCancellationPolicyMockDataMonoRoom
  }
}

export const MultiRoom: Story = {
  args: {
    ...uiCancellationPolicyMockDataMultiRoom
  }
}
