@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Cancellation-policy {
  display: flex;
  flex-direction: column;
  border: 0.1rem solid map.get(colors.$neutral, "200");
  gap: map.get(spaces.$sizes, "7");
  padding-block: map.get(spaces.$sizes, "7");
  padding-inline: map.get(spaces.$sizes, "6");
  background-color: map.get(colors.$pearlGrey, "200");

  @include mq.media(">=small") {
    background-color: map.get(colors.$porcelainWhite, "100");
  }

  .Amenity {
    @include text.lbf-text("body-02");
  }

  &__item {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__title {
    @include text.lbf-text("caption-01-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "5");

    &:deep(.Amenity__icon) {
      font-size: 2rem;
    }
  }

  &__description {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
  }

  &--side-bar {
    background-color: initial;

    .Amenity {
      @include text.lbf-text("body-02-strong");
    }
  }
}
