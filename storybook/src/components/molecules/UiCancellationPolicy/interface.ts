import type { UiAmenityProps } from "../../atoms/UiAmenity/interface"
import type { UiCancellationPolicyVariant } from "./enums"

export interface UiCancellationPolicyProps {
  /**
   * Interface for managing cancellation policies across multiple rooms.
   */
  roomsCancellationPolicy: RoomCancellationPolicy[]
  /**
   * Specifies the variant of the cancellation policy to be used.
   */
  variant?: UiCancellationPolicyVariant
}

export interface RoomCancellationPolicy {
  id: string
  /**
   * Cancellation policy details for the amenity.
   */
  cancellationPolicy: UiAmenityProps
  /**
   * Description of the amenity or service.
   */
  description: string
}
