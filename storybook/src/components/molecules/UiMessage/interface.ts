import type { UiMessageDisplay, UiMessageVariation } from "./enums"

export interface UiMessageProps {
  /**
   * Message
   */
  description?: string
  /**
   * Size prop
   */
  display?: UiMessageDisplay
  /**
   * Whether the message should be displayed horizontally or vertically
   */
  isHorizontal?: boolean
  /**
   * Visible state
   */
  isVisible?: boolean
  /**
   * Links in message
   */
  links?: MessageLink[]
  /**
   * Whether the loyalty banner should be displayed or not
   */
  loyaltyBanner?: boolean
  /**
   * whether there is icon or not
   */
  noIcon?: boolean
  /**
   * Removes the border radius
   */
  noRadius?: boolean
  /**
   * Title
   */
  title?: string
  /**
   * Variation prop
   */
  variation?: UiMessageVariation
}

export interface MessageLink {
  /**
   * The URL that the link points to
   */
  href: string
  /**
   * Specifies where to open the linked document
   */
  target?: string
  /**
   * The text displayed for the link
   */
  text: string
}
