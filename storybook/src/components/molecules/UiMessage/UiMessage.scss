@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Message {
  .ads-message {
    :deep(.ads-message__wrapper) {
      gap: map.get(spaces.$sizes, "4");

      @include mq.media(">=small") {
        padding: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "6");
      }
    }

    // Display
    &--full-width {
      :deep(.ads-message__wrapper) {
        border-radius: 0;
        gap: map.get(spaces.$sizes, "6");
        padding: map.get(spaces.$sizes, "6");
      }
    }

    // Neutral settings
    &--neutral {
      :deep(.ads-message__wrapper) {
        background-color: map.get(colors.$pearlGrey, "200");
      }
    }

    // Accent settings
    &--accent {
      :deep(.ads-message__wrapper) {
        background-color: map.get(colors.$caviarBlack, "200");
      }
    }

    // Loyalty settings
    &--loyalty {
      :deep(.ads-message__wrapper) {
        background-color: map.get(colors.$velvet, "100");
      }

      :deep(.ads-message__icon-container) {
        display: none;
      }

      :deep(.ads-message__message) {
        color: map.get(colors.$stratosBlue, "800");
      }

      :deep(.ads-message__title) {
        color: map.get(colors.$caviarBlack, "900");
      }

      .ads-message_logo--loyalty {
        margin-block-end: map.get(spaces.$sizes, "5");

        @include mq.media(">=small") {
          margin-bottom: 1.6rem;
        }
      }
    }

    // Success settings
    &--success {
      :deep(.ads-message__title) {
        color: map.get(colors.$caviarBlack, "900");
      }

      :deep(.ads-message__icon-container) {
        .ads-icon {
          color: map.get(colors.$green, "500");
        }
      }

      &.ads-message--full-width {
        :deep(.ads-message__wrapper) {
          background-color: map.get(colors.$green, "500");
        }

        :deep(.ads-message__message) {
          color: map.get(colors.$basics, "white");
        }

        :deep(.ads-message__title) {
          color: map.get(colors.$basics, "white");
        }

        :deep(.ads-message__icon-container) {
          .ads-icon {
            color: map.get(colors.$basics, "white");
          }
        }

        :deep(.ads-message__links) {
          color: map.get(colors.$basics, "white");
        }
      }
    }

    // Warning settings
    &--warning-custom {
      :deep(.ads-message__wrapper) {
        background-color: map.get(colors.$yellow, "200");
      }
    }

    // Danger settings
    &--danger {
      :deep(.ads-message__icon-container) {
        .ads-icon {
          color: map.get(colors.$red, "500");
        }
      }
    }

    &--no-radius {
      :deep(.ads-message__wrapper) {
        border-radius: 0;
      }
    }
  }

  // Global settings
  :deep(.ads-message__wrapper) {
    gap: 1.6rem;
    border: none;
    border-radius: 0.6rem;
    padding-inline: map.get(spaces.$sizes, "6");
    padding-block: map.get(spaces.$sizes, "6");

    .ads-message--no-radius & {
      border-radius: 0;
    }
  }

  :deep(.ads-message__message) {
    @include text.lbf-text("body-01");

    color: map.get(colors.$caviarBlack, "700");

    &.ads-message__message:not(:last-child) {
      margin-block-end: map.get(spaces.$sizes, "4");
    }
  }

  :deep(.ads-message__title) {
    @include text.lbf-text("body-01-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  :deep(.ads-message__links) {
    @include text.lbf-text("body-01-underline");

    color: map.get(colors.$caviarBlack, "900");
  }

  :deep(.ads-message__icon-container) {
    display: flex;

    .ads-icon {
      color: map.get(colors.$caviarBlack, "800");
    }
  }

  &--no-icon {
    :deep(.ads-message__icon-container) {
      display: none;
    }
  }

  &--horizontal {
    :deep(.ads-message__content) {
      display: grid;
      grid-template-columns: 1fr max-content;
      align-items: center;
      width: 100%;
    }

    :deep(.ads-message .ads-message__message.ads-message__message:not(:last-child)) {
      margin-block-end: unset;
    }

    :deep(.ads-message__title),
    :deep(.ads-message__message) {
      grid-column: 1;
    }

    :deep(.ads-message__links) {
      grid-column: 2 / -1;
      grid-row: 1;
    }
  }
}
