import type { Meta, StoryObj } from "@storybook/vue3"
import UiComplementaryInformation from "./UiComplementaryInformation.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"

const meta = {
  args: {
    bottomLinkHref: "#",
    bottomLinkText: "Bottom link text",
    title: "UI Complementary Information title",
    topLinkHref: "#",
    topLinkText: "Top link text"
  },
  render: (args) => ({
    components: { UiComplementaryInformation },
    setup() {
      return { args }
    },
    template: `
      <UiComplementaryInformation v-bind="args">
        <template #content>
          <p class="body-02">UiComplementaryInformation content</p>
        </template>
      </UiComplementaryInformation>
    `
  })
} satisfies Meta<typeof UiComplementaryInformation>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}

export const accessibleRooms: Story = {
  args: {
    bottomLinkHref: undefined,
    bottomLinkText: undefined,
    title: "Accessible rooms",
    topLinkHref: undefined,
    topLinkText: undefined
  },
  render: (args) => ({
    components: { UiComplementaryInformation, UiLink },
    setup() {
      const variant = UiLinkVariant.NEUTRAL
      return { args, variant }
    },
    template: `
      <UiComplementaryInformation v-bind="args">
        <template #content>
          <p class="body-02">A representative for room reservations is here to help</p>
          <p class="body-02">+ 1-800-257-7544 (Canada & US)</p>
          <UiLink type="button" text="Learn more about accessibility" class="caption-01-underline" :variant="variant"/>
        </template>
      </UiComplementaryInformation>
    `
  })
}
