@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Complementary-information {
  display: flex;
  flex-direction: column;
  border-style: solid;
  border-width: 1px;
  gap: map.get(spaces.$sizes, "6");
  padding-inline: map.get(spaces.$sizes, "6");
  padding-block: map.get(spaces.$sizes, "7");
  border-color: map.get(colors.$neutral, "200");
}

.Complementary-information__text {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "4");
}

.Complementary-information__title {
  @include text.lbf-text("heading-03");
}

.Complementary-information__content {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");
  color: map.get(colors.$caviarBlack, "700");
}

.Complementary-information__link {
  :deep(.ads-link) {
    @include text.lbf-text("caption-01-underline");
  }
}
