import type { Meta, StoryObj } from "@storybook/vue3"

import type { UiDestinationListItemProps } from "../../atoms/UiDestinationListItem/interface"
import UiDestinationListRegion from "./UiDestinationListRegion.vue"

const hotels = [
  {
    city: "City",
    country: "Country",
    id: "1",
    name: "Hotel name",
    selected: false,
    state: "State"
  },
  {
    city: "City",
    country: "Country",
    id: "2",
    name: "Hotel name",
    selected: false,
    state: "State"
  },
  {
    city: "City",
    country: "Country",
    id: "3",
    name: "Hotel name",
    selected: false,
    state: "State"
  },
  {
    city: "City",
    country: "Country",
    id: "4",
    name: "Hotel name",
    selected: false,
    state: "State"
  }
]

const meta = {
  args: {
    id: "1",
    items: hotels as UiDestinationListItemProps[],
    name: "Region name"
  },
  component: UiDestinationListRegion
} satisfies Meta<typeof UiDestinationListRegion>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}
