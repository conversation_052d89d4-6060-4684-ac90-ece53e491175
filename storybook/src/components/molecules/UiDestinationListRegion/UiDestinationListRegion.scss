@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Destination-list-region {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");

  &:first-child {
    padding-top: map.get(spaces.$sizes, "7");
  }
}

.Destination-list-region__list {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");
}

.Destination-list-region__title {
  @include text.lbf-text("label-01");

  color: map.get(colors.$caviarBlack, "700");
  font-size: 1.2rem;
}
