import type { UiDestinationListItemProps } from "../../atoms/UiDestinationListItem/interface"

export interface UiDestinationListRegionProps {
  /**
   * Unique identifier for the region
   */
  id: string
  /**
   * List of destination items in the region
   */
  items?: UiDestinationListItemProps[]
  /**
   * Name of the region
   */
  name: string
  /**
   * ID of the selected item, if any
   */
  selectedItemId?: string
}
