<script setup lang="ts">
import { AdsRadioGroup } from "@accor/ads-components"
import { type UiRadioGroupProps } from "./interface"

const props = defineProps<UiRadioGroupProps>()
const currentValue = defineModel<string>()
</script>

<template>
  <div class="Radio-group" :class="{ 'Radio-group--hidden-label': isTitleHidden }">
    <AdsRadioGroup v-model="currentValue" v-bind="props" />
  </div>
</template>

<style lang="scss" scoped>
@use "../../atoms/UiRadioButton/UiRadioButton.scss";
@use "./UiRadioGroup.scss";
</style>
