import type { UiRadioButtonDisplay } from "./enums"
import type { UiRadioButtonProps } from "../../atoms/UiRadioButton/interface"

export interface UiRadioGroupProps {
  /**
   * Defines the layout of the radio buttons (column or row)
   */
  display?: UiRadioButtonDisplay
  /**
   * Error message to display when validation fails
   */
  errorMessage?: string
  /**
   * List of radio button items
   */
  items: UiRadioButtonProps[]
  /**
   * Radio group with title hidden
   */
  isTitleHidden?: boolean

  /**
   * Name attribute for the radio group (used in forms)
   */
  name: string
  /**
   * Specifies whether selecting an option is required
   */
  required?: boolean
  /**
   * Title displayed above the radio group, it's mandatory for accessibility reasons. If you want to hide the label, use isTitleHidden prop
   */
  title: string
}
