<script setup lang="ts">
import UiListItem from "../../atoms/UiListItem/UiListItem.vue"
import { type UiListItemGroupProps } from "./interface"

defineProps<UiListItemGroupProps>()
</script>

<template>
  <dl class="List-item-group">
    <dt class="List-item-group__label">{{ title }}</dt>

    <div :class="['List-item-group__items', { 'List-item-group__items--columns': hasColumns }]">
      <dd v-for="item in items" :key="item.id" class="List-item-group__item">
        <UiListItem :id="item.id" :subtitle="item.subtitle" :title="item.title" />
      </dd>
    </div>
  </dl>
</template>

<style lang="scss" scoped>
@use "./UiListItemGroup.scss";
</style>
