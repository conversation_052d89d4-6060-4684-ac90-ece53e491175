import type { Meta, StoryObj } from "@storybook/vue3"
import UiListItemGroup from "./UiListItemGroup.vue"

const meta: Meta<typeof UiListItemGroup> = {
  args: {
    items: [
      { id: 1, subtitle: "Additional charge", title: "Title label" },
      { id: 2, title: "Title label" },
      { id: 3, title: "Title label" }
    ],
    title: "List title"
  },
  component: UiListItemGroup
}

export default meta

type Story = StoryObj<typeof meta>

export const ListItemGroup: Story = {}
