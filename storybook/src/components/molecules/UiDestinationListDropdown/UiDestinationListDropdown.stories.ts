import type { Meta, StoryObj } from "@storybook/vue3"
import UiDestinationListDropdown from "./UiDestinationListDropdown.vue"
import { type UiDestinationListDropdownItem } from "./types"
import { storyDataSetMobile } from "../../organisms/UiDestination/storyDataSetMobile"

const meta = {
  args: {
    items: storyDataSetMobile as UiDestinationListDropdownItem[]
  },
  component: UiDestinationListDropdown
} satisfies Meta<typeof UiDestinationListDropdown>

export default meta
type Story = StoryObj<typeof meta>

export const primary: Story = {}
