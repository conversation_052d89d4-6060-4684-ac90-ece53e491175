@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Reservation-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: map.get(colors.$pearlGrey, "200");
  padding-inline: map.get(spaces.$sizes, "7");
  padding-block: map.get(spaces.$sizes, "10");

  @include mq.media(">=small") {
    padding: map.get(spaces.$sizes, "13") map.get(spaces.$sizes, "8");
  }

  @include mq.media(">=medium") {
    padding: map.get(spaces.$sizes, "13") map.get(spaces.$sizes, "11");
  }

  &__title {
    @include text.lbf-text("exp-heading-04-alt");
    text-align: center;
    padding-block-end: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      font-size: map.get(text.$sizes, "3xl");
      line-height: map.get(text.$line-height, "s");
      letter-spacing: -0.64px;
    }

    @include mq.media(">=medium") {
      font-size: map.get(text.$sizes, "4xl");
      line-height: map.get(text.$line-height, "xs");
      letter-spacing: -0.96px;
    }
  }

  &__message {
    @include text.lbf-text("heading-03");
    display: flex;
    align-items: center;
    flex-direction: column;
    text-align: center;
    gap: map.get(spaces.$sizes, "4");
    padding-block-end: map.get(spaces.$sizes, "10");

    &--confirmed {
      color: map.get(colors.$green, "500");
    }

    &--failed {
      color: map.get(colors.$red, "500");
    }

    & .Icon {
      height: 2.4rem;
      width: 2.4rem;
    }

    @include mq.media(">=small") {
      flex-direction: row;
    }
  }

  &__reservation {
    @include text.lbf-text("body-01");
    text-align: center;
    padding-block-end: map.get(spaces.$sizes, "6");
    color: map.get(colors.$caviarBlack, "700");

    &-number {
      @include text.lbf-text("body-01-strong");
    }
  }

  &__mail,
  &__charged {
    @include text.lbf-text("body-01");
    text-align: center;

    color: map.get(colors.$caviarBlack, "500");
  }

  &__charged {
    padding-block-end: map.get(spaces.$sizes, "10");
  }
}
