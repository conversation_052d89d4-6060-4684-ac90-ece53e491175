<script setup lang="ts">
import type { Icon } from "../../../assets/icons"
import { ReservationStatus } from "./enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiReservationStatusProps } from "./interface"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

const props = defineProps<UiReservationStatusProps>()

const { t } = useI18n()

const isConfirmed = computed(() => props.status === ReservationStatus.CONFIRMED)
const isFailed = computed(() => props.status === ReservationStatus.FAILED)

const texts = computed(() => {
  switch (props.status) {
    case ReservationStatus.CONFIRMED:
      return {
        icon: "checkRounded" as Icon,
        message: t("ui.molecules.ui_reservation_status.confirmed.message"),
        title: t("ui.molecules.ui_reservation_status.confirmed.title")
      }
    case ReservationStatus.FAILED:
    default:
      return {
        icon: "warn" as Icon,
        message: t("ui.molecules.ui_reservation_status.failed.message"),
        title: t("ui.molecules.ui_reservation_status.failed.title")
      }
  }
})
</script>

<template>
  <div class="Reservation-status">
    <h2 class="Reservation-status__title">{{ texts.title }}</h2>

    <h3
      class="Reservation-status__message"
      :class="isConfirmed ? 'Reservation-status__message--confirmed' : 'Reservation-status__message--failed'"
    >
      <UiIcon :name="texts.icon" />

      <span>{{ texts.message }}</span>
    </h3>

    <p v-if="isConfirmed && reservationNumber" class="Reservation-status__reservation">
      {{ $t("ui.molecules.ui_reservation_status.confirmed.room_reservation") }}
      <span class="Reservation-status__reservation-number">{{ reservationNumber }}</span>
    </p>

    <p v-if="isConfirmed && mail" class="Reservation-status__mail">
      {{ $t("ui.molecules.ui_reservation_status.confirmed.mail", { mail }) }}
    </p>
    <p v-else-if="isFailed" class="Reservation-status__charged">
      {{ $t("ui.molecules.ui_reservation_status.failed.no_amount_charged") }}
    </p>

    <!-- TODO: Add behavior for the button to retry reservation -->
    <UiButton
      v-if="isFailed"
      :text="$t('ui.molecules.ui_reservation_status.failed.try_again')"
      :aria-label="$t('ui.molecules.ui_reservation_status.failed.try_again')"
    />
  </div>
</template>

<style lang="scss" scoped>
@use "./UiReservationStatus.scss";
</style>
