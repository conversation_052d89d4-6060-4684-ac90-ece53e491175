<script setup lang="ts">
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import type { UiModalHeaderProps } from "./interface"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

const { t } = useI18n()

const props = defineProps<UiModalHeaderProps>()

const captionLabel = computed(() => {
  return props.caption ? props.caption : t("ui.molecules.ui_modal_header.caption")
})

defineEmits(["UiModalHeader::click"])
</script>

<template>
  <div class="Modal-header" :class="{ 'Modal-header--bordered': hasBorderBottom }">
    <button type="button" class="Modal-header__banner" @click="$emit('UiModalHeader::click')">
      <UiIcon name="arrow" class="Modal-header__icon" />
      <p class="Modal-header__caption">{{ captionLabel }}</p>
    </button>
    <h5 class="Modal-header__title">{{ title }}</h5>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiModalHeader.scss";
</style>
