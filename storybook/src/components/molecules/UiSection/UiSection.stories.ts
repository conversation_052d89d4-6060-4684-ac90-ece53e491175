import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiRadioGroup from "../UiRadioGroup/UiRadioGroup.vue"
import UiSection from "./UiSection.vue"

const meta: Meta<typeof UiSection> = {
  args: {
    title: "Section"
  },
  component: UiSection
}

export default meta

type Story = StoryObj<typeof meta>

export const Section: Story = {
  args: {
    title: "Section"
  },
  render: (args) => ({
    components: { UiRadioGroup, UiSection },
    setup() {
      return { args }
    },
    template: `
      <UiSection v-bind="args">
        <template #content>
          <UiRadioGroup 
            is-title-hidden
            modelValue="1"
            title="radioGroup"
            name="radioGroup"
            :items="[
               { label: 'Label 1', selectedValue: 1 },
                { label: 'Label 2', selectedValue: 2 },
                { label: 'Label 3', selectedValue: 3 }
            ]"
          />
        </template>
      </UiSection>
    `
  })
}
