<script setup lang="ts">
import UiAmenitiesCard from "../../molecules/UiAmenitiesCard/UiAmenitiesCard.vue"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import { UiButtonSize } from "../../atoms/UiButton/enums"
import { type UiRateCardProps } from "./interface"
import { rateCardPriceDisplay } from "./constants"
import { useI18n } from "vue-i18n"

const { t } = useI18n()
const props = defineProps<UiRateCardProps>()
const emit = defineEmits([
  "UiRateCardChoseRate::click",
  "UiRateCardAmenitiesButton::click",
  "UiRateCardAmenitiesSignIn::click"
])

const secondaryPriceInfoText = (secondaryPrice: string | undefined) => {
  if (secondaryPrice) {
    return `${props.userLocalization === "US" ? t("ui.molecules.ui_rate_card.average") : t("ui.molecules.ui_rate_card.total")} ${secondaryPrice} ${props.currency}`
  }

  return ""
}
</script>

<template>
  <div class="Rate-card">
    <UiAmenitiesCard
      class="Rate-card__amenities"
      :title="title"
      :amenities="amenities"
      :button-label="$t('ui.molecules.ui_rate_card.button.rate_details')"
      :sign-in-uri="signInUri"
      @ui-amenities-card-button::click="emit('UiRateCardAmenitiesButton::click')"
      @ui-amenities-card-sign-in::click="emit('UiRateCardAmenitiesSignIn::click')"
    />

    <div class="Rate-card__price-content">
      <h4 class="Rate-card__price-title">{{ aggregationLabel }}</h4>
      <div class="Rate-card__item-content">
        <div class="Rate-card__item">
          <div
            v-if="publicPriceDisplay === rateCardPriceDisplay.STRIKETHROUGH"
            class="Rate-card__main-pricing--strikethrough"
          >
            {{ publicPrice }}
            {{ currency }}
          </div>

          <div v-else class="Rate-card__main-pricing">
            <span
              v-if="publicPriceDisplay === rateCardPriceDisplay.REGULAR_WITH_LABEL && !expediaCompliant"
              class="Rate-card__public-label"
            >
              {{ $t("ui.molecules.ui_rate_card.public_label") }}
            </span>

            <span class="Rate-card__public-price">
              {{ publicPrice }}
            </span>

            <span class="Rate-card__public-currency">{{ currency }}</span>
          </div>

          <div v-if="secondaryPublicPrice">
            <p class="Rate-card__public-secondary-price">{{ secondaryPriceInfoText(secondaryPublicPrice) }}</p>
          </div>
        </div>

        <div v-if="prominentMemberPrice" class="Rate-card__item">
          <div class="Rate-card__main-pricing">
            <p v-if="memberPriceDisplay === rateCardPriceDisplay.REGULAR_WITH_LABEL" class="Rate-card__member-label">
              {{ $t("ui.molecules.ui_rate_card.member_label") }}
            </p>

            <p
              :class="
                memberPriceDisplay === rateCardPriceDisplay.REGULAR_WITH_LABEL
                  ? 'Rate-card__member-price--whith-label'
                  : 'Rate-card__member-price'
              "
            >
              {{ prominentMemberPrice }}
            </p>

            <p class="Rate-card__member-currency">{{ currency }}</p>
          </div>
          <div v-if="secondaryMemberPrice" class="Rate-card__item">
            <p class="Rate-card__member-secondary-price">{{ secondaryPriceInfoText(secondaryMemberPrice) }}</p>
          </div>
        </div>

        <p v-if="formattedTaxType" class="Rate-card__tax">{{ formattedTaxType }}</p>
      </div>

      <UiButton
        class="Rate-card__button"
        :text="$t('ui.molecules.ui_rate_card.button.choose_rate')"
        :size="UiButtonSize.LARGE"
        :is-loading="isLoading"
        :href="link"
        :disabled="disabled"
        @click="$emit('UiRateCardChoseRate::click')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiRateCard.scss";
</style>
