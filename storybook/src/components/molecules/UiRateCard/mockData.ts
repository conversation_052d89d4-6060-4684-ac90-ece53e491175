import { AmenitiesColor } from "../../atoms/UiAmenity/enums"
import type { UiRateCardProps } from "./interface"
import { rateCardPriceDisplay } from "./constants"

export const rateCardMockData: UiRateCardProps = {
  aggregationLabel: "Average per night",
  amenities: [
    {
      accessibilityIconLabel: "Payment:",
      iconColor: AmenitiesColor.CAVIAR_BLACK_800,
      iconName: "check",
      label: "No prepayment required",
      labelColor: AmenitiesColor.CAVIAR_BLACK_700
    },
    {
      accessibilityIconLabel: "Cancel policies",
      iconColor: AmenitiesColor.GREEN_500,
      iconName: "time",
      label: "Free cancellation until 00:00 PM 00/00/0000",
      labelColor: AmenitiesColor.GREEN_500
    },
    {
      accessibilityIconLabel: "Mealplan:",
      iconColor: AmenitiesColor.CAVIAR_BLACK_800,
      iconName: "restaurant",
      label: "All inclusive"
    },
    {
      accessibilityIconLabel: "Loyalty:",
      iconColor: AmenitiesColor.STRATOS_BLUE_800,
      iconName: "allIcon",
      label: "Join for free or sign in to save up to XX on this booking.",
      labelColor: AmenitiesColor.STRATOS_BLUE_800
    }
  ],
  currency: "USD",
  formattedTaxType: "Includes resort experience fees: $XX.\nTaxes not included: XX$.",
  memberPriceDisplay: rateCardPriceDisplay.REGULAR_WITH_LABEL,
  prominentMemberPrice: "914$",
  publicPrice: "963$",
  publicPriceDisplay: rateCardPriceDisplay.REGULAR_WITH_LABEL,
  rateCardId: "1",
  secondaryMemberPrice: "1828$",
  secondaryPublicPrice: "1926$",
  title: "RATE CARD TITLE",
  userLocalization: "FR"
}
