import type { Meta, StoryObj } from "@storybook/vue3"
import UiRateCard from "./UiRateCard.vue"
import { rateCardMockData } from "./mockData"
import { rateCardPriceDisplay } from "./constants"

const meta: Meta<typeof UiRateCard> = {
  argTypes: {
    memberPriceDisplay: {
      control: { type: "select" },
      options: Object.values(rateCardPriceDisplay)
    },
    publicPriceDisplay: {
      control: { type: "select" },
      options: Object.values(rateCardPriceDisplay)
    }
  },
  component: UiRateCard
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    ...rateCardMockData
  }
}
