@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Rate-card {
  background-color: map.get(colors.$basics, "white");
  padding-block: map.get(spaces.$sizes, "6");
  padding-inline: map.get(spaces.$sizes, "7");

  @include mq.media(">=large") {
    min-height: 27.6rem;

    display: grid;
    grid-template-columns: 1fr 29.2rem;
    padding: 0;
  }

  &__amenities {
    @include mq.media(">=large") {
      display: flex;
      flex-direction: column;
      padding: map.get(spaces.$sizes, "7");
    }

    :deep(.Amenities-card__amenities-list) {
      margin-block: map.get(spaces.$sizes, "7");
      gap: map.get(spaces.$sizes, "5");
    }

    :deep(.Amenities-card__amenities-item) {
      @include text.lbf-text("body-02-strong");
      @include text.lbf-text("body-02-strong");
    }
  }

  &__price-content {
    display: flex;
    flex-direction: column;
    border-top: 1px solid map.get(colors.$neutral, "200");
    gap: map.get(spaces.$sizes, "7");
    margin-top: map.get(spaces.$sizes, "7");
    padding-block-start: map.get(spaces.$sizes, "7");

    @include mq.media(">=large") {
      padding: map.get(spaces.$sizes, "7");
      border-top: none;
      margin: 0;
      border-left: 1px solid map.get(colors.$neutral, "200");
    }
  }

  &__price-title {
    @include text.lbf-text("label-01");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__item-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "5");

    @include mq.media(">=large") {
      min-height: 10.2rem;
    }
  }

  &__item {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "3");
  }

  &__main-pricing {
    display: flex;
    align-items: baseline;
    gap: map.get(spaces.$sizes, "4");

    @include mq.media(">=large") {
      margin-top: 0.4rem;
    }

    &--strikethrough {
      @include text.lbf-text("body-02-strong");
      text-decoration: line-through;

      color: map.get(colors.$caviarBlack, "700");
    }
  }

  &__public-label {
    @include text.lbf-text("body-02-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__public-price {
    @include text.lbf-text("heading-01");
  }

  &__public-currency {
    @include text.lbf-text("caption-01");
  }

  &__public-secondary-price {
    @include text.lbf-text("caption-01-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__member-label {
    @include text.lbf-text("body-02-strong");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__member-price {
    @include text.lbf-text("heading-01");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__member-price--whith-label {
    @include text.lbf-text("body-01-strong");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__member-currency {
    @include text.lbf-text("caption-01");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__member-secondary-price {
    @include text.lbf-text("caption-01-strong");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__tax {
    @include text.lbf-text("caption-01");
    white-space: break-spaces;

    color: map.get(colors.$caviarBlack, "500");
  }

  :deep(.Amenity) {
    font-size: map.get(text.$sizes, "xs");

    .Amenity__label {
      margin-top: 3px; // Due to misalignment of the font
    }
  }
}
