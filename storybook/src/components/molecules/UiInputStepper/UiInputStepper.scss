@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Input-stepper {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__labels {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "2");
  }

  &__label {
    @include text.lbf-text("body-01");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__sub-label {
    @include text.lbf-text("caption-01");

    color: map.get(colors.$caviarBlack, "500");
  }

  :deep(.ads-input-stepper__count) {
    @include text.lbf-text("body-01");
    padding-inline: map.get(spaces.$sizes, "3");
  }

  :deep(.ads-button) {
    height: 3.2rem;
    width: 3.3rem;
    border-radius: map.get(boxes.$radii, "rounded");

    &:not(:hover, :active, :disabled) {
      border: 1px solid map.get(colors.$caviarBlack, "800");
      color: map.get(colors.$caviarBlack, "800");
    }

    &:hover {
      &:not(:disabled, :active) {
        background-color: map.get(colors.$caviarBlack, "800");
        color: map.get(colors.$basics, "white");
      }
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "700");
      }
    }

    &:focus-visible::after {
      border-radius: 2px;
      border: map.get(boxes.$borders, "interactiveSelected");
    }
  }
}
