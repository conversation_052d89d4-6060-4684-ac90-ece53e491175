export interface UiInputStepperProps {
  /**
   * Maximum value of the input
   */
  max?: number
  /**
   * Label of the input
   */
  label: string
  /**
   * Minimum value of the input
   */
  min?: number
  /**
   * Current count of the input
   */
  modelValue: number
  /**
   * Id of the input that will be used in the ariaLabelledBy (must be uniq)
   */
  name: string
  /**
   * Screen-only: description for the decrement button
   */
  srOnlyDecrementDescription?: string
  /**
   * Screen-only: description for the increment button
   */
  srOnlyIncrementDescription?: string
  /**
   * Sublabel of the input
   */
  subLabel?: string
}
