import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiInputStepper from "./UiInputStepper.vue"
import { ref } from "vue"

const meta = {
  component: UiInputStepper
} satisfies Meta<typeof UiInputStepper>

export default meta
type Story = StoryObj<typeof meta>

export const InputStepper: Story = {
  args: {
    label: "Label",
    max: 10,
    modelValue: 0,
    name: "label"
  },
  render: (args) => ({
    components: { UiInputStepper },
    setup() {
      const count = ref(0)
      return { args, count }
    },
    template: `
    <div style="width: 300px">
      <UiInputStepper v-bind="args" @update:modelValue="(newVal) => count = newVal" :model-value="count" />
    </div>
    `
  })
}

export const WithSubLabel: Story = {
  args: {
    label: "Label",
    max: 10,
    modelValue: 0,
    name: "label",
    subLabel: "Sublabel"
  },
  render: (args) => ({
    components: { UiInputStepper },
    setup() {
      const count = ref(0)
      return { args, count }
    },
    template: `
    <div style="width: 300px">
      <UiInputStepper v-bind="args" @update:modelValue="(newVal) => count = newVal" :model-value="count" />
    </div>
    `
  })
}
