import type { Meta, StoryObj } from "@storybook/vue3"

import UiAccordion from "./UiAccordion.vue"

const meta = {
  component: UiAccordion
} satisfies Meta<typeof UiAccordion>

export default meta
type Story = StoryObj<typeof meta>

export const Accordion: Story = {
  args: {
    name: "accordion-1",
    title: "Accordion title"
  },
  render: (args) => ({
    components: { UiAccordion },
    setup() {
      return { args }
    },
    template: `
      <UiAccordion v-bind="args">
        <template #content>
          Accordion content
        </template>
      </UiAccordion>
    `
  })
}
