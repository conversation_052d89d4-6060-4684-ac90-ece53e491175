<script setup lang="ts">
import { AdsAccordion } from "@accor/ads-components"
import { type UiAccordionProps } from "./interface"
import { ref } from "vue"

const props = defineProps<UiAccordionProps>()

const expanded = ref(props.startExpanded)
</script>

<template>
  <div class="Accordion">
    <AdsAccordion v-model:expanded="expanded" v-bind="props">
      <template #content>
        <slot name="content"></slot>
      </template>
    </AdsAccordion>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiAccordion.scss";
</style>
