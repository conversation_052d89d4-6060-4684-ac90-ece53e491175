<script setup lang="ts">
import { allowBodyOverflow, preventBodyOverflow } from "../../../helpers/bodyOverflow"
import { nextTick, watch } from "vue"
import { Transition } from "../UiModal/enums"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import type { UiSplashScreenProps } from "./interface"

const props = defineProps<UiSplashScreenProps>()

watch(
  () => props.isVisible,
  (newVal) => {
    nextTick(() => {
      if (newVal) {
        preventBodyOverflow()
      } else if (!newVal) {
        allowBodyOverflow()
      }
    })
  }
)
</script>

<template>
  <Transition :name="Transition.FADE">
    <div v-if="isVisible" class="Splash-screen">
      <UiImage class="Splash-screen__image" :src="src" :alt="alt" />
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
@use "./UiSplashScreen.scss";
</style>
