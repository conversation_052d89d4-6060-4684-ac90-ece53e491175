@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";
@use "@sb-utilities/z-index";

.Sidebar-accordion {
  z-index: z-index.z("Sidebar-accordion");
  position: fixed;
  top: calc(100dvh - 7.6rem);
  right: 0;
  left: 0;
  transition: top 0.7s ease-in-out;
  background-color: map.get(colors.$basics, "white");

  &--opened {
    top: 0;
    height: 100dvh;

    .Sidebar-accordion__button {
      border-color: colors.$absoluteWhite;
      border-bottom: 0.1rem solid map.get(colors.$caviarBlack, "200");
    }

    .Sidebar-accordion__button-end {
      transform: rotateZ(0.25turn);
    }

    .Sidebar-accordion__content {
      opacity: 1;
    }
  }

  &--animating {
    height: 100dvh;

    .Sidebar-accordion__button-start {
      opacity: 0;
    }

    .Sidebar-accordion__content {
      opacity: 0;
    }
  }

  &__button {
    width: 100%;
    border: 0.1rem solid map.get(colors.$caviarBlack, "200");
    border-radius: 0.4rem 0.4rem 0 0;
    border-bottom: 0;
    box-shadow: 0 0 1rem 0 rgba(map.get(colors.$caviarBlack, "300"), 0.25);
    transition: top 0.2s ease-in-out;
    padding: map.get(spaces.$sizes, "6");

    &:focus-visible {
      .Sidebar-accordion__button-content {
        outline: 0.2rem solid black;
      }
    }
  }

  &__button-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__button-start {
    display: flex;
    flex-direction: column;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    gap: map.get(spaces.$sizes, "3");
  }

  &__button-total {
    @include text.lbf-text("caption-01-uppercase");
    text-align: start;

    color: map.get(colors.$caviarBlack, "500");
  }

  &__button-price {
    @include text.lbf-text("body-01-strong");

    color: map.get(colors.$caviarBlack, "900");
  }

  &__button-currency {
    @include text.lbf-text("body-02");
  }

  &__button-title {
    @include text.lbf-text("exp-heading-05");

    padding-block-start: map.get(spaces.$sizes, "4");
    padding-block-end: map.get(spaces.$sizes, "4");
  }

  &__button-end {
    transform: rotateZ(-0.25turn);
    font-size: 2.4rem;
    transition: transform 0.2s ease-in-out;
    color: map.get(colors.$caviarBlack, "900");
  }

  &__content {
    display: flex;
    flex-direction: column;
    max-height: calc(100dvh - 8rem);
    overflow: auto;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    gap: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$basics, "white");
  }
}
