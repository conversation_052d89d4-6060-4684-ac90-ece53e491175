<script setup lang="ts">
import { allowBodyOverflow, preventBodyOverflow } from "../../../helpers/bodyOverflow"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiSidebarAccordionProps } from "./interface"
import { ref } from "vue"

defineProps<UiSidebarAccordionProps>()

const isOpen = ref<boolean>(false)
const isAnimating = ref<boolean>(false)
const isButtonInitial = ref<boolean>(true)

const toggleOpen = () => {
  isOpen.value = !isOpen.value
  isAnimating.value = true

  setTimeout(() => {
    isAnimating.value = false
  }, 700)

  setTimeout(() => {
    isButtonInitial.value = !isButtonInitial.value
  }, 700)

  if (isOpen.value) {
    preventBodyOverflow()
  } else {
    allowBodyOverflow()
  }
}
</script>
<template>
  <div
    id="Sidebar-accordion"
    :class="[
      'Sidebar-accordion',
      { 'Sidebar-accordion--opened': isOpen },
      { 'Sidebar-accordion--animating': isAnimating }
    ]"
  >
    <button
      id="Sidebar-accordion__button"
      class="Sidebar-accordion__button"
      type="button"
      :aria-expanded="isOpen"
      :aria-label="$t('ui.molecules.ui_sidebar_accordion.button_aria_label')"
      aria-controls="Sidebar-accordion__button-content"
      @click="toggleOpen"
    >
      <span class="Sidebar-accordion__button-content">
        <span class="Sidebar-accordion__button-start">
          <!-- TODO hide pricing details block for now, due to inconsistency between LBF and Legacy (cf https://accor-it.atlassian.net/browse/CONVERT-840) -->
          <!-- <template v-if="isButtonInitial">
            <span class="Sidebar-accordion__button-total">{{ $t("ui.molecules.ui_sidebar_accordion.total") }}</span>
            <span class="Sidebar-accordion__button-price">
              {{ price }}
              <span class="Sidebar-accordion__button-currency">{{ currency }}</span>
            </span>
          </template> -->
          <span class="Sidebar-accordion__button-title">{{ $t("ui.molecules.ui_sidebar_accordion.stay") }}</span>
        </span>
        <UiIcon name="chevronRight" class="Sidebar-accordion__button-end" />
      </span>
    </button>
    <div
      v-show="isOpen || isAnimating"
      id="Sidebar-accordion__content"
      class="Sidebar-accordion__content"
      aria-labelledby="Sidebar-accordion__button"
      role="region"
    >
      <slot></slot>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@use "./UiSidebarAccordion.scss";
</style>
