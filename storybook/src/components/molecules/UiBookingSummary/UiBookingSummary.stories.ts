import type { Meta, StoryObj } from "@storybook/vue3"
import { mockStoreSearchStep, mockStoreStayStep } from "./mockData"
import UiBookingSummary from "./UiBookingSummary.vue"

const meta = {
  component: UiBookingSummary
} satisfies Meta<typeof UiBookingSummary>

export default meta
type Story = StoryObj<typeof meta>

export const SearchStep: Story = {
  args: {
    ...mockStoreSearchStep
  }
}

export const StayStep: Story = {
  args: {
    ...mockStoreStayStep
  }
}
