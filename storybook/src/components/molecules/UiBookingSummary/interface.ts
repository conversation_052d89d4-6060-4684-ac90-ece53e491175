import type { RoomType } from "../../organisms/UiRoom/types"
import type { UiBookingSummaryStep } from "./enums"
import type { UiImageProps } from "../../atoms/UiImage/interface"

export interface UiBookingSummaryProps {
  /**
   * Number of adults for the booking.
   */
  adults?: number
  /**
   * Check-in time for the stay.
   */
  checkInHour?: string
  /**
   * Check-out time for the stay.
   */
  checkOutHour?: string
  /**
   * Number of children for the booking.
   */
  children?: number
  /**
   * Id of the room currently selected
   */
  currentRoomId: number
  /**
   * Check-in date for the booking.
   */
  dateIn?: Date
  /**
   * Check-out date for the booking.
   */
  dateOut?: Date
  /**
   * Image properties for the booking display.
   */
  images?: UiImageProps[]
  /**
   * Rooms booked.
   */
  rooms: RoomType[]
  /**
   * Current booking step in the summary.
   */
  step?: UiBookingSummaryStep
  /**
   * Title of the booking or reservation.
   */
  title?: string
  /**
   * Url of the hotel
   */
  url?: string
  loading?: boolean
}
