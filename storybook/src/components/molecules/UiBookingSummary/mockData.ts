import { type UiBookingSummaryProps } from "./interface"
import { UiBookingSummaryStep } from "./enums"
import { UiImageLoading } from "../../atoms/UiImage/enums"

export const mockStoreSearchStep: UiBookingSummaryProps = {
  checkInHour: "08:00",
  checkOutHour: "13:00",
  currentRoomId: 0,
  images: [
    {
      loading: UiImageLoading.LAZY,
      src: "https://m.ahstatic.com/is/image/accorhotels/b6v4:4by3?fmt=jpg&op_usm=1.75,0.3,2,0&resMode=sharp2&iccEmbed=true&icc=sRGB&dpr=on,1.5&wid=335&hei=251&qlt=80"
    }
  ],
  rooms: [],
  step: UiBookingSummaryStep.SEARCH,
  title: "Fairmont hotel name"
}

export const mockStoreStayStep: UiBookingSummaryProps = {
  ...mockStoreSearchStep,
  step: UiBookingSummaryStep.STAY
}

export const mockStoreEnhanceStep: UiBookingSummaryProps = {
  ...mockStoreSearchStep,
  step: UiBookingSummaryStep.ENHANCE
}
