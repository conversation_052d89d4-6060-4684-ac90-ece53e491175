@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/z-index";
@use "@sb-utilities/mq";

.Dropdown-account {
  display: flex;
  flex-direction: column;

  @include mq.media(">=medium") {
    position: relative;
  }

  &__activator {
    :deep(.ads-button) {
      @include text.lbf-text("caption-01");
      padding: 0.4rem;
    }

    :deep(.ads-button:focus-visible::after) {
      border-color: colors.$absoluteWhite;
    }
  }

  &__content {
    z-index: z-index.z("Dropdown-account");
    position: absolute;
    top: 6rem;
    right: 0;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$basics, "white");
    box-shadow: map.get(boxes.$shadows, "topBottom");

    @include mq.media(">=medium") {
      padding: map.get(spaces.$sizes, "7");
    }

    &--logged {
      // Can't use extend here to override padding property
      padding: 1.6rem;
    }

    @include mq.media(">=small") {
      top: 6.8rem;
    }

    @include mq.media(">=medium") {
      top: 4rem;
      width: 37.5rem;
      border-radius: 2px;
    }
  }

  &__close {
    align-self: end;
    width: max-content;
  }

  &__close-icon {
    margin-left: auto;
    cursor: pointer;
    pointer-events: all;
    color: map.get(colors.$basics, "black");
    font-size: map.get(text.$sizes, "xl");
  }

  &__loyalty-card {
    display: flex;
    flex-direction: column;
    border-radius: 0.6rem;
    gap: map.get(spaces.$sizes, "6");
    padding: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$velvet, "100");
  }

  &__loyalty-card-title {
    @include text.lbf-text("heading-03");

    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "6");
    color: map.get(colors.$neutral, "900");
  }

  // font-size not working as wanted here (width and height can't be the same)
  &__loyalty-card-title .Icon {
    width: 3.7rem;
    height: 2.4rem;
    color: map.get(colors.$stratosBlue, "900");
  }

  &__loyalty-card-subtitle {
    @include text.lbf-text("body-02");

    color: map.get(colors.$stratosBlue, "800");
  }

  &__sign-in {
    margin-block: map.get(spaces.$sizes, "4");
  }

  &__bottom-link {
    display: flex;
    justify-content: space-between;
  }

  &__logged {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__user-name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__loyalty-status {
    display: flex;
    border-radius: 6px;
    gap: map.get(spaces.$sizes, "6");
  }

  &__loyalty-status-infos {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__loyalty-status-since {
    font-style: italic;
  }

  &__loyalty-status-icon {
    &--classic {
      color: map.get(colors.$stratosBlue, "900");
    }

    &--silver {
      color: map.get(colors.$silver, "500");
    }

    &--gold {
      color: map.get(colors.$gold, "500");
    }

    &--platinum {
      color: map.get(colors.$platinium, "700");
    }

    &--diamond {
      color: map.get(colors.$diamond, "500");
    }

    &--limitless {
      color: map.get(colors.$basics, "black");
    }
  }

  &__guest-insert {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    border-radius: 6px;
    gap: map.get(spaces.$sizes, "4");
  }

  &__guest-insert-points {
    font-weight: 700;
  }

  &__logged-links {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  :deep(.Button) {
    display: flex;
  }

  .Dropdown-account__activator :deep(.Button__text) {
    display: none;

    @include mq.media(">=medium") {
      display: block;
      max-width: 20rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  :deep(.Button__icon) {
    font-size: 1.8rem;
  }

  &__overlay {
    position: fixed;
    z-index: z-index.z("Overlay");
    top: 6rem;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    background-color: map.get(colors.$black, "70");

    @include mq.media(">=medium") {
      display: none;
    }
  }
}
