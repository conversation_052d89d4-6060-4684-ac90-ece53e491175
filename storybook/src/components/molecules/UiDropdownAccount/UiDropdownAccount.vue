<script setup lang="ts">
import { UiButtonSize, UiButtonVariation } from "../../atoms/UiButton/enums"
import {
  addTrapFocusToElement,
  allowBodyOverflow,
  focusFirstElement,
  preventBodyOverflow,
  removeTrapFocusToElement,
  thousandSeparator
} from "../../../helpers/index"
import { computed, nextTick, onUnmounted, ref, useTemplateRef, watch } from "vue"
import { useCurrentWindowSize, useDate } from "../../../composables/index"
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiDropdownAccountProps } from "./interface"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"
import { onClickOutside } from "@vueuse/core"

const { formatDateToLocale } = useDate()
const { isDesktop } = useCurrentWindowSize()

const props = withDefaults(defineProps<UiDropdownAccountProps>(), {
  isUserLoyaltyMember: false
})

const emit = defineEmits([
  "UiDropdownAccount::logOutClicked",
  "UiDropdownAccount::signInClicked",
  "UiDropdownAccount::signUpClicked"
])

const isOpen = ref(false)

const activator = useTemplateRef<HTMLButtonElement>("activator")
const dropdownAccountContent = useTemplateRef("dropdownAccountContent")

const isUserLoggedIn = computed(() => {
  return !!props.userLoggedInfos?.id
})

const handleClose = () => {
  isOpen.value = false

  if (!isDesktop.value) {
    allowBodyOverflow()
  }
}

const handleToggle = () => {
  if (isOpen.value) {
    handleClose()
  } else {
    isOpen.value = true
  }
}

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === "Escape" && isOpen.value) {
    handleClose()
  }
}

const handleClickOutside = () => {
  if (isOpen.value) {
    handleClose()
  }
}

const handleSignUpClick = () => {
  emit("UiDropdownAccount::signUpClicked")
  handleClose()
}

const handleSignInClick = () => {
  emit("UiDropdownAccount::signInClicked")
  handleClose()
}

const handleLogoutClick = () => {
  emit("UiDropdownAccount::logOutClicked")
  handleClose()
}

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyPress)

  if (dropdownAccountContent.value) {
    removeTrapFocusToElement(dropdownAccountContent.value)
  }
})

watch(dropdownAccountContent, (value) => {
  if (value) {
    if (!isDesktop.value) {
      preventBodyOverflow()
    }
    addTrapFocusToElement(value)
  }
})

watch(isOpen, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      if (dropdownAccountContent.value) {
        focusFirstElement(dropdownAccountContent.value)
      }
    })

    document.addEventListener("keydown", handleKeyPress)
  } else {
    document.removeEventListener("keydown", handleKeyPress)
  }
})

// Need new tick for prevent behavior with activator btn who toggle the modal
nextTick(() => {
  onClickOutside(dropdownAccountContent, handleClickOutside, { ignore: [activator.value] })
})
</script>

<template>
  <div v-if="isOpen && !isDesktop" class="Dropdown-account__overlay"></div>

  <div class="Dropdown-account">
    <UiButton
      ref="activator"
      class="Dropdown-account__activator"
      :text="isUserLoggedIn ? `${userLoggedInfos?.firstName} ${userLoggedInfos?.lastName}` : $t('global.account')"
      :size="UiButtonSize.SMALL"
      :icon="isUserLoggedIn ? 'accountLogged' : 'account'"
      @click="handleToggle"
    />

    <div
      v-if="isOpen"
      ref="dropdownAccountContent"
      class="Dropdown-account__content"
      :class="{ 'Dropdown-account__content--logged': isUserLoggedIn }"
      role="dialog"
      aria-modal="true"
      :aria-label="$t('ui.molecules.ui_dropdown_account.label')"
    >
      <button class="Dropdown-account__close" @click="handleClose" @keydown.enter.space="handleClose">
        <UiIcon class="Dropdown-account__close-icon" name="close" />
      </button>

      <div v-if="!isUserLoggedIn && !isUserLoyaltyMember" class="Dropdown-account__loyalty-card">
        <div class="Dropdown-account__loyalty-card-title">
          <UiIcon name="allMonogram" />
          <span>{{ $t("ui.molecules.ui_dropdown_account.title") }}</span>
        </div>

        <p class="Dropdown-account__loyalty-card-subtitle">{{ $t("ui.molecules.ui_dropdown_account.subtitle") }}</p>

        <UiButton
          :text="$t('global.become_member')"
          :variation="UiButtonVariation.LOYALTY"
          :size="UiButtonSize.SMALL"
          uppercase
          @click="handleSignUpClick"
        />
      </div>

      <UiButton
        v-if="!isUserLoggedIn"
        class="Dropdown-account__sign-in"
        :text="$t('global.sign_in')"
        :variation="UiButtonVariation.TERTIARY"
        uppercase
        @click="handleSignInClick"
      />

      <div v-if="isUserLoggedIn" class="Dropdown-account__logged">
        <div class="Dropdown-account__user-name heading-02 color-caviarBlack-900">
          {{ userLoggedInfos?.firstName }} {{ userLoggedInfos?.lastName }}
        </div>

        <div class="Dropdown-account__loyalty-status bg-neutral-100 pa-4">
          <UiIcon
            class="Dropdown-account__loyalty-status-icon text-4xl"
            :class="`Dropdown-account__loyalty-status-icon--${userLoggedInfos?.statusName?.toLowerCase()}`"
            name="allStatusSquareIcons"
          />

          <div class="Dropdown-account__loyalty-status-infos">
            <p>
              <span class="caption-01-strong color-caviarBlack-500">
                {{ $t("ui.molecules.ui_dropdown_account.loyalty.status") }}
              </span>

              <span class="body-01-uppercase color-stratosBlue-900">
                {{ userLoggedInfos?.statusName }}
              </span>
            </p>

            <p class="Dropdown-account__loyalty-status-since caption-01 color-caviarBlack-500">
              {{ $t("ui.molecules.ui_dropdown_account.loyalty.since") }}
              {{ formatDateToLocale(userLoggedInfos?.statusSince) }}
            </p>
          </div>
        </div>

        <div class="Dropdown-account__guest-insert color-caviarBlack-800 bg-velvet-100 body-01">
          <p>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.reward_points_label") }}
            <span class="Dropdown-account__guest-insert-points">
              {{ thousandSeparator(userLoggedInfos?.rewardPointsValue) }}
            </span>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.points", userLoggedInfos?.rewardPointsValue || 0) }}
          </p>

          <p>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.status_points_label") }}
            <span class="Dropdown-account__guest-insert-points">
              {{ thousandSeparator(userLoggedInfos?.statusPointsValue) }}
            </span>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.points", userLoggedInfos?.statusPointsValue || 0) }}
          </p>

          <p>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.nights_label") }}
            <span class="Dropdown-account__guest-insert-points">
              {{ thousandSeparator(userLoggedInfos?.nightValue) }}
            </span>
            {{ $t("ui.molecules.ui_dropdown_account.guest_insert.nights", userLoggedInfos?.nightValue || 0) }}
          </p>
        </div>

        <div class="Dropdown-account__logged-links">
          <UiLink
            target="_blank"
            :text="$t('ui.molecules.ui_dropdown_account.logged_links.your_reservation')"
            :href="yourReservationUri"
            :variant="UiLinkVariant.STRONG_HOVER"
            @click="handleClose"
          />

          <UiLink
            target="_blank"
            :text="$t('ui.molecules.ui_dropdown_account.logged_links.your_loyalty_account')"
            :href="yourLoyaltyAccountUri"
            :variant="UiLinkVariant.STRONG_HOVER"
            @click="handleClose"
          />

          <UiLink
            target="_blank"
            :text="$t('ui.molecules.ui_dropdown_account.logged_links.your_points_statement')"
            :href="yourPointStatementUri"
            :variant="UiLinkVariant.STRONG_HOVER"
            @click="handleClose"
          />
        </div>
      </div>

      <UiDivider :direction="DividerDirection.HORIZONTAL" />

      <div class="Dropdown-account__bottom-link">
        <UiLink
          v-if="!isUserLoggedIn && myBookingsUrl"
          :text="$t('global.my_bookings')"
          :href="myBookingsUrl"
          :variant="UiLinkVariant.STRONG_HOVER"
          @click="handleClose"
        />
        <UiLink
          v-if="isUserLoggedIn"
          :text="$t('global.log_out')"
          :variant="UiLinkVariant.STRONG_HOVER"
          @click="handleLogoutClick"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDropdownAccount.scss";
</style>
