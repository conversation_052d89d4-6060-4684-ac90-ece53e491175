import type { Meta, StoryObj } from "@storybook/vue3"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiDropdownAccount from "./UiDropdownAccount.vue"
import { ref } from "vue"

const meta = {
  args: {
    yourLoyaltyAccountUri: "#",
    yourPointStatementUri: "#",
    yourReservationUri: "#"
  },
  component: UiDropdownAccount
} satisfies Meta<typeof UiDropdownAccount>

export default meta
type Story = StoryObj<typeof meta>

export const DropdownAccount: Story = {
  args: {
    myBookingsUrl: "https://int-www.fairmont.com/reservations/en/find-reservation"
  },
  render: (args) => ({
    components: { UiButton, UiDropdownAccount },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiDropdownAccount v-bind="args" />
      </div>
    `
  })
}

export const LoggedDropdownAccount: Story = {
  args: {
    userLoggedInfos: {
      firstName: "John",
      id: "userId",
      lastName: "WILLENBERGER",
      nightValue: 2,
      rewardPointsValue: 1800,
      statusName: "DIAMOND",
      statusPointsValue: 500,
      statusSince: "01/01/2020"
    }
  }
}
