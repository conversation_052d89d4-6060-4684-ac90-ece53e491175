export interface UiDropdownAccountProps {
  /**
   * Indicates if the user is a member of the loyalty program
   */
  isUserLoyaltyMember?: boolean
  /**
   * URL to the user's bookings page
   */
  myBookingsUrl?: string
  /**
   * Information about the logged-in user
   */
  userLoggedInfos?: User
  /**
   * URI to access the user's loyalty account
   */
  yourLoyaltyAccountUri: string
  /**
   * URI to view the user's point statement
   */
  yourPointStatementUri: string
  /**
   * URI to manage the user's reservations
   */
  yourReservationUri: string
}

interface User {
  /**
   * Unique identifier for the user
   */
  id: string
  /**
   * First name of the user
   */
  firstName: string
  /**
   * Last name of the user
   */
  lastName: string
  /**
   * Value representing the number of nights the user has spent
   */
  nightValue?: number
  /**
   * Value representing the reward points the user has
   */
  rewardPointsValue?: number
  /**
   * Current status name of the user in the loyalty program
   */
  statusName: string
  /**
   * Value representing the status points the user has accumulated
   */
  statusPointsValue?: number
  /**
   * Date since the user achieved their current status
   */
  statusSince?: string
}
