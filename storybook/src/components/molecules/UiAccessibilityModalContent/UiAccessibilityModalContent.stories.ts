import type { Meta, StoryObj } from "@storybook/vue3"
import UiAccessibilityModalContent from "./UiAccessibilityModalContent.vue"
import { accessibilityModalContentData } from "./mockData"

const meta: Meta<typeof UiAccessibilityModalContent> = {
  args: accessibilityModalContentData,
  component: UiAccessibilityModalContent
}

export default meta

type Story = StoryObj<typeof meta>

export const HotelHeading: Story = {}
