@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Accessibility-modal-content {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "8");
  padding-inline: map.get(spaces.$sizes, "6");
  padding-block-end: map.get(spaces.$sizes, "11");
  color: map.get(colors.$basics, "black");

  @include mq.media(">=small") {
    gap: map.get(spaces.$sizes, "11");
    padding: 0 map.get(spaces.$sizes, "8") map.get(spaces.$sizes, "12") map.get(spaces.$sizes, "8");

    :deep(.List-item-group__label) {
      font-size: 2.8rem;
    }
  }

  @include mq.media(">=medium") {
    padding: 0 map.get(spaces.$sizes, "11") map.get(spaces.$sizes, "12") map.get(spaces.$sizes, "11");

    :deep(.List-item-group__label) {
      font-size: 3.2rem;
    }
  }

  &__contact-title {
    @include text.lbf-text("exp-heading-05");
    margin-block-end: map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      margin-bottom: map.get(spaces.$sizes, "7");
      font-size: 2.8rem;
    }
  }

  &__contact-text {
    @include text.lbf-text("body-01");

    color: map.get(colors.$caviarBlack, "700");

    :deep(.Link) {
      display: inline;

      a {
        color: map.get(colors.$caviarBlack, "700");
      }
    }
  }

  :deep(.Hotel-heading) {
    margin: 0 auto;
  }
}
