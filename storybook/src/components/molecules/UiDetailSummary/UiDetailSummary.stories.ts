import type { Meta, StoryObj } from "@storybook/vue3"

import UiDetailSummary from "./UiDetailSummary.vue"

const meta = {
  component: UiDetailSummary
} satisfies Meta<typeof UiDetailSummary>

export default meta
type Story = StoryObj<typeof meta>

export const DetailSummaryEditable: Story = {
  args: {
    canBeEdited: false,
    disabled: false,
    isComplete: false,
    title: "DetailSummary title"
  },
  render: (args) => ({
    components: { UiDetailSummary },
    setup() {
      return { args }
    },
    template: `
      <UiDetailSummary v-bind="args">
        <template #title>
          DetailSummary title
        </template>
        <template #content>
          DetailSummary content
        </template>
      </UiDetailSummary>
    `
  })
}

export const DetailSummaryEditableDisabled: Story = {
  args: {
    canBeEdited: false,
    disabled: true,
    isComplete: false,
    title: "DetailSummary title"
  },
  render: (args) => ({
    components: { UiDetailSummary },
    setup() {
      return { args }
    },
    template: `
      <UiDetailSummary v-bind="args">
        <template #title>
          DetailSummary title
        </template>
        <template #content>
          DetailSummary content
        </template>
      </UiDetailSummary>
    `
  })
}

export const DetailSummaryEditableDisabledWithSubtitle: Story = {
  args: {
    canBeEdited: false,
    disabled: true,
    isComplete: false,
    subtitle: "DetailSummary subtitle lorem ipsum",
    title: "DetailSummary title"
  },
  render: (args) => ({
    components: { UiDetailSummary },
    setup() {
      return { args }
    },
    template: `
      <UiDetailSummary v-bind="args">
        <template #title>
          DetailSummary title
        </template>
        <template #content>
          DetailSummary content
        </template>
      </UiDetailSummary>
    `
  })
}

export const DetailSummaryEditableComplete: Story = {
  args: {
    canBeEdited: true,
    disabled: false,
    isComplete: true,
    title: "DetailSummary title"
  },
  render: (args) => ({
    components: { UiDetailSummary },
    setup() {
      return { args }
    },
    template: `
      <UiDetailSummary v-bind="args">
        <template #title>
          DetailSummary title
        </template>
        <template #content>
          DetailSummary content
        </template>
      </UiDetailSummary>
    `
  })
}
