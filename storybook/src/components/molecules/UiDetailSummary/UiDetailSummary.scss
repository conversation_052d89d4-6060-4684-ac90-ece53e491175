@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Detail-summary {
  display: grid;
  grid-template-rows: min-content 0fr;
  border: 1px solid map.get(colors.$neutral, "200");
  transition: grid-template-rows 0.3s ease-in-out;
  border-radius: map.get(boxes.$radii, "soft");

  &--open {
    grid-template-rows: min-content 1fr;
  }

  &--disabled {
    background-color: map.get(colors.$pearlGrey, "200");

    :deep(.Icon).Detail-summary__lock {
      font-size: map.get(spaces.$sizes, "8");
      color: map.get(colors.$mistGrey, "400");
    }
  }

  &__summary {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    cursor: default;
    margin: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      align-items: center;
    }

    :deep(.Icon).Detail-summary__icon {
      margin-right: map.get(spaces.$sizes, "5");
      font-size: map.get(spaces.$sizes, "8");
      color: map.get(colors.$green, "500");
    }

    &::marker {
      display: none;
    }
  }

  &__title-block {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "5");
  }

  &__title {
    @include text.lbf-text("exp-heading-04");

    @include mq.media(">=small") {
      padding-right: map.get(spaces.$sizes, "6");
      padding-top: 0.5rem; // The font is not vertically align, so we add some top margin to make it align vertically.
    }
  }

  &__subtitle {
    @include text.lbf-text("body-02");

    color: map.get(colors.$mistGrey, "600");
  }

  &__content {
    overflow: hidden;

    :deep(.Divider) {
      margin-inline: map.get(spaces.$sizes, "7");
    }
  }

  &__button {
    @include text.lbf-text("body-01-uppercase");
    display: flex;
    align-items: center;
    padding: 0;
    background-color: transparent;
    box-shadow: none;
    border: none;
    color: inherit;
    gap: map.get(spaces.$sizes, "4");

    :deep(.Icon) {
      margin-right: 0;
      font-size: 1.8rem;
      color: map.get(colors.$caviarBlack, "800");
    }
  }
}
