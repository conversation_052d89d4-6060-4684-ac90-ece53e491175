<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import type { UiDetailSummaryProps } from "./interface"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { onMounted } from "vue"

const props = defineProps<UiDetailSummaryProps>()

const isOpen = defineModel<boolean>("is-open")

onMounted(() => {
  isOpen.value = !props.disabled && !(props.canBeEdited && props.isComplete)
})
</script>

<template>
  <div
    class="Detail-summary"
    :class="{
      'Detail-summary--open': isOpen,
      'Detail-summary--disabled': disabled
    }"
  >
    <details :id="id" open>
      <summary class="Detail-summary__summary" @click.prevent>
        <UiIcon v-if="isComplete && !isOpen" class="Detail-summary__icon" name="checkRounded" />
        <div class="Detail-summary__title-block">
          <h4 class="Detail-summary__title">{{ title }}</h4>
          <p v-if="subtitle" class="Detail-summary__subtitle">{{ subtitle }}</p>
        </div>
        <button v-if="isComplete && !isOpen" class="Detail-summary__button" @click="isOpen = true">
          <p>{{ $t("ui.molecules.ui_detail_summary.edit") }}</p>
          <UiIcon name="edit" />
        </button>
        <UiIcon v-else-if="disabled" name="lock" class="Detail-summary__lock" />
      </summary>
    </details>
    <div v-if="isOpen" class="Detail-summary__content">
      <UiDivider :direction="DividerDirection.HORIZONTAL" length="auto" />
      <slot name="content"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiDetailSummary.scss";
</style>
