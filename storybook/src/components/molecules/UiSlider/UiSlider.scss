@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";

.Slider {
  :deep(.ads-pagination) {
    position: absolute;
    top: 50%;
    width: 100%;
    height: 0;
    transform: translateY(-50%);
    justify-content: space-between;
    padding-inline: map.get(spaces.$sizes, "6");

    .ads-button:not(.ads-button--none) {
      border-radius: 3.75rem;
    }

    .ads-pagination__previous,
    .ads-pagination__next {
      border-color: map.get(colors.$stratosBlue, "800");
      color: map.get(colors.$stratosBlue, "800");
    }
  }

  :deep(.ads-pagination__page-label) {
    // sr-only
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  :deep(.gallery-item) {
    border-radius: unset;
  }
}
