export interface UiPricingDetailsProps {
  /**
   * The price in the hotel's local currency.
   */
  hotelCurrencyPrices: Price
  /**
   * Total number of nights for the stay.
   */
  numberOfNight: number
  /**
   * The price in the user's selected currency.
   */
  userCurrencyPrices: Price
  /**
   * Whether to show the room details.
   */
  showRoomDetails: boolean

  /**
   * Whether to show the pay online option.
   */
  showPayOnline: boolean

  /**
   * Whether to show the pay at the hotel option.
   */
  showPayAtTheHotel: boolean
}

export type Price = {
  currency: string
  hotelAmount: string
  onlineAmount: string
  totalPrice: string
  taxAndFeesPrice?: string
  roomPrice?: string
}
