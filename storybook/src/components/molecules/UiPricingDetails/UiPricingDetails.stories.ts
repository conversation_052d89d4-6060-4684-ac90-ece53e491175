import type { Meta, StoryObj } from "@storybook/vue3"
import UiPricingDetails from "./UiPricingDetails.vue"
import { pricingDetailsMockData } from "./mockData"

const meta: Meta<typeof UiPricingDetails> = {
  component: UiPricingDetails
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    ...pricingDetailsMockData,
    userCurrencyPrices: undefined
  }
}

export const WithUserCurrency: Story = {
  args: {
    ...pricingDetailsMockData,
    userCurrencyPrices: pricingDetailsMockData.userCurrencyPrices
  }
}
