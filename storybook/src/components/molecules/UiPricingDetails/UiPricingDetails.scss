@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Pricing-details {
  border: 0.1rem solid map.get(colors.$neutral, "200");
  border-radius: map.get(boxes.$radii, "soft");

  &__title {
    @include text.lbf-text("exp-heading-06");
    padding: map.get(spaces.$sizes, "6");
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");
    padding-block: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");
  }

  &__room-container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__pricing-item {
    @include text.lbf-text("body-02");
    display: flex;
    justify-content: space-between;
    color: map.get(colors.$caviarBlack, "700");
  }

  &__caption {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
  }

  &__total-container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");
  }

  &__total-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__total-label-content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }

  &__total-title {
    @include text.lbf-text("body-01-strong");
  }

  &__tax-label {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
  }

  &__total-price {
    @include text.lbf-text("heading-01");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__currency {
    @include text.lbf-text("body-02");
  }

  &__payment-content {
    display: flex;
    flex-direction: column;
    border-radius: map.get(spaces.$sizes, "3");
    gap: map.get(spaces.$sizes, "3");
    padding-inline: map.get(spaces.$sizes, "4");
    background-color: map.get(colors.$pearlGrey, "200");
  }

  &__payment-item {
    @include text.lbf-text("caption-01");
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-block: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "3");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__user-currency-price {
    @include text.lbf-text("caption-01");

    color: map.get(colors.$caviarBlack, "500");
  }

  &__price-block {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: map.get(spaces.$sizes, "4");
  }

  &__local-currency-explanation {
    @include text.lbf-text("caption-01");

    color: map.get(colors.$caviarBlack, "500");
    margin-block: map.get(spaces.$sizes, "6");
    margin-inline: map.get(spaces.$sizes, "3");
  }
}
