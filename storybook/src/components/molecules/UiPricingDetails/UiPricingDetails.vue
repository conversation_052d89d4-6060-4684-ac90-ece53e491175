<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiPricingDetailsProps } from "./interface"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

const props = defineProps<UiPricingDetailsProps>()

const { t } = useI18n()

const numberOfNightLabel = computed(() => {
  return t("ui.molecules.ui_pricing_details.night_label", {
    night: t("ui.molecules.ui_pricing_details.night", { count: props.numberOfNight })
  })
})

const differentCurrency = computed(() => {
  return props.userCurrencyPrices.currency !== props.hotelCurrencyPrices.currency
})
</script>

<template>
  <div class="Pricing-details">
    <h3 class="Pricing-details__title">{{ $t("ui.molecules.ui_pricing_details.title") }}</h3>

    <UiDivider :direction="DividerDirection.HORIZONTAL" />

    <div class="Pricing-details__content">
      <div v-if="showRoomDetails" class="Pricing-details__room-container">
        <div class="Pricing-details__pricing-content">
          <div class="Pricing-details__pricing-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.room_label") }}</p>
            <p>
              {{ hotelCurrencyPrices.roomPrice }}
              <span>{{ hotelCurrencyPrices.currency }}</span>
            </p>
          </div>
          <p class="Pricing-details__caption">{{ numberOfNightLabel }}</p>
        </div>

        <div class="Pricing-details__pricing-item">
          <p>{{ $t("ui.molecules.ui_pricing_details.tax_label") }}</p>
          <p>
            {{ hotelCurrencyPrices.taxAndFeesPrice }}
            <span>{{ hotelCurrencyPrices.currency }}</span>
          </p>
        </div>
      </div>

      <UiDivider v-if="showRoomDetails" :direction="DividerDirection.HORIZONTAL" />

      <div class="Pricing-details__total-container">
        <div class="Pricing-details__total-content">
          <div class="Pricing-details__total-label-content">
            <p class="Pricing-details__total-title">{{ $t("ui.molecules.ui_pricing_details.total_label") }}</p>
            <p class="Pricing-details__tax-label">{{ $t("ui.molecules.ui_pricing_details.taxes_and_fees_label") }}</p>
          </div>

          <div class="Pricing-details__price-block">
            <p class="Pricing-details__total-price">
              {{ userCurrencyPrices.totalPrice }}
            </p>

            <p v-if="differentCurrency" class="Pricing-details__user-currency-price">
              {{ $t("ui.molecules.ui_pricing_details.or") }} {{ hotelCurrencyPrices.totalPrice }}
            </p>
          </div>
        </div>

        <div v-if="showPayAtTheHotel || showPayOnline" class="Pricing-details__payment-content">
          <div v-if="showPayOnline" class="Pricing-details__payment-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.online_payment_label") }}</p>

            <div class="Pricing-details__price-block">
              <p>
                {{ userCurrencyPrices.onlineAmount }}
              </p>

              <p v-if="differentCurrency" class="Pricing-details__user-currency-price">
                {{ $t("ui.molecules.ui_pricing_details.or") }} {{ hotelCurrencyPrices.onlineAmount }}
              </p>
            </div>
          </div>

          <UiDivider v-if="showPayOnline" :direction="DividerDirection.HORIZONTAL" />

          <div v-if="showPayAtTheHotel" class="Pricing-details__payment-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.hotel_payment_label") }}</p>

            <div class="Pricing-details__price-block">
              <p>
                {{ userCurrencyPrices.hotelAmount }}
              </p>

              <p v-if="differentCurrency" class="Pricing-details__user-currency-price">
                {{ $t("ui.molecules.ui_pricing_details.or") }} {{ hotelCurrencyPrices.hotelAmount }}
              </p>
            </div>
          </div>
          <template v-if="differentCurrency">
            <UiDivider :direction="DividerDirection.HORIZONTAL" />
            <p class="Pricing-details__local-currency-explanation">
              {{ $t("ui.molecules.ui_pricing_details.local_currency_explanation") }}
            </p>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiPricingDetails.scss";
</style>
