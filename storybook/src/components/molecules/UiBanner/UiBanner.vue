<script setup lang="ts">
import type { UiBannerProps } from "./interface"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "../../atoms/UiButton/enums"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"

defineEmits(["becomeMember:banner"])

defineProps<UiBannerProps>()
</script>

<template>
  <div class="Banner">
    <UiIcon class="Banner__icon" name="allIcon" />

    <p class="Banner__message">
      {{ $t("ui.molecules.ui_banner.message", { percentage }) }}
    </p>

    <UiButton
      class="Banner__button"
      :is-loading="loading"
      :variation="UiButtonVariation.LOYALTY_SECONDARY"
      :text="$t('ui.molecules.ui_banner.button_label')"
      @click="$emit('becomeMember:banner')"
    />
  </div>
</template>

<style lang="scss" scoped>
@use "./UiBanner.scss";
</style>
