@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Banner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  background-color: map.get(colors.$velvet, "100");
  color: map.get(colors.$stratosBlue, "800");
  gap: map.get(spaces.$sizes, "7");
  padding: map.get(spaces.$sizes, "7");

  @include mq.media(">=small") {
    flex-direction: row;
    padding: 1.6rem;
  }

  &__icon {
    display: none;

    @include mq.media(">=small") {
      display: block;
      width: 4.8rem;
      height: 4.8rem;
    }
  }

  &__message {
    @include text.lbf-text("body-02-strong");
    flex: 1;
  }

  :deep(.<PERSON><PERSON>) {
    @include mq.media("<small") {
      width: 100%;
    }
  }
}
