@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Mobile-search-resume {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");
  padding: map.get(spaces.$sizes, "6");
  background-color: map.get(colors.$pearlGrey, "200");

  @include mq.media(">=small") {
    padding: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "8");
  }

  &__destination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    p {
      @include text.lbf-text("body-01");
    }

    :deep(.ads-link) {
      @include text.lbf-text("body-01-underline");
      color: colors.$absoluteBlack;
    }
  }

  &__dates {
    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    :deep(.Icon) {
      transform: rotateZ(180deg);
      font-size: map.get(text.$sizes, "s");
    }

    span {
      @include text.lbf-text("caption-01");
      color: map.get(colors.$caviarBlack, "700");
    }
  }

  &__details {
    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    :deep(.Icon) {
      color: map.get(colors.$neutral, "200");
      font-size: map.get(text.$sizes, "3xs");
    }

    span {
      @include text.lbf-text("caption-01");
      color: map.get(colors.$caviarBlack, "500");
    }
  }
}
