<script setup lang="ts">
import { SpecialRates } from "../../organisms/UiSpecialRates/enums"
import { UiButtonType } from "../../atoms/UiButton/enums"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import type { UiMobileSearchResumeProps } from "./interface"
import { computed } from "vue"
import { useDatePicker } from "../../../composables/useDatePicker"
import { useI18n } from "vue-i18n"

defineEmits(["UiMobileSearchResume::edit"])
const props = defineProps<UiMobileSearchResumeProps>()

const { t } = useI18n()
const { computedDate } = useDatePicker()

const computedDateIn = computed(() => computedDate(props.dateIn as Date))

const computedDateOut = computed(() => computedDate(props.dateOut as Date))

const computedRooms = computed(() => {
  const roomsNumber = props.rooms?.length ?? 0
  const guestsNumber = (props.rooms ?? []).reduce(
    (acc: number, { adults, children }: { adults: number; children: number }) => acc + adults + children,
    0
  )

  return `${t("ui.molecules.ui_mobile_search_resume.rooms", { count: roomsNumber })}, ${t("ui.molecules.ui_mobile_search_resume.guests", { count: guestsNumber })}`
})

const computedSpecialRates = computed(() => {
  if (props.iataCode) {
    return t("ui.organisms.ui_special_rates.rate_items.iata_code_label")
  }

  if (props.specialRate === SpecialRates.NONE) {
    return t("ui.molecules.ui_mobile_search_resume.special_rates.none")
  }

  return t(`ui.organisms.ui_special_rates.rate_items.items.${props.specialRate.toUpperCase()}`)
})
</script>

<template>
  <div class="Mobile-search-resume">
    <div class="Mobile-search-resume__destination">
      <p>{{ hotel?.name }}</p>
      <UiLink
        :text="$t('ui.molecules.ui_mobile_search_resume.edit')"
        :type="UiButtonType.BUTTON"
        @click="$emit('UiMobileSearchResume::edit')"
      />
    </div>

    <div class="Mobile-search-resume__dates">
      <span>{{ computedDateIn }}</span>
      <UiIcon name="arrow" />
      <span>{{ computedDateOut }}</span>
    </div>

    <div class="Mobile-search-resume__details">
      <span>{{ computedRooms }}</span>
      <UiIcon name="divider" />
      <span>{{ computedSpecialRates }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiMobileSearchResume.scss";
</style>
