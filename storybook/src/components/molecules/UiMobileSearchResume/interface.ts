import type { Destination } from "../../atoms/UiDestinationListItem/interface"
import type { RoomType } from "../../organisms/UiRoom/types"
import type { SpecialRates } from "../../organisms/UiSpecialRates/enums"

export interface UiMobileSearchResumeProps {
  /**
   * The date the user wants to check-in.
   */
  dateIn?: Date
  /**
   * The date the user wants to check-out.
   */
  dateOut?: Date
  /**
   * Selected hotel
   */
  hotel?: Destination
  /**
   * Iata Code
   */
  iataCode?: string
  /**
   * Rooms selected by the user.
   */
  rooms: RoomType[]
  /**
   * Selected special rates
   */
  specialRate: SpecialRates
}
