import type { ActionAlignment, ModalSize, Transition } from "./enums"

export interface UiModalProps {
  /**
   * Alignment of the action button at the bottom of the modal
   */
  actionAlignment?: ActionAlignment
  /**
   * Id of the element labelling the modal if it has no element slotted inside the title
   */
  ariaLabelledBy?: string
  /**
   * Classes to add to the modal
   */
  class?: string
  /**
   * Whether to use an inverted close style, need to be used with `withOverlay` prop
   */
  isCloseStyleInverse?: boolean
  /**
   * Boolean to handle the modal's status inside the component
   */
  isOpen?: boolean
  /**
   * Determines whether the modal should take up full screen on mobile devices
   * need to be used with `withOverlay` prop
   */
  mobileFullScreen?: boolean
  /**
   * Determines whether the modal should take full size or not
   */
  mobileMinHeight?: boolean
  /**
   * Size of the modal, need to be used with `withOverlay` prop
   */
  modalSize?: ModalSize
  /**
   * The modal doesn't have any header displayed
   */
  noHeader?: boolean
  /**
   * The modal header doesn't have any shadow
   */
  noHeaderShadow?: boolean
  /**
   * Determines whether the modal should take up full screen on tablet devices
   * need to be used with `withOverlay` prop
   */
  tabletFullScreen?: boolean
  /**
   * Transition name
   */
  transition?: Transition
  /**
   * Determines whether to show an overlay behind the modal
   */
  withOverlay?: boolean
}
