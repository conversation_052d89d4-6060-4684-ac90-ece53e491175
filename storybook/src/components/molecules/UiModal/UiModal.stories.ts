import { ActionAlignment, ModalSize } from "./enums"
import type { <PERSON>a, StoryObj } from "@storybook/vue3"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiCheckbox from "../../atoms/UiCheckbox/UiCheckbox.vue"
import UiModal from "../UiModal/UiModal.vue"
import UiModalHeader from "../UiHeaderModal/UiHeaderModal.vue"

import { ref } from "vue"

const modalContent: string = `
  <div class="pa-6" style="min-height: 100%; display: flex; flex-direction: column; justify-content: space-between">
    <UiCheckbox id="1" label="Example Checkbox" name="example"/>
    <UiCheckbox id="2" label="Example Checkbox" name="example"/>
    <UiCheckbox id="3" label="Example Checkbox" name="example"/>
    <UiCheckbox id="4" label="Example Checkbox" name="example"/>
    <UiCheckbox id="5" label="Example Checkbox" name="example"/>
    <UiCheckbox id="6" label="Example Checkbox" name="example"/>
    <UiCheckbox id="7" label="Example Checkbox" name="example"/>
    <UiCheckbox id="8" label="Example Checkbox" name="example"/>
    <UiCheckbox id="9" label="Example Checkbox" name="example"/>
    <UiCheckbox id="10" label="Example Checkbox" name="example"/>
    <UiCheckbox id="11" label="Example Checkbox" name="example"/>
    <UiCheckbox id="12" label="Example Checkbox" name="example"/>
    <UiCheckbox id="13" label="Example Checkbox" name="example"/>
    <UiCheckbox id="14" label="Example Checkbox" name="example"/>
    <UiCheckbox id="15" label="Example Checkbox" name="example"/>
    <UiCheckbox id="16" label="Example Checkbox" name="example"/>
  </div>
`

const modalActions: string = `
  <template #actions>
    <UiButton text="Cancel" />
    <UiButton text="Validate" />
  </template>
`

const meta = {
  argTypes: {
    modalSize: {
      control: { type: "select" },
      options: Object.values(ModalSize)
    }
  },
  component: UiModal
} satisfies Meta<typeof UiModal>

export default meta
type Story = StoryObj<typeof meta>

export const Modal: Story = {
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal :is-open="isOpen" v-bind="args" @ui-modal::close="isOpen = false">
          ${modalContent}
        </UiModal>
      </div>
    `
  })
}

export const ModalWithTitle: Story = {
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal :is-open="isOpen" v-bind="args" @ui-modal::close="isOpen = false">
          <template #title>
            Modal Title
          </template>
          ${modalContent}
        </UiModal>
      </div>
    `
  })
}

export const ModalWithCustomHeader: Story = {
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal, UiModalHeader },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal :is-open="isOpen" v-bind="args" @ui-modal::close="isOpen = false">
          <template #header>
            <UiModal-header has-border-bottom title="Modal title" @ui-modal-header::click="isOpen = false" />
          </template>
          ${modalContent}
        </UiModal>
      </div>
    `
  })
}

export const ModalWithAction: Story = {
  argTypes: {
    actionAlignment: {
      control: { type: "select" },
      options: Object.values(ActionAlignment)
    }
  },
  args: {
    ...Modal.args,
    actionAlignment: ActionAlignment.LEFT
  },
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal v-bind="args" v-if="isOpen" @ui-modal::close="isOpen = false">
          <template #title>
            Modal Title
          </template>
          ${modalContent}
          <template #actions>
            <UiButton text="Validate" />
          </template>
        </UiModal>
      </div>
    `
  })
}

export const ModalWithActions: Story = {
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal :is-open="isOpen" v-bind="args" @ui-modal::close="isOpen = false">
          <template #title>
            Modal Title
          </template>
          ${modalContent}
          ${modalActions}
        </UiModal>
      </div>
    `
  })
}

export const ModalWithOverlay: Story = {
  render: (args) => ({
    components: { UiButton, UiCheckbox, UiModal },
    setup() {
      const isOpen = ref<boolean>(false)

      return { args, isOpen }
    },
    template: `
      <div>
        <UiButton @click="isOpen = !isOpen" text="Toggle Modal"/>
        <UiModal :is-open="isOpen" v-bind="args" @ui-modal::close="isOpen = false" :with-overlay="true" aria-labelled-by="modal-title">
          <div class="pa-7">
            <p class="heading-01 mb-7" id="modal-title">Modal title</p>
            <p class="body-01-strong">Lorem ipsum dolor sit amet consectetur. Mus semper nullam enim sit gravida. Facilisis integer.</p>
            <p class="body-01">
            On the Fairmont websites, Accor and its partners wish to store or retrieve information on your device in order to : <br />
              - operate the websites and provide you with the services you request (these cannot be rejected) <br />
              - enhance and customize website functionalities <br />
              - measure website audience and performance <br />
              - profile your interests to provide you with relevant advertising <br />
              - allow you to interact with social networks. <br />
            You will be able to modify your choices at any time by clicking on the "Cookies" link at the bottom of the page. More information
            </p>

            <UiButton style="max-width: 300px;" class="mt-8" text="Lorem" />
          </div>
        </UiModal>
      </div>
    `
  })
}
