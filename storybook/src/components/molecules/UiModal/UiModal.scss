@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/z-index";
@use "@sb-utilities/mq";

.Modal {
  z-index: z-index.z("Modal");
  position: fixed;
  inset: 0;
  display: flex;
  flex-direction: column;
  background-color: map.get(colors.$basics, "white");

  &__header {
    // Note : no z-index here, otherwise it prevents nested modals header to go over the nesting modal's header
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &--default {
      padding: map.get(spaces.$sizes, "7");
      gap: map.get(spaces.$sizes, "6");
      box-shadow: map.get(boxes.$shadows, "topStrong");
    }

    &--no-shadow {
      box-shadow: none;
    }

    &--overlay {
      padding-block: map.get(spaces.$sizes, "8");
      padding-inline-end: map.get(spaces.$sizes, "6");

      @include mq.media(">=small") {
        padding: map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "8");
      }

      .Modal--small &,
      .Modal--medium & {
        padding: map.get(spaces.$sizes, "6");

        @include mq.media(">=small") {
          padding: map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "5");
        }
      }
    }

    &:has(.Modal__close--inverse) {
      height: 0;
      padding: 0;
    }
  }

  &__close {
    display: flex;
    margin-left: auto;
    cursor: pointer;
    pointer-events: all;
    color: map.get(colors.$caviarBlack, "800");
    font-size: map.get(text.$sizes, "xl");

    &--inverse {
      position: absolute;
      top: map.get(spaces.$sizes, "7");
      right: map.get(spaces.$sizes, "7");
      border-radius: 2px;
      padding: map.get(spaces.$sizes, "6");
      background-color: map.get(colors.$basics, "white");
    }
  }

  &__content {
    flex-grow: 1;
    overflow-y: auto;
  }

  &__actions {
    position: sticky;
    display: flex;
    justify-content: space-between;
    box-shadow: map.get(boxes.$shadows, "top");
    padding: map.get(spaces.$sizes, "6");

    :deep(.ads-button) {
      // default style
      @include text.lbf-text("body-01-uppercase");
    }

    &--right {
      justify-content: flex-end;
    }

    &--center {
      justify-content: center;
    }

    &--full {
      & * {
        width: 100%;
      }
    }
  }

  // Overlay styles
  &--with-overlay {
    max-width: 129.2rem;
    width: calc(100% - map.get(spaces.$sizes, "8"));
    left: calc(50% - map.get(spaces.$sizes, "6"));
    transform: translateX(-50%);
    margin: map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      // In the case there is an overlay, the modal doesn't take all the height available
      // but only the needed height, maxed at 100dvh - Y margin
      bottom: unset;
      width: calc(100% - map.get(spaces.$sizes, "10"));
      max-height: calc(100dvh - map.get(spaces.$sizes, "10"));
      margin: map.get(spaces.$sizes, "7");
      left: calc(50% - map.get(spaces.$sizes, "7"));
    }
  }

  &--mobile-fullscreen {
    @include mq.media("<small") {
      width: 100%;
      left: 50%;
      margin: 0;
    }
  }

  &--mobile-min-height {
    @include mq.media("<small") {
      inset: unset;
      top: 0;
      left: 0;
      transform: unset;
    }
  }

  &--tablet-full-screen {
    @include mq.media("<medium") {
      width: 100%;
      max-height: 100dvh;
      left: 50%;
      margin: 0;
    }
  }

  // Modal size modifiers
  &--small {
    @include mq.media(">=small") {
      max-width: 64rem;
    }
  }

  &--medium {
    @include mq.media(">=medium") {
      max-width: 100.6rem;
      width: calc(100% - map.get(spaces.$sizes, "8"));
      left: calc(50% - map.get(spaces.$sizes, "7"));
    }
  }
}

.Modal__overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  inset: 0;
  z-index: z-index.z("Overlay");
  background-color: map.get(colors.$black, "70");
  cursor: pointer;
}
