<script setup lang="ts">
import { UiImageCountPosition, type UiImageCountProps } from "./interface"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { computed } from "vue"

defineEmits(["UiImageCount::click"])
const props = withDefaults(defineProps<UiImageCountProps>(), {
  position: UiImageCountPosition.TOP_RIGHT
})

const positionClass = computed(() => {
  return `Image-count--${props.position}`
})
</script>

<template>
  <div class="Image-count" :class="positionClass">
    <button class="Image-count__button" :aria-label="ariaLabel" @click="$emit('UiImageCount::click')">
      <UiIcon name="iconGallery" />
      <p>{{ count }}</p>
    </button>
  </div>
</template>

<style lang="scss" scoped>
@use "./UiImageCount.scss";
</style>
