@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Image-count {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  padding: map.get(spaces.$sizes, "4");

  &--top-right {
    justify-content: flex-end;
    align-items: flex-start;
  }

  &--bottom-right {
    justify-content: flex-end;
    align-items: flex-end;
  }

  &--top-left {
    justify-content: flex-start;
    align-items: flex-start;
  }

  &--bottom-left {
    justify-content: flex-start;
    align-items: flex-end;
  }

  &__button {
    display: flex;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: background-color 0.2s ease;
    gap: map.get(spaces.$sizes, "3");
    padding: map.get(spaces.$sizes, "5");
    color: map.get(colors.$porcelainWhite, "100");

    &:hover {
      background-color: rgba(0, 0, 0, 0.6);
    }

    :deep(.Icon) {
      font-size: map.get(text.$sizes, "s");
    }

    p {
      @include text.lbf-text("caption-01-strong");
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
    }
  }
}
