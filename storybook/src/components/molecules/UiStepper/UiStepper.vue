<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from "vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiStep from "../../atoms/UiStep/UiStep.vue"
import { type UiStepperProps } from "./interface"
import { useI18n } from "vue-i18n"

const props = defineProps<UiStepperProps>()
const emit = defineEmits(["UiStepper::click"])

const stepperListRef = ref<HTMLElement | null>(null)

const { t } = useI18n()

onMounted(async () => {
  await nextTick()
  centerActiveStep()
})

const stepsWithStates = computed(() => {
  return props.items.map((step, index) => ({
    ...step,
    isCompleted: index < props.currentStepIndex,
    isCurrent: index === props.currentStepIndex
  }))
})

function handleStepClick(index: number) {
  emit("UiStepper::click", index)
}

function centerActiveStep() {
  const { currentStepIndex } = props
  const stepperList = stepperListRef.value

  if (!stepperList || currentStepIndex >= stepperList.children.length || currentStepIndex < 0) {
    return
  }

  const activeStep = stepperList.children[currentStepIndex] as HTMLElement

  activeStep.scrollIntoView({
    behavior: "smooth",
    block: "nearest",
    inline: "center"
  })
}
</script>

<template>
  <nav class="Stepper" :aria-label="t('ui.molecules.ui_stepper.aria_label')">
    <ol ref="stepperListRef" class="Stepper__list">
      <li
        v-for="(step, index) in stepsWithStates"
        :key="index"
        :class="['Stepper__item', { 'Stepper__item--disabled': !step.isCompleted }]"
        @click="handleStepClick(index)"
      >
        <UiStep v-bind="step" :total-step="stepsWithStates.length" />
        <span v-if="index < stepsWithStates.length - 1" class="Stepper__separator">
          <UiIcon name="chevronRight" />
        </span>
      </li>
    </ol>
  </nav>
</template>

<style lang="scss" scoped>
@use "./UiStepper.scss";
</style>
