import type { Meta, StoryFn } from "@storybook/vue3"
import UiStepper from "./UiStepper.vue"
import { ref } from "vue"

export default {
  args: {
    currentStepIndex: 2,
    items: [
      { href: "#", isCompleted: true, isCurrent: false, step: 1, text: "Search", totalStep: 4 },
      { href: "#", isCompleted: true, isCurrent: false, step: 2, text: "Stay", totalStep: 4 },
      { href: "#", isCompleted: false, isCurrent: true, step: 3, text: "Enhance", totalStep: 4 },
      { href: "#", isCompleted: false, isCurrent: false, step: 4, text: "Complete", totalStep: 4 }
    ]
  },
  component: UiStepper
} as Meta<typeof UiStepper>

export const Default: StoryFn<typeof UiStepper> = (args) => ({
  components: { UiStepper },
  setup() {
    const items = ref(args.items.map((item) => ({ ...item })))
    const currentStepIndex = ref(args.currentStepIndex)

    const handleStepClick = (index: number) => {
      currentStepIndex.value = index
    }

    return {
      currentStepIndex,
      handleStepClick,
      items
    }
  },
  template: `
    <UiStepper
      :items="items"
      :current-step-index="currentStepIndex"
      @UiStepper::click="handleStepClick"
    />
  `
})
