import { Meta, Title, Unstyled } from "@storybook/blocks"

<Meta title="Design Tokens/Texts" />

<Title>Texts</Title>

<Unstyled>
  <div className="demo-grid">
    <div className="demo-item demo-item--full">
      <h1 className="exp-heading-01">Latine/expressive/heading-01</h1>
    </div>
    <div className="demo-item demo-item--full">
      <h1 className="exp-heading-01-alt">Latine/expressive/heading-01-alt</h1>
    </div>
    <div className="demo-item demo-item--full">
      <h2 className="exp-heading-02">Latine/expressive/heading-02</h2>
    </div>
    <div className="demo-item demo-item--full">
      <h2 className="exp-heading-02-alt">Latine/expressive/heading-02-alt</h2>
    </div>
    <div className="demo-item demo-item--full">
      <h3 className="exp-heading-03">Latine/expressive/heading-03</h3>
    </div>
    <div className="demo-item demo-item--full">
      <h3 className="exp-heading-03-alt">Latine/expressive/heading-03-alt</h3>
    </div>
    <div className="demo-item demo-item--full">
      <h4 className="exp-heading-04">Latine/expressive/heading-04</h4>
    </div>
    <div className="demo-item demo-item--full">
      <h4 className="exp-heading-04-alt">Latine/expressive/heading-04-alt</h4>
    </div>
    <div className="demo-item demo-item--full">
      <h5 className="exp-heading-05">Latine/expressive/heading-05</h5>
    </div>
    <div className="demo-item demo-item--full">
      <h5 className="exp-heading-05-underline">Latine/expressive/heading-05-underline</h5>
    </div>
    <div className="demo-item demo-item--full">
      <h6 className="exp-heading-06">Latine/expressive/heading-06</h6>
    </div>
    <div className="demo-item demo-item--full">
      <p className="exp-subheading-01">Latine/expressive/subheading-01</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="exp-subheading-02">Latine/expressive/subheading-02</p>
    </div>
    <div className="demo-item demo-item--full">
      <h1 className="heading-01">Latine/ui/heading-01</h1>
    </div>
    <div className="demo-item demo-item--full">
      <h1 className="heading-01-underline">Latine/ui/heading-01-underline</h1>
    </div>
    <div className="demo-item demo-item--full">
      <h2 className="heading-02">Latine/ui/heading-02</h2>
    </div>
    <div className="demo-item demo-item--full">
      <h2 className="heading-02-underline">Latine/ui/heading-02-underline</h2>
    </div>
    <div className="demo-item demo-item--full">
      <h3 className="heading-03">Latine/ui/heading-03</h3>
    </div>
    <div className="demo-item demo-item--full">
      <h3 className="heading-03-underline">Latine/ui/heading-03-underline</h3>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-01">Latine/ui/body-01</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-01-strong">Latine/ui/body-01-strong</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-01-underline">Latine/ui/body-01-underline</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-01-uppercase">Latine/ui/body-01-uppercase</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-01-uppercase-strong">Latine/ui/body-01-uppercase-strong</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-02">Latine/ui/body-02</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-02-underline">Latine/ui/body-02-underline</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-02-strong">Latine/ui/body-02-strong</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="body-02-uppercase">Latine/ui/body-02-uppercase</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="label-01">Latine/ui/label-01</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="caption-01">Latine/ui/caption-01</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="caption-01-underline">Latine/ui/caption-01-underline</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="caption-01-strong">Latine/ui/caption-01-strong</p>
    </div>
    <div className="demo-item demo-item--full">
      <p className="caption-01-uppercase">Latine/ui/caption-01-uppercase</p>
    </div>
  </div>
</Unstyled>

##

## Sass token

```scss
// Font-size
$sizes: (
  "3xs": 1rem,
  "2xs": 1.2rem,
  "xs": 1.4rem,
  "s": 1.6rem,
  "m": 1.8rem,
  "l": 2rem,
  "xl": 2.4rem,
  "2xl": 2.8rem,
  "3xl": 3.2rem,
  "4xl": 4.8rem,
  "5xl": 6.4rem
);

$weight: (
  "light": 300,
  "regular": 400,
  "medium": 500,
  "semiBold": 600
);

$line-height: (
  "3xs": 1,
  "2xs": 1.12,
  "xs": 1.16,
  "s": 1.25,
  "m": 1.28,
  "l": 1.33,
  "xl": 1.4,
  "2xl": 1.42,
  "3xl": 1.44,
  "4xl": 1.5,
  "5xl": 1.57
);
```

## How to use them

### Utility Classes

Each global text style referenced in the Figma Design System is available as a utility class that can be used anywhere in the project.<br />
These utility classes contain all the necessary CSS properties to replicate the Design System:<br />
`font-family`, `font-size`, `font-weight`, `line-height`, etc.

```html
<h1 className="exp-heading-01">Latine/expressive/heading-01</h1>
<h1 className="exp-heading-01-alt">Latine/expressive/heading-01-alt</h1>
<h2 className="exp-heading-02">Latine/expressive/heading-02</h2>
<h2 className="exp-heading-02-alt">Latine/expressive/heading-02-alt</h2>
<h3 className="exp-heading-03">Latine/expressive/heading-03</h3>
<h3 className="exp-heading-03-alt">Latine/expressive/heading-03-alt</h3>
<h4 className="exp-heading-04">Latine/expressive/heading-04</h4>
<h4 className="exp-heading-04-alt">Latine/expressive/heading-04-alt</h4>
<h5 className="exp-heading-05">Latine/expressive/heading-05</h5>
<h5 className="exp-heading-05-underline">Latine/expressive/heading-05-underline</h5>
<h6 className="exp-heading-06">Latine/expressive/heading-06</h6>
<p className="exp-subheading-01">Latine/expressive/subheading-01</p>
<p className="exp-subheading-02">Latine/expressive/subheading-02</p>
<h1 className="heading-01">Latine/ui/heading-01</h1>
<h1 className="heading-01-underline">Latine/ui/heading-01-underline</h1>
<h2 className="heading-02">Latine/ui/heading-02</h2>
<h2 className="heading-02-underline">Latine/ui/heading-02-underline</h2>
<h3 className="heading-03">Latine/ui/heading-03</h3>
<h3 className="heading-03-underline">Latine/ui/heading-03-underline</h3>
<p className="body-01">Latine/ui/body-01</p>
<p className="body-01-strong">Latine/ui/body-01-strong</p>
<p className="body-01-underline">Latine/ui/body-01-underline</p>
<p className="body-01-uppercase">Latine/ui/body-01-uppercase</p>
<p className="body-01-uppercase-strong">Latine/ui/body-01-uppercase-strong</p>
<p className="body-02">Latine/ui/body-02</p>
<p className="body-02-underline">Latine/ui/body-02-underline</p>
<p className="body-02-strong">Latine/ui/body-02-strong</p>
<p className="body-02-uppercase">Latine/ui/body-02-uppercase</p>
<p className="label-01">Latine/ui/label-01</p>
<p className="caption-01">Latine/ui/caption-01</p>
<p className="caption-01-underline">Latine/ui/caption-01-underline</p>
<p className="caption-01-strong">Latine/ui/caption-01-strong</p>
<p className="caption-01-uppercase">Latine/ui/caption-01-uppercase</p>
```

There are also utility classes for the **`font-size`**, **`font-weight`**, and **`line-height`** properties, related to the design tokens, which can also be used throughout the project.<br />
**(This should remain exceptional. When possible, prefer the utility classes mentioned above.)**

### Stylesheet

In some cases, it may be useful to apply these font properties directly in a stylesheet rather than using a utility class.<br />
This can be done as follows:

```scss
@use "assets/styles/config/text" as text;

.my-class {
  font-size: map-get(text.$sizes, "s"); // font-size: 16px;
  font-weight: map-get(text.$weight, "medium"); // font-weight: 500;
  line-height: map-get(text.$line-height, "m"); // line-height: 1.28;
}
```
