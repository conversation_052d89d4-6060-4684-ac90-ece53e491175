import { Meta, Unstyled } from "@storybook/blocks"
import { Title } from "@storybook/blocks"

<Meta title="Design Tokens/Boxes" />

<Title>Boxes</Title>

## Examples

### Radius

<Unstyled>
  <div className="demo-grid label">
    <div className="demo-item demo-item--radius radius-none">radius-none (0px)</div>
    <div className="demo-item demo-item--radius radius-rounded">radius-rounded</div>
  </div>
</Unstyled>

### Borders

<Unstyled>
  <div className="demo-grid label">
    <div className="demo-item border-decorative">border-decorative</div>
    <div className="demo-item border-interactive">border-interactive</div>
    <div className="demo-item border-interactiveHover">border-interactiveHover</div>
    <div className="demo-item border-interactiveSelected">border-interactiveSelected</div>
  </div>
</Unstyled>

### Shadows

<Unstyled>
  <div className="demo-grid label">
    <div className="demo-item shadow-top">shadow-top</div>
    <div className="demo-item shadow-topStrong">shadow-topStrong</div>
    <div className="demo-item shadow-bottom">shadow-bottom</div>
    <div className="demo-item shadow-bottomStrong">shadow-bottomStrong</div>
    <div className="demo-item shadow-topBottom">shadow-topBottom</div>
  </div>
</Unstyled>

## Sass token

```scss
// Radius
$radii: (
  "none": 0px,
  "rounded": 9999px
);

// Borders
$borders: (
  "none": none,
  "decorative": 1px solid map.get(colors.$basics, "black"),
  "interactive": 1px solid map.get(colors.$neutral, "500"),
  "interactiveHover": 1px solid map.get(colors.$neutral, "600"),
  "interactiveSelected": 2px solid map.get(colors.$basics, "black")
);
```

## How to use them

### Utility Classes

Each global "Box" style referenced in the Figma Design System is available as a utility class that can be used anywhere in the project, as shown below:

```html
<!-- Radius --->
<div class="radius-none"></div>
<div class="radius-rounded"></div>
<!-- Border --->
<div class="border-decorative"></div>
<!-- The default color of a border is "black". It can be overridden with a utility class "border-{color}" -->
<div class="border-decorative border-blue-100"></div>
```

### Stylesheet

In some cases, it may be useful to apply these "Box" properties directly in a stylesheet rather than using a utility class.<br />
This can be done as follows:

```scss
@use "@sb-config/boxes";

.my-component {
  border-radius: map-get(boxes.$radii, "none"); // border-radius: 0px;
  border: map-get(boxes.$borders, "decorative"); // border: 1px solid #000;
}
```
