import { Meta, Unstyled } from "@storybook/blocks"
import { Title } from "@storybook/blocks"

<Meta title="Design Tokens/Layout" />

<Title>Layout</Title>

# The container

The project layout is based on a container with the following rules:

- **small** : Auto width with 16px outer margins
- **medium large** : Auto width with a maximum of 1280px and 32px outer margins
- **xlarge xxlarge** : Auto width with a maximum of 1920px and 64px outer margins

It can be used with the utility class **`container`**, and elements placed directly inside (must be a JSX.Element) are automatically positioned in the central part of the page.

<Unstyled>
  <div className="mb-5 pa-3 radius-m label" style={{ backgroundColor: "#eee" }}>
    Container
    <div className="container radius-m label">
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        Content
      </div>
    </div>
  </div>
</Unstyled>

# The grid

The pages of the project are built on a grid with the following rules:

- **Mobile** : 4 columns and 16px gutters
- **Tablette et Desktop** : 12 columns and 32px gutters

It can be used anywhere in the project with the utility class **`grid`**, and the elements inside this grid are **initially positioned** on this grid, with 1 element per column.

**This grid is meant to be a development aid but is not essential everywhere;** common sense should be used in its application.

<Unstyled>
  <div className="mb-5 px-3 py-1 radius-m label" style={{ backgroundColor: "#eee" }}>
    <div className="grid radius-m label">
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        1
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        2
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        3
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        4
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        5
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        6
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        7
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        8
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        9
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        10
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        11
      </div>
      <div className="demo-item radius-s grid-item" style={{ height: "300px" }}>
        12
      </div>
    </div>
  </div>
</Unstyled>

## Sass token

```scss
// Grid utility classes
.grid {
  --grid-columns-number: 4;
  --grid-gap: 16px;

  display: grid;
  grid-template-columns: repeat(var(--grid-columns-number), 1fr);
  column-gap: var(--grid-gap);

  @include mq.media(">=medium") {
    --grid-columns-number: 12;
    --grid-gap: 24px;
  }

  @include mq.media(">=large") {
    --grid-gap: 24px;
  }

  @include mq.media(">=xlarge") {
    --grid-gap: 24px;
  }
}
```

## Using the Grid

Since this grid is built on the **`display: grid`** CSS property, it is necessary to use the appropriate CSS properties to position our elements inside this grid.

You can find documentation on using the CSS properties related to `display: grid` on **[this page](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_grid_layout/Grid_layout_using_line-based_placement)**.

With a preference for:

- [The **`grid-column`** and **`grid-row`** shorthands](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_grid_layout/Grid_layout_using_line-based_placement#the_grid-column_and_grid-row_shorthands)
- [Using the **span** keyword](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_grid_layout/Grid_layout_using_line-based_placement#using_the_span_keyword)

### Examples

**Simple usage**

<Unstyled>
  <div className="mb-5 px-3 py-1 radius-m label" style={{ backgroundColor: "#eee" }}>
    <div className="grid radius-m label">
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "span 4" }}>
        1 (grid-column: span 4)
      </div>
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "span 4" }}>
        2 (grid-column: span 4)
      </div>
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "span 4" }}>
        3 (grid-column: span 4)
      </div>
    </div>
  </div>
</Unstyled>

**More complex usage on 2 dimensions**

<Unstyled>
  <div className="mb-5 px-3 pt-3 pb-1 radius-m label" style={{ backgroundColor: "#eee" }}>
    (grid-template-rows: repeat(2, 1fr))
    <div className="grid radius-m label" style={{ gridTemplateRows: "repeat(2, 1fr)" }}>
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "1 / span 6", gridRow: "1 / span 2" }}>
        1 (grid-column: 1 / span 6) (grid-row: 1 / span 2)
      </div>
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "5 / span 4", gridRow: 1 }}>
        2 (grid-column: 5 / span 4) (grid-row: 1)
      </div>
      <div className="demo-item radius-s grid-item" style={{ gridColumn: "span 4", gridRow: 1 }}>
        3 (grid-column: span 4) (grid-row: 1)
      </div>
    </div>
  </div>
</Unstyled>

<style>
  {`
  .grid-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    margin-bottom: 8px;
    padding-left: 0;
    padding-right: 0;
    box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.13)
  }
  `}
</style>
