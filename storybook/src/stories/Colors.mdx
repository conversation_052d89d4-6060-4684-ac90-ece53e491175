import { Meta, Title, ColorPalette, ColorItem, Description } from "@storybook/blocks"

<Meta title="Design Tokens/Colors" />

<Title>Colors</Title>

<ColorPalette>
  <ColorItem
    title="Brand/Pink"
    colors={{
      "pink-100": "#FFE6EF",
      "pink-200": "#FBACC8",
      "pink-300": "#F273A0",
      "pink-400": "#E53977",
      "pink-500": "#D4024D",
      "pink-600": "#BE0037",
      "pink-700": "#9E0024",
      "pink-800": "#6E0015",
      "pink-900": "#3D0009"
    }}
  />
  <ColorItem
    title="Brand/Stratos Blue"
    colors={{
      "stratosBlue-100": "#E9E8F2",
      "stratosBlue-200": "#C9C7DE",
      "stratosBlue-300": "#ABA7CA",
      "stratosBlue-400": "#8D88B4",
      "stratosBlue-500": "#706B9D",
      "stratosBlue-600": "#544E85",
      "stratosBlue-700": "#38326C",
      "stratosBlue-800": "#1E1852",
      "stratosBlue-900": "#050033"
    }}
  />
  <ColorItem
    title="Brand/Velvet"
    colors={{
      "velvet-100": "#EAEDF5",
      "velvet-200": "#BDC3DF",
      "velvet-300": "#919AC7",
      "velvet-400": "#6671AD",
      "velvet-500": "#3D4892",
      "velvet-600": "#2A347E",
      "velvet-700": "#1A2366",
      "velvet-800": "#0E154B",
      "velvet-900": "#06092C"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Caviar Black"
    colors={{
      "caviarBlack-100": "#F3F3F3",
      "caviarBlack-200": "#E3E3E3",
      "caviarBlack-300": "#C4C4C4",
      "caviarBlack-400": "#9E9E9E",
      "caviarBlack-500": "#6C6C6C",
      "caviarBlack-600": "#434343",
      "caviarBlack-700": "#3A3A3A",
      "caviarBlack-800": "#2F2F2F",
      "caviarBlack-900": "#000"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Porcelain White"
    colors={{
      "porcelainWhite-100": "#FFFFFF",
      "porcelainWhite-200": "#EFEFEF",
      "porcelainWhite-300": "#DFDFDF",
      "porcelainWhite-400": "#CFCFCF",
      "porcelainWhite-500": "#BFBFBF",
      "porcelainWhite-600": "#AFAFAF",
      "porcelainWhite-700": "#9F9F9F",
      "porcelainWhite-800": "#8F8F8F",
      "porcelainWhite-900": "#808080"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Mist Grey"
    colors={{
      "mistGrey-100": "#E8E8E8",
      "mistGrey-200": "#CFCFCF",
      "mistGrey-300": "#B5B5B5",
      "mistGrey-400": "#9C9C9C",
      "mistGrey-500": "#828282",
      "mistGrey-600": "#696969",
      "mistGrey-700": "#4F4F4F",
      "mistGrey-800": "#363636",
      "mistGrey-900": "#1C1C1C"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Warm Grey"
    colors={{
      "warmGrey-100": "#F9F8F7",
      "warmGrey-200": "#F1EFED",
      "warmGrey-300": "#DBD7D5",
      "warmGrey-400": "#C7C0BC",
      "warmGrey-500": "#B0A5A0",
      "warmGrey-600": "#9A8D87",
      "warmGrey-700": "#81766F",
      "warmGrey-800": "#564C47",
      "warmGrey-900": "#443F3C"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Pearl Grey"
    colors={{
      "pearlGrey-100": "#FBFBFC",
      "pearlGrey-200": "#F7F7F7",
      "pearlGrey-300": "#EEEFF0",
      "pearlGrey-400": "#E6E6E8",
      "pearlGrey-500": "#D8D9DB",
      "pearlGrey-600": "#CCCCCE",
      "pearlGrey-700": "#B0B1B3",
      "pearlGrey-800": "#909192",
      "pearlGrey-900": "#666667"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Russian Blue"
    colors={{
      "russianBlue-500": "#3B556C"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/British Racing Green"
    colors={{
      "britishRacingGreen-500": "#00430E"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Amber"
    colors={{
      "amber-500": "#B19143"
    }}
  />
  <ColorItem
    title="Brand/Fairmont/Chestnut"
    colors={{
      "chestnut-500": "#764732"
    }}
  />
  <ColorItem
    title="Brand/Neutral"
    colors={{
      "neutral-100": "#F4F4F5",
      "neutral-200": "#DADADD",
      "neutral-300": "#C0BFC4",
      "neutral-400": "#A5A5AC",
      "neutral-500": "#8B8A93",
      "neutral-600": "#6F6E77",
      "neutral-700": "#575661",
      "neutral-800": "#3E3D48",
      "neutral-900": "#252339"
    }}
  />
  <ColorItem
    title="Brand/Neutral/White"
    subtitle="Part 1"
    colors={{
      absoluteWhite: "#FFF",
      "white-10": "rgba(255,255,255,0.1)",
      "white-20": "rgba(255,255,255,0.2)",
      "white-30": "rgba(255,255,255,0.3)",
      "white-40": "rgba(255,255,255,0.4)"
    }}
  />
  <ColorItem
    title="Brand/Neutral/White"
    subtitle="Part 2"
    colors={{
      "white-50": "rgba(255,255,255,0.5)",
      "white-60": "rgba(255,255,255,0.6)",
      "white-70": "rgba(255,255,255,0.7)",
      "white-80": "rgba(255,255,255,0.8)",
      "white-90": "rgba(255,255,255,0.9)"
    }}
  />
  <ColorItem
    title="Brand/Neutral/Black"
    subtitle="Part 1"
    colors={{
      absoluteBlack: "#000",
      "black-10": "rgba(0,0,0,0.1)",
      "black-20": "rgba(0,0,0,0.2)",
      "black-30": "rgba(0,0,0,0.3)",
      "black-40": "rgba(0,0,0,0.4)"
    }}
  />
  <ColorItem
    title="Brand/Neutral/Black"
    subtitle="Part 2"
    colors={{
      "black-50": "rgba(0,0,0,0.5)",
      "black-60": "rgba(0,0,0,0.6)",
      "black-70": "rgba(0,0,0,0.7)",
      "black-80": "rgba(0,0,0,0.8)",
      "black-90": "rgba(0,0,0,0.9)"
    }}
  />
  <ColorItem
    title="Functional/Red"
    colors={{
      "red-100": "#FBE6E7",
      "red-200": "#EEAFB1",
      "red-300": "#E07B7C",
      "red-400": "#CE4A49",
      "red-500": "#BA1F1A",
      "red-600": "#A50D07",
      "red-700": "#890100",
      "red-800": "#670000",
      "red-900": "#3D0000"
    }}
  />
  <ColorItem
    title="Functional/Yellow"
    colors={{
      "yellow-100": "#FEF7DF",
      "yellow-200": "#FCEEC0",
      "yellow-300": "#F9E4A1",
      "yellow-400": "#F6DB83",
      "yellow-500": "#F2D166",
      "yellow-600": "#CCAB45",
      "yellow-700": "#99832B",
      "yellow-800": "#665916",
      "yellow-900": "#332D0F"
    }}
  />
  <ColorItem
    title="Functional/Green"
    colors={{
      "green-100": "#E6F3EB",
      "green-200": "#AED7C0",
      "green-300": "#78B997",
      "green-400": "#44996E",
      "green-500": "#117846",
      "green-600": "#056A3A",
      "green-700": "#00582D",
      "green-800": "#004220",
      "green-900": "#002813"
    }}
  />
  <ColorItem
    title="Functional/Blue"
    colors={{
      "blue-100": "#E6F1F8",
      "blue-200": "#ADD1E6",
      "blue-300": "#77AED2",
      "blue-400": "#438ABC",
      "blue-600": "#025291",
      "blue-700": "#003E79",
      "blue-900": "#001836"
    }}
  />
  <ColorItem
    title="Functional/Orange"
    colors={{
      "orange-500": "#BB5C19"
    }}
  />
  <ColorItem
    title="Loyalty Status/Silver"
    colors={{
      "silver-100": "#E4E4E5",
      "silver-200": "#C8C9CA",
      "silver-300": "#ADADAF",
      "silver-400": "#919295",
      "silver-500": "#76777A",
      "silver-600": "#5F6063",
      "silver-700": "#49494B",
      "silver-800": "#323234",
      "silver-900": "#1B1B1C"
    }}
  />
  <ColorItem
    title="Loyalty Status/Gold"
    colors={{
      "gold-100": "#F3E7C6",
      "gold-200": "#E6D49E",
      "gold-300": "#D7BF7A",
      "gold-400": "#C4A963",
      "gold-500": "#AF913A",
      "gold-600": "#8D752F",
      "gold-700": "#6C5924",
      "gold-800": "#4A3D19",
      "gold-900": "#28210D"
    }}
  />
  <ColorItem
    title="Loyalty Status/Platinium"
    colors={{
      "platinium-100": "#BEC2C6",
      "platinium-200": "#A8ACB2",
      "platinium-300": "#91969D",
      "platinium-400": "#7B8188",
      "platinium-500": "#656B73",
      "platinium-600": "#4F565E",
      "platinium-700": "#394049",
      "platinium-800": "#23272D",
      "platinium-900": "#0D0F11"
    }}
  />
  <ColorItem
    title="Loyalty Status/Diamond"
    colors={{
      "diamond-100": "#D9D9D9",
      "diamond-200": "#D1D1D1",
      "diamond-300": "#CACACA",
      "diamond-400": "#C2C2C2",
      "diamond-500": "#BABABA",
      "diamond-600": "#969696",
      "diamond-700": "#727272",
      "diamond-800": "#4F4F4F",
      "diamond-900": "#2B2B2B"
    }}
  />
  <ColorItem
    title="Loyalty Status/Black"
    colors={{
      "loyaltyBlack-100": "#E0E0E0",
      "loyaltyBlack-200": "#C4C4C4",
      "loyaltyBlack-300": "#A8A8A8",
      "loyaltyBlack-400": "#8C8C8C",
      "loyaltyBlack-500": "#707070",
      "loyaltyBlack-600": "#545454",
      "loyaltyBlack-700": "#383838",
      "loyaltyBlack-800": "#1C1C1C",
      "loyaltyBlack-900": "#000"
    }}
  />
  <ColorItem title="Basics" colors={{ black: "#000", white: "#FFF", transparent: "transparent" }} />
</ColorPalette>

## Sass token

```scss
// Colors variables maps

// Brand
$pink: (
  "100": #ffe6ef,
  "200": #fbacc8,
  "300": #f273a0,
  "400": #e53977,
  "500": #d4024d,
  "600": #be0037,
  "700": #9e0024,
  "800": #6e0015,
  "900": #3d0009
);

$stratosBlue: (
  "100": #e9e8f2,
  "200": #c9c7de,
  "300": #aba7ca,
  "400": #8d88b4,
  "500": #706b9d,
  "600": #544e85,
  "700": #38326c,
  "800": #1e1852,
  "900": #050033
);

$velvet: (
  "100": #eaedf5,
  "200": #bdc3df,
  "300": #919ac7,
  "400": #6671ad,
  "500": #3d4892,
  "600": #2a347e,
  "700": #1a2366,
  "800": #0e154b,
  "900": #06092c
);

// Brand/Fairmont
$caviarBlack: (
  "100": #f3f3f3,
  "200": #e3e3e3,
  "300": #c4c4c4,
  "400": #9e9e9e,
  "500": #6c6c6c,
  "600": #434343,
  "700": #3a3a3a,
  "800": #2f2f2f,
  "900": #000
);

$porcelainWhite: (
  "100": #ffffff,
  "200": #efefef,
  "300": #dfdfdf,
  "400": #cfcfcf,
  "500": #bfbfbf,
  "600": #afafaf,
  "700": #9f9f9f,
  "800": #8f8f8f,
  "900": #808080
);

$mistGrey: (
  "100": #e8e8e8,
  "200": #cfcfcf,
  "300": #b5b5b5,
  "400": #9c9c9c,
  "500": #828282,
  "600": #696969,
  "700": #4f4f4f,
  "800": #363636,
  "900": #1c1c1c
);

$warmGrey: (
  "100": #f9f8f7,
  "200": #f1efed,
  "300": #dbd7d5,
  "400": #c7c0bc,
  "500": #b0a5a0,
  "600": #9a8d87,
  "700": #81766f,
  "800": #564c47,
  "900": #443f3c
);

$pearlGrey: (
  "100": #fbfbfc,
  "200": #f7f7f7,
  "300": #eeeff0,
  "400": #e6e6e8,
  "500": #d8d9db,
  "600": #ccccce,
  "700": #b0b1b3,
  "800": #909192,
  "900": #666667
);

$russianBlue: (
  "500": #3b556c
);

$britishRacingGreen: (
  "500": #00430e
);

$amber: (
  "500": #b19143
);

$chestnut: (
  "500": #764732
);

// Brand/Neutral
$neutral: (
  "100": #f4f4f5,
  "200": #dadadd,
  "300": #c0bfc4,
  "400": #a5a5ac,
  "500": #8b8a93,
  "600": #6f6e77,
  "700": #575661,
  "800": #3e3d48,
  "900": #252339
);

// Brand/Neutral/White
$absoluteWhite: #fff;
$white: (
  "10": rgba($absoluteWhite, 0.1),
  "20": rgba($absoluteWhite, 0.2),
  "30": rgba($absoluteWhite, 0.3),
  "40": rgba($absoluteWhite, 0.4),
  "50": rgba($absoluteWhite, 0.5),
  "60": rgba($absoluteWhite, 0.6),
  "70": rgba($absoluteWhite, 0.7),
  "80": rgba($absoluteWhite, 0.8),
  "90": rgba($absoluteWhite, 0.9)
);

// Brand/Neutral/Black
$absoluteBlack: #000;
$black: (
  "10": rgba($absoluteBlack, 0.1),
  "20": rgba($absoluteBlack, 0.2),
  "30": rgba($absoluteBlack, 0.3),
  "40": rgba($absoluteBlack, 0.4),
  "50": rgba($absoluteBlack, 0.5),
  "60": rgba($absoluteBlack, 0.6),
  "70": rgba($absoluteBlack, 0.7),
  "80": rgba($absoluteBlack, 0.8),
  "90": rgba($absoluteBlack, 0.9)
);

// Functional
$red: (
  "100": #fbe6e7,
  "200": #eeafb1,
  "300": #e07b7c,
  "400": #ce4a49,
  "500": #ba1f1a,
  "600": #a50d07,
  "700": #890100,
  "800": #670000,
  "900": #3d0000
);

$yellow: (
  "100": #fef7df,
  "200": #fceec0,
  "300": #f9e4a1,
  "400": #f6db83,
  "500": #f2d166,
  "600": #ccab45,
  "700": #99832b,
  "800": #665916,
  "900": #332d0f
);

$green: (
  "100": #e6f3eb,
  "200": #aed7c0,
  "300": #78b997,
  "400": #44996e,
  "500": #117846,
  "600": #056a3a,
  "700": #00582d,
  "800": #004220,
  "900": #002813
);

$blue: (
  "100": #e6f1f8,
  "200": #add1e6,
  "300": #77aed2,
  "400": #438abc,
  "600": #025291,
  "700": #003e79,
  "900": #001836
);

$orange: (
  "500": #bb5c19
);

// Loyalty Status
$silver: (
  "100": #e4e4e5,
  "200": #c8c9ca,
  "300": #adadaf,
  "400": #919295,
  "500": #76777a,
  "600": #5f6063,
  "700": #49494b,
  "800": #323234,
  "900": #1b1b1c
);

$gold: (
  "100": #f3e7c6,
  "200": #e6d49e,
  "300": #d7bf7a,
  "400": #c4a963,
  "500": #af913a,
  "600": #8d752f,
  "700": #6c5924,
  "800": #4a3d19,
  "900": #28210d
);

$platinium: (
  "100": #bec2c6,
  "200": #a8acb2,
  "300": #91969d,
  "400": #7b8188,
  "500": #656b73,
  "600": #4f565e,
  "700": #394049,
  "800": #23272d,
  "900": #0d0f11
);

$diamond: (
  "100": #d9d9d9,
  "200": #d1d1d1,
  "300": #cacaca,
  "400": #c2c2c2,
  "500": #bababa,
  "600": #969696,
  "700": #727272,
  "800": #4f4f4f,
  "900": #2b2b2b
);

$loyaltyBlack: (
  "100": #e0e0e0,
  "200": #c4c4c4,
  "300": #a8a8a8,
  "400": #8c8c8c,
  "500": #707070,
  "600": #545454,
  "700": #383838,
  "800": #1c1c1c,
  "900": #000
);

// Basics
$basics: (
  "black": $absoluteBlack,
  "white": $absoluteWhite,
  "transparent": transparent
);
```

## How to use them

### Utility Classes

Each of these colors can be used as "color," "background-color," or "border-color" using utility classes.

```html
<!-- Color --->
<div class="color-black"></div>
<div class="color-white"></div>
<div class="color-blue-100"></div>
<!-- Background color --->
<div class="bg-black"></div>
<div class="bg-white"></div>
<div class="bg-blue-100"></div>
<!-- Border color --->
<div class="border-black"></div>
<div class="border-white"></div>
<div class="border-blue-100"></div>
...
```

### Stylesheet

In some cases, it may be useful to apply colors directly in a stylesheet rather than using a utility class.<br />
This can be done as follows:

```scss
@use "@sb-config/colors";

.my-class {
  color: map-get(colors.$basics, "black"); // color: #000;
  background-color: map-get(colors.$blue, 100); // background-color: #E6F1F8;
  any-color-property: map-get(colors.$green, 300); // my-color-property: #78B997;
}
```

### Additional Colors

You may find colors in the mockups that are not referenced here, as they are specific to a component.
In this case, you should create a color variable directly at the top of the component's stylesheet:

```scss
$other-color: #c7b3e6;

.my-component-class {
  color: $other-color; // color: #C7B3E6;
}
```
