import { Meta, Title, Unstyled } from "@storybook/blocks"

<Meta title="Design Tokens/Spaces" />

<Title>Spaces</Title>

## Examples

<Unstyled>
  <div className="demo-grid radius-m label">
    <div className="demo-block">
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-1" />
        <span>space-1 = 0px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-2" />
        <span>space-2 = 2px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-3" />
        <span>space-3 = 4px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-4" />
        <span>space-4 = 8px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-5" />
        <span>space-5 = 12px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-6" />
        <span>space-6 = 16px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-7" />
        <span>space-7 = 24px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-8" />
        <span>space-8 = 32px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-9" />
        <span>space-9 = 40px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-10" />
        <span>space-10 = 48px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-11" />
        <span>space-11 = 64px</span>
      </div>
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-12" />
        <span>space-12 = 80px</span>
      </div>{' '}
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-13" />
        <span>space-13 = 96px</span>
      </div>{' '}
      <div className="w-item radius-s">
        <div className="w-indicator radius-s pl-14" />
        <span>space-14 = 128px</span>
      </div>
      <div className="w-item w-item--full radius-s">
        <span>space-auto = auto</span>
      </div>
    </div>

  </div>
</Unstyled>

## Sass token

```scss
$size-base-unit: 0.4rem;
// legacy map, keep it to avoid big refactor
$sizes: (
  "auto": auto,
  "1": $size-base-unit * 0,
  //0px
  "2": $size-base-unit * 0.5,
  // 2px
  "3": $size-base-unit * 1,
  // 4px
  "4": $size-base-unit * 2,
  // 8px
  "5": $size-base-unit * 3,
  // 12px
  "6": $size-base-unit * 4,
  // 16px
  "7": $size-base-unit * 6,
  // 24px
  "8": $size-base-unit * 8,
  // 32px
  "9": $size-base-unit * 10,
  // 40px
  "10": $size-base-unit * 12,
  // 48px
  "11": $size-base-unit * 16,
  // 64px
  "12": $size-base-unit * 20,
  // 80px
  "13": $size-base-unit * 24,
  // 96px
  "14": $size-base-unit * 32 // 128px,
);
```

## How to use them

### Utility classes

Utility classes apply a **margin**, **padding**, or **gap** to an element ranging from 0 to 11.<br />
These classes can be applied using the following format: `{property}{direction}-{size}`.

**`{property}`** specifies the type of spacing:

- m — applies **margin**
- p — applies **padding**
- g — applies **gap**

**`{direction}`** indicates the side to which the property is applied:

- t — applies spacing on **margin-block-start** and **padding-block-start**
- b — applies spacing on **margin-block-end** and **padding-block-end**
- l — applies spacing on **margin-inline-start** and **padding-inline-start**
- r — applies spacing on **margin-inline-end** and **padding-inline-end**
- x — applies spacing on **margin-inline** and **padding-inline** or **column-gap**
- y — applies spacing on **margin-block** and **padding-block** or **row-gap**
- a — applies spacing in **all directions** for the chosen property

```html
<!-- Margin --->
<div className="ma-1"></div>
<div className="mx-1"></div>
<div className="my-1"></div>
<div className="mt-1"></div>
<div className="mr-1"></div>
<div className="mb-1"></div>
<div className="ml-1"></div>
<!-- Padding --->
<div className="pa-1"></div>
<div className="px-1"></div>
<div className="py-1"></div>
<div className="pt-1"></div>
<div className="pr-1"></div>
<div className="pb-1"></div>
<div className="pl-1"></div>
<!-- Gap --->
<div className="g-1"></div>
<div className="gx-1"></div>
<div className="gy-1"></div>
```

### Responsive Design

It is possible to use the names of the breakpoints defined in the project (file 'utilities/mq.scss') to apply spacing based on a specific screen size.<br/>
To do this, include the breakpoint name in the utility class using the following format: **`{property}{direction}-{breakpoint}-{size}`**.

Here is an example using the project's default breakpoints:

```scss
// utilities/_mq.scss
$breakpoints-list: (
  "sm": 768px,
  "md": 992px,
  "lg": 1280px
) !default;
```

```html
<div className="ma-2 ma-md-4"></div>
<!-- default => margin: 8px; -->
<!-- @media (min-width: 992px) => margin: 16px; -->
```

### Stylesheet

In some cases, it may be useful to apply spacing directly in a stylesheet rather than using a utility class.<br />
You can do this as follows:

```scss
@use "sass:map";
@use "@sb-config/spaces";

.my-class {
  margin: map.get(spaces.$sizes, "5"); // margin: 24px;
}
```

<style>
  {`
  .w-item {
    display: flex;
    gap: 4px;
    height: 20px;
    margin-top: 5px;
  }
  .w-item--full {
    flex-basis: 100%;
  }
  .w-item span {
    align-self: center;
  }
  .w-indicator {
    background-color: #000;
    width: 0;
  }
  `}
</style>
