import { Meta, Title, Unstyled } from "@storybook/blocks"

<Meta title="Help/Add an Icon" />

<Title>Add an Icon</Title>

Before adding a new icon to the project:<br/> **always make sure that it is not already present in the [Iconography](/story/iconography--iconography) documentation**

Here are the steps to add an icon to the project's icon list:

## 1. Download the icon from the UI Kit section in the Figma mockup

Simply select the icon you want to download.<br />
**(Make sure to select the parent layer, which should be square, and keep the empty space around the icon).**

Once the icon is selected in the mockup:

- Go to the **"Export"** section in the right sidebar.
- Click the **"+" button**.
- Select the **SVG** format from the dropdown list.
- Open the **"Preview"** dropdown to ensure you are exporting what you want.
- Click the **"Export"** button.

## 2. Clean up the icon using the [SVG OMG](https://jakearchibald.github.io/svgomg/) tool

You can clean and optimize the icon with this tool.

- Drag and drop the icon file you just downloaded into the window.
- Make sure the **"Prefer viewBox to width/height"** option is enabled.
- Re-download the cleaned icon.
- Rename this new file with the icon name specified in the **UI Kit** (if applicable).

## 3. Add the icon to the project

- Add your icon to the **"storybook/assets/icons"** folder.
- Open it in your code editor.
- Icons (monochrome) that you want to control the color via its parent color or another CSS property **should not contain "fill" or "stroke" properties**. If they do, remove them (the icon should appear black in your editor's preview).
- For icons that have a fixed color, you can leave the existing color properties.

## 4. Make the icon accessible in your components

We use the **[vite-svg-loader](https://github.com/jpkleemans/vite-svg-loader)** loader to be able to use the icons as Vue components.<br />
In the **"index.ts"** file, import your icon and add the component based on it to the **"iconMapping"** object (key in kebab case), and be sure to organize them by group.

```js
...
import ChevronThin from "./chevron-thin.svg"

export const iconMapping = {
  "chevron-thin": ChevronThin
}
...
```

## 5. Use an icon in the project

To use an icon anywhere in the project, use the dedicated **[Atoms/UiIcon](/docs/components-atoms-uiicon--docs)** component.<br/>
This component takes a **"name"** parameter from the enum defined earlier.

```js
...
import UiIcon from "@sb-components/atoms/uiIcon/uiIcon";
...
<template>
  <atoms-ui-icon name="chevron-thin" />
</template>
```

You will likely want the icon to be passed from a prop in certain components. In this case, the prop should be typed as shown in the example below:

```ts
import { Icon } from "@sb-assets/icons";

export interface MyComponentProps {
  ...
  picto: Icon;
  ...
}

<template>
  <atoms-ui-icon :name="picto" />
</template>
```

![Good Job !](https://media.giphy.com/media/XreQmk7ETCak0/giphy.gif)
