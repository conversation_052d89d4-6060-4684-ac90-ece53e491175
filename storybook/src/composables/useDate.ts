import { i18n } from "../i18n"

export const useDate = () => {
  const formatDateToLocale = (
    date: Date | string | undefined,
    outputFormat: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    }
  ) => {
    if (!date) return null

    return new Date(date).toLocaleDateString(i18n.global.locale.value, outputFormat)
  }

  const formatTimeToLocale = (
    time: string | undefined,
    outputFormat: Intl.DateTimeFormatOptions = {
      hour: "numeric"
    }
  ) => {
    if (!time) return null

    const date = new Date()
    // Hour format from the API should be "09:00"
    date.setHours(Number(time.split(":")[0]))
    return new Date(date).toLocaleTimeString(i18n.global.locale.value, outputFormat)
  }

  return {
    formatDateToLocale,
    formatTimeToLocale
  }
}
