import { DateMoment } from "../components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { useI18n } from "vue-i18n"

export const useDatePicker = () => {
  const { t, locale } = useI18n()
  const dateFormat: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
    weekday: "short"
  }

  const calendarHeaderFormat: Intl.DateTimeFormatOptions = { month: "long", year: "numeric" }

  const computedDate = (date: Date | null, returnNull: boolean = false) => {
    if (!date) return returnNull ? null : "-"
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const todayTime = today.getTime()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const tomorrowTime = tomorrow.getTime()

    const selectedDate = new Date(date)
    selectedDate.setHours(0, 0, 0, 0)
    switch (selectedDate.getTime()) {
      case todayTime:
        return t("global.today")
      case tomorrowTime:
        return t("global.tomorrow")
      default:
        return new Date(selectedDate).toLocaleDateString(locale.value, dateFormat)
    }
  }

  const handleChangeDate = ({
    startDate,
    endDate,
    dates,
    currentFocused,
    date,
    maxRange
  }: {
    startDate: string | null
    endDate: string | null
    dates: Date[]
    currentFocused: DateMoment | null
    date: Date
    maxRange?: number
  }) => {
    const updatedValue = {
      currentFocused,
      dates,
      endDate,
      startDate
    }

    const tempStartDate = startDate ? dates[DateMoment.START] : null
    const tempEndDate = endDate ? dates[DateMoment.END] || dates[DateMoment.START] : null
    const tempDate = date

    tempStartDate?.setHours(0, 0, 0, 0)
    tempEndDate?.setHours(0, 0, 0, 0)
    tempDate.setHours(0, 0, 0, 0)

    if (currentFocused === DateMoment.START) {
      if (dates.length >= 1) {
        if (tempStartDate) {
          updatedValue.dates.shift()
          updatedValue.dates.unshift(tempDate)
          updatedValue.startDate = computedDate(tempDate, true)

          if (!dates[DateMoment.END]) {
            updatedValue.currentFocused = DateMoment.END
          }
        }

        if (
          tempEndDate &&
          (tempEndDate.getTime() <= tempDate.getTime() ||
            (maxRange && tempEndDate.getTime() >= tempDate.getTime() + maxRange * 24 * 60 * 60 * 1000))
        ) {
          updatedValue.dates = [tempDate]
          updatedValue.endDate = null
          updatedValue.currentFocused = DateMoment.END
        }

        if (!updatedValue.startDate && tempEndDate && tempEndDate.getTime() > tempDate.getTime()) {
          updatedValue.dates.unshift(date)
          updatedValue.startDate = computedDate(tempDate, true)
        }
      } else {
        updatedValue.dates.push(tempDate)
        updatedValue.startDate = computedDate(tempDate, true)

        updatedValue.currentFocused = DateMoment.END
      }

      updatedValue.startDate = computedDate(tempDate, true)
    } else if (currentFocused === DateMoment.END) {
      if (!dates.length) {
        updatedValue.dates.push(tempDate)
        updatedValue.endDate = computedDate(tempDate, true)
        updatedValue.currentFocused = DateMoment.START
      } else if (tempStartDate && tempStartDate.getTime() > tempDate.getTime()) {
        updatedValue.dates = [tempDate]
        updatedValue.startDate = computedDate(tempDate, true)
        updatedValue.endDate = null
      } else if (tempStartDate && tempStartDate.getTime() === tempDate.getTime()) {
        updatedValue.dates = [tempDate]
        updatedValue.startDate = null
        updatedValue.endDate = computedDate(tempDate, true)
        updatedValue.currentFocused = DateMoment.START
      } else {
        updatedValue.dates[DateMoment.END] = tempDate
        updatedValue.endDate = computedDate(tempDate, true)
      }
    }

    return updatedValue
  }

  const checkIfDateIsCorrect = (input: string) => new Date(input) instanceof Date && !isNaN(new Date(input).getTime())

  const changeDateAfterInput = (
    date: DateMoment,
    event: Event,
    maxDate: Date | undefined,
    startDate: string | null,
    endDate: string | null,
    dates: Date[]
  ) => {
    const updatedValue = {
      dates,
      endDate,
      startDate
    }

    const currentUpdatingDate = dates[date]
    const formattedCurrentUpdatingDate = computedDate(currentUpdatingDate, true)

    if (formattedCurrentUpdatingDate === (event.target as HTMLInputElement).value) return

    if (
      !checkIfDateIsCorrect((event.target as HTMLInputElement)?.value) ||
      (maxDate && new Date((event.target as HTMLInputElement)?.value).getTime() >= maxDate.getTime())
    ) {
      if (date === DateMoment.START) {
        updatedValue.startDate = computedDate(dates[DateMoment.START], true)
      }

      if (date === DateMoment.END && dates[DateMoment.START] && dates[DateMoment.END]) {
        updatedValue.endDate = computedDate(dates[DateMoment.END], true)
      } else if (date === DateMoment.END && !startDate) {
        updatedValue.endDate = computedDate(dates[DateMoment.START], true)
      }

      return updatedValue
    }

    if (date === DateMoment.START) {
      updatedValue.startDate = computedDate(new Date((event.target as HTMLInputElement)?.value), true)
      updatedValue.dates.unshift(new Date((event.target as HTMLInputElement)?.value))
    } else {
      updatedValue.endDate = computedDate(new Date((event.target as HTMLInputElement)?.value), true)
      updatedValue.dates.push(new Date((event.target as HTMLInputElement)?.value))
    }

    return updatedValue
  }

  const setDay = (date: string) => {
    const currentDate = new Date()
    let convertedDate: Date

    // handles 'today' and 'tomorrow' cases
    if (date === t("global.today")) {
      convertedDate = currentDate
    } else if (date === t("global.tomorrow")) {
      convertedDate = new Date(currentDate.setDate(currentDate.getDate() + 1))
    } else {
      convertedDate = new Date(date)
    }

    return convertedDate as Date
  }

  return {
    calendarHeaderFormat,
    changeDateAfterInput,
    computedDate,
    dateFormat,
    handleChangeDate,
    setDay
  }
}
