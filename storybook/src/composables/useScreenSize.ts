import { computed } from "vue"
import { useWindowSize } from "@vueuse/core"

export const useCurrentWindowSize = () => {
  const { width } = useWindowSize()

  const isDesktop = computed(() => {
    return width.value !== Infinity ? width.value >= Breakpoints.md : false
  })

  const isMobile = computed(() => {
    return width.value !== Infinity ? width.value < Breakpoints.sm : false
  })

  return {
    isDesktop,
    isMobile
  }
}

/* eslint-disable sort-keys */
export const Breakpoints = {
  sm: 768,
  md: 1024,
  lg: 1280,
  xlg: 1440,
  xxlg: 1920
}
