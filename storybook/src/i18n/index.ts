import { type I18nOptions, createI18n } from "vue-i18n"

import storybookDeMessages from "./locales/de.json"
import storybookEnMessages from "./locales/en.json"
import storybookEsMessages from "./locales/es.json"
import storybookFrMessages from "./locales/fr.json"
import storybookJaMessages from "./locales/ja.json"
import storybookZhMessages from "./locales/zh.json"

const options: I18nOptions = {
  fallbackLocale: "en",
  globalInjection: true,
  legacy: false,
  locale: "en",
  messages: {
    de: storybookDeMessages,
    en: storybookEnMessages,
    es: storybookEsMessages,
    fr: storybookFrMessages,
    ja: storybookJaMessages,
    zh: storybookZhMessages
  },
  pluralRules: {}
}

export const i18n = createI18n<false, typeof options>(options)

export {
  storybookDeMessages,
  storybookEnMessages,
  storybookEsMessages,
  storybookFrMessages,
  storybookJaMessages,
  storybookZhMessages
}
