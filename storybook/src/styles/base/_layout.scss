/*! layout */

@use "@sb-utilities/mq";

.container {
  --layout-width: 1340px;
  --layout-padding: 16px;

  display: grid;
  grid-template-columns:
    1fr min(calc(100% - var(--layout-padding) * 2), var(--layout-width))
    1fr;

  & > * {
    grid-column: 2 / -2;
  }

  @include mq.media(">=small") {
    --layout-padding: 32px;

    grid-template-columns:
      1fr min(calc(100% - var(--layout-padding) * 2), var(--layout-width))
      1fr;
  }

  @include mq.media(">=large") {
    --layout-padding: 64px;

    grid-template-columns:
      1fr min(calc(100% - var(--layout-padding) * 2), var(--layout-width))
      1fr;
  }
}

.wrapped-content {
  grid-column: 2 / -2;
}

.fullwidth-content {
  grid-column: 1 / -1;
}

// Grid utility classes
.grid {
  --grid-columns-number: 4;
  --grid-gap: 16px;

  display: grid;
  grid-template-columns: repeat(var(--grid-columns-number), 1fr);
  column-gap: var(--grid-gap);

  @include mq.media(">=small") {
    --grid-columns-number: 12;
    --grid-gap: 24px;
  }
}
