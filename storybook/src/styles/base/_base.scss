@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/fonts";
@use "@sb-utilities/text";

// -----------------------------------------------------------------------------
// This file contains very basic styles.
// -----------------------------------------------------------------------------

/**
 * Make all elements from the DOM inherit from the parent box-sizing
 * Since `*` has a specificity of 0, it does not override the `html` value
 * making all elements inheriting from the root box-sizing value
 * See: https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
*,
*::before,
*::after {
  box-sizing: inherit;
  -webkit-tap-highlight-color: transparent;
}

/**
 * This CSS code block targets users who prefer reduced motion due
 * to accessibility concerns or personal preference. It modifies the
 * style of elements to minimize motion, using the following properties:
 *
 * 1) transition-duration and animation-duration: Set to 0.001ms (virtually
 * instant) to effectively disable animations/transitions. Using 0.001ms
 * instead of 0 avoids browser bugs that may cause a jarring user experience,
 * where all animations are abruptly removed, rather than smoothly disabled.
 *
 * 2) background-attachment: Set to 'initial' to prevent fixed or scrolling
 * background images, reducing motion.
 *
 * 3) scroll-behavior: Set to 'auto' to disable smooth scrolling effects.
 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    transition-duration: 0.001ms !important;
    animation-duration: 0.001ms !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
  }
}

// Additionnal reset styles for very common stuff
// Normalize tries to only remove differences between browsers.
// If we want an actual reset, we need to reset the styles of
// just a handful of elements.

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: inherit;
}

p,
ul,
ol,
figure {
  margin: 0;
}

ul,
ol {
  list-style: none;
  padding-left: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

// Reset button styles.
// It takes a bit of work to achieve a neutral look.
button {
  padding: 0;
  border: none;
  font: inherit;
  color: inherit;
  background-color: transparent;
  // show a hand cursor on hover
  cursor: pointer;
}

input {
  display: inline-block;
  width: 100%;
  font: inherit;
}

details > summary {
  cursor: pointer;
  user-select: none;
}

// Here we contradict Normalize
fieldset {
  margin: 0;
  border: 0;
  padding: 0;
}

// Force images and frames to fit their container at most
img,
iframe {
  max-width: 100%;
}

// Other small fixes
::-ms-clear {
  display: none;
}

// Document
html {
  // Set up a decent box model on the root element
  box-sizing: border-box;
  // Map root font-size to 10px, so that 1rem = 10px
  font-size: 0.625em; /* fallback IE8+ */
  font-size: calc(1em * 0.625); /* IE9-IE11 math fixing. See http://bit.ly/1g4X0bX */
}

body {
  touch-action: manipulation;

  font-family: fonts.$base;
  color: map.get(colors.$basics, "black");
  background-color: map.get(colors.$basics, "white");
}

// TO REMOVE WHEN App.vue CUSTOMISED ---
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
// -------------------------------------
