@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Date-picker-desktop,
.Date-picker-mobile,
.Date-picker-modal {
  .dp__theme_light {
    --dp-text-color: #{map.get(colors.$caviarBlack, "700")};
    --dp-hover-color: #{map.get(colors.$pearlGrey, "200")};
    --dp-hover-text-color: #{map.get(colors.$caviarBlack, "700")};
    --dp-primary-color: #{colors.$absoluteBlack};
    --dp-primary-text-color: #{colors.$absoluteWhite};
    --dp-secondary-color: #{map.get(colors.$neutral, "200")};
    --dp-border-color: transparent;
    --dp-menu-border-color: transparent;
    --dp-icon-color: #{colors.$absoluteBlack};
    --dp-range-between-dates-background-color: var(--dp-hover-color);
    --dp-range-between-dates-text-color: var(--dp-hover-text-color);
    --dp-range-between-border-color: var(--dp-hover-color);

    --dp-font-family: fonts.$base;
    --dp-border-radius: 2px;
    --dp-cell-border-radius: 4px;

    --dp-cell-size: 4.8rem;
    --dp-action-row-padding: 2.4rem;
    --dp-row-margin: 2px 0;
    --dp-two-calendars-spacing: 4rem;
    --dp-menu-padding: 2.4rem 2.4rem 0 2.4rem;

    --dp-font-size: 16px;

    .dp__cell_inner {
      @include text.lbf-text("body-01");

      &:hover {
        border: 1px solid colors.$absoluteBlack;
      }

      &:not(.dp__pointer):hover {
        border: unset;
      }

      &.dp__range_start.dp__range_end {
        border-radius: 4px;
      }
    }

    .dp__calendar_item {
      &:focus-within {
        margin: -1px;
        border-radius: 4px;
        border: 1px solid colors.$absoluteBlack;
      }

      &[aria-disabled="true"] {
        border-color: transparent;
      }
    }

    .dp__calendar_header_item {
      @include text.lbf-text("body-02");
    }

    .dp__menu {
      box-shadow: map.get(boxes.$shadows, "default");
    }

    .dp__calendar_header {
      border-bottom: 1px dashed map.get(colors.$neutral, "200");
    }

    .dp__today {
      border: none;
    }

    .dp__calendar_header_separator {
      background: transparent;
    }

    .dp__cell_inner.dp__cell_disabled:not(.dp__range_end):not(.dp__range_start) {
      &:hover {
        background-color: inherit;
        border: unset;
        color: rgb(218, 218, 221);
      }
    }
  }

  &__header {
    position: relative;
    display: flex;
    justify-content: center;
    width: 100%;
    margin-block-end: map.get(spaces.$sizes, "7");
  }

  &__previous,
  &__next {
    position: absolute;
    top: 50%;
  }

  &__previous {
    left: 0;
    transform: translateY(-50%) rotateZ(180deg);
  }

  &__next {
    right: 0;
    transform: translateY(-50%);
  }

  &__month-year {
    @include text.lbf-text("heading-03");
  }

  &__nights {
    @include text.lbf-text("label-01");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__clear {
    @include text.lbf-text("body-01-underline");
    color: map.get(colors.$caviarBlack, "900");

    &:hover,
    &:focus {
      color: map.get(colors.$caviarBlack, "800");
    }

    &:-moz-user-disabled {
      color: map.get(colors.$caviarBlack, "500");
    }

    &:disabled {
      cursor: default;
      color: map.get(colors.$caviarBlack, "500");
    }
  }
}

.Date-picker-mobile {
  .dp__calendar_item,
  .dp__cell_inner {
    width: 100%;
    height: 100%;
    max-height: 4.8rem;
  }

  .dp__menu_inner {
    padding-inline: map.get(spaces.$sizes, "4");
  }

  .dp__main.dp__theme_light.dp__flex_display {
    flex-flow: column;
  }
}
