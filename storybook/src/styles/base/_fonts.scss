// ------------------
// Fonts font-face
// ------------------
@use "sass:list";

$fonts-path: "@sb-fonts";

$montserrat: (
  Regular: 400 normal,
  Medium: 500 normal,
  SemiBold: 600 normal
);

@each $variant-name, $variant-data in $montserrat {
  $base: "/Montserrat/Montserrat-" + $variant-name;

  @font-face {
    font-family: "Montserrat";
    font-style: list.nth($variant-data, 2);
    font-weight: list.nth($variant-data, 1);
    src: url($fonts-path + $base + ".woff2") format("woff2");
  }
}

$silk-serif: (
  Light: 300 normal,
  LightItalic: 300 italic
);

@each $variant-name, $variant-data in $silk-serif {
  $silkSerif: "/SilkSerif/SilkSerif-" + $variant-name;

  @font-face {
    font-family: "SilkSerif";
    font-style: list.nth($variant-data, 2);
    font-weight: list.nth($variant-data, 1);
    src: url($fonts-path + $silkSerif + ".woff2") format("woff2");
  }
}
