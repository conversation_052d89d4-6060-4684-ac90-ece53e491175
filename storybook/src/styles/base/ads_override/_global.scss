@use "sass:map";
@use "@sb-config/fonts";
@use "@sb-config/text";

:root {
  // ads variables mapping
  // font-family
  --ads-fonts-heading: #{fonts.$base};
  --ads-fonts-body: #{fonts.$base};
  --ads-fonts-mono: #{fonts.$base};

  // font-size
  --ads-font-sizes-3xs: #{map.get(text.$sizes, "3xs")};
  --ads-font-sizes-2xs: #{map.get(text.$sizes, "2xs")};
  --ads-font-sizes-xs: #{map.get(text.$sizes, "xs")};
  --ads-font-sizes-sm: #{map.get(text.$sizes, "s")};
  --ads-font-sizes-md: #{map.get(text.$sizes, "m")};
  --ads-font-sizes-lg: #{map.get(text.$sizes, "l")};
  --ads-font-sizes-xl: #{map.get(text.$sizes, "xl")};
  --ads-font-sizes-2xl: #{map.get(text.$sizes, "2xl")};
  --ads-font-sizes-3xl: #{map.get(text.$sizes, "3xl")};
  --ads-font-sizes-4xl: #{map.get(text.$sizes, "4xl")};
  --ads-font-sizes-5xl: #{map.get(text.$sizes, "5xl")};
  --ads-font-sizes-6xl: #{map.get(text.$sizes, "5xl")};
  --ads-font-sizes-7xl: #{map.get(text.$sizes, "5xl")};
  --ads-font-sizes-8xl: #{map.get(text.$sizes, "5xl")};
  --ads-font-sizes-9xl: #{map.get(text.$sizes, "5xl")};

  // font-weight
  --ads-font-weights-hairline: #{map.get(text.$weight, "light")};
  --ads-font-weights-thin: #{map.get(text.$weight, "light")};
  --ads-font-weights-light: #{map.get(text.$weight, "light")};
  --ads-font-weights-normal: #{map.get(text.$weight, "regular")};
  --ads-font-weights-medium: #{map.get(text.$weight, "medium")};
  --ads-font-weights-semibold: #{map.get(text.$weight, "semiBold")};
  --ads-font-weights-bold: #{map.get(text.$weight, "semiBold")};
  --ads-font-weights-extrabold: #{map.get(text.$weight, "semiBold")};
  --ads-font-weights-black: #{map.get(text.$weight, "semiBold")};

  // line-height
  --ads-line-heights-3: #{map.get(text.$line-height, "2xs")}rem;
  --ads-line-heights-4: #{map.get(text.$line-height, "xs")}rem;
  --ads-line-heights-5: #{map.get(text.$line-height, "s")}rem;
  --ads-line-heights-6: #{map.get(text.$line-height, "m")}rem;
  --ads-line-heights-7: #{map.get(text.$line-height, "l")}rem;
  --ads-line-heights-8: #{map.get(text.$line-height, "xl")}rem;
  --ads-line-heights-9: #{map.get(text.$line-height, "2xl")}rem;
  --ads-line-heights-10: #{map.get(text.$line-height, "3xl")}rem;
  --ads-line-heights-normal: normal;
  --ads-line-heights-none: #{map.get(text.$line-height, "3xs")}rem;
  --ads-line-heights-shorter: #{map.get(text.$line-height, "s")}rem;
  --ads-line-heights-short: #{map.get(text.$line-height, "l")}rem;
  --ads-line-heights-base: #{map.get(text.$line-height, "4xl")}rem;
  --ads-line-heights-tall: #{map.get(text.$line-height, "4xl")}rem;
  --ads-line-heights-taller: #{map.get(text.$line-height, "4xl")}rem;
}
