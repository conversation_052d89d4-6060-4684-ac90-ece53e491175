@use "@sb-config/spaces";
@use "@sb-utilities/mq";

@each $size, $value in spaces.$sizes {
  // Margin
  .ma-#{$size} {
    margin: $value;
  }
  .mx-#{$size} {
    margin-inline: $value;
  }
  .my-#{$size} {
    margin-block: $value;
  }
  .mt-#{$size} {
    margin-block-start: $value;
  }
  .mb-#{$size} {
    margin-block-end: $value;
  }
  .ml-#{$size} {
    margin-inline-start: $value;
  }
  .mr-#{$size} {
    margin-inline-end: $value;
  }

  // Padding
  .pa-#{$size} {
    padding: $value;
  }
  .px-#{$size} {
    padding-inline: $value;
  }
  .py-#{$size} {
    padding-block: $value;
  }
  .pt-#{$size} {
    padding-block-start: $value;
  }
  .pb-#{$size} {
    padding-block-end: $value;
  }
  .pl-#{$size} {
    padding-inline-start: $value;
  }
  .pr-#{$size} {
    padding-inline-end: $value;
  }

  // Gap
  .g-#{$size} {
    gap: $value;
  }
  .gx-#{$size} {
    column-gap: $value;
  }
  .gy-#{$size} {
    row-gap: $value;
  }
}

@each $bp-name, $bp-size in mq.$breakpoints-list {
  @include mq.media(">=#{$bp-name}") {
    @each $size, $value in spaces.$sizes {
      // Margin
      .ma-#{$bp-name}-#{$size} {
        margin: $value;
      }
      .mx-#{$bp-name}-#{$size} {
        margin-inline: $value;
      }
      .my-#{$bp-name}-#{$size} {
        margin-block: $value;
      }
      .mt-#{$bp-name}-#{$size} {
        margin-block-start: $value;
      }
      .mb-#{$bp-name}-#{$size} {
        margin-block-end: $value;
      }
      .ml-#{$bp-name}-#{$size} {
        margin-inline-start: $value;
      }
      .mr-#{$bp-name}-#{$size} {
        margin-inline-end: $value;
      }

      // Padding
      .pa-#{$bp-name}-#{$size} {
        padding: $value;
      }
      .px-#{$bp-name}-#{$size} {
        padding-inline: $value;
      }
      .py-#{$bp-name}-#{$size} {
        padding-block: $value;
      }
      .pt-#{$bp-name}-#{$size} {
        padding-block-start: $value;
      }
      .pb-#{$bp-name}-#{$size} {
        padding-block-end: $value;
      }
      .pl-#{$bp-name}-#{$size} {
        padding-inline-start: $value;
      }
      .pr-#{$bp-name}-#{$size} {
        padding-inline-end: $value;
      }

      // Gap
      .g-#{$bp-name}-#{$size} {
        gap: $value;
      }
      .gx-#{$bp-name}-#{$size} {
        column-gap: $value;
      }
      .gy-#{$bp-name}-#{$size} {
        row-gap: $value;
      }
    }
  }
}
