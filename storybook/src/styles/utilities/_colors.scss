@use "@sb-config/colors";

// Utilities classes generating
$colorPallets: (
  "pink": colors.$pink,
  "stratosBlue": colors.$stratosBlue,
  "velvet": colors.$velvet,
  "caviarBlack": colors.$caviarBlack,
  "porcelainWhite": colors.$porcelainWhite,
  "mistGrey": colors.$mistGrey,
  "warmGrey": colors.$warmGrey,
  "pearlGrey": colors.$pearlGrey,
  "russianBlue": colors.$russianBlue,
  "britishRacingGreen": colors.$britishRacingGreen,
  "amber": colors.$amber,
  "chestnut": colors.$chestnut,
  "neutral": colors.$neutral,
  "white": colors.$white,
  "black": colors.$black,
  "red": colors.$red,
  "yellow": colors.$yellow,
  "green": colors.$green,
  "blue": colors.$blue,
  "orange": colors.$orange,
  "silver": colors.$silver,
  "gold": colors.$gold,
  "platinium": colors.$platinium,
  "diamond": colors.$diamond,
  "loyaltyBlack": colors.$loyaltyBlack,
  "basics": colors.$basics,
  "customs": colors.$customs
);

@each $color, $map in $colorPallets {
  @each $name, $value in $map {
    .bg-#{$color}-#{$name} {
      background-color: $value;
    }
    .color-#{$color}-#{$name} {
      color: $value;
    }
    .border-#{$color}-#{$name} {
      border-color: $value;
    }
  }
}

@each $name, $color in colors.$basics {
  .bg-#{$name} {
    background-color: $color;
  }
  .color-#{$name} {
    color: $color;
  }
  .border-#{$name} {
    border-color: $color;
  }
}
