//Z-index
// Returns z-index layer level
// @param {String} $name - element to order in layers
// return {Integer}
// @require $name
@use "sass:list";

@function z($name) {
  @if list.index($z-indexes, $name) {
    @return (list.length($z-indexes) - list.index($z-indexes, $name)) * 10 + 10;
  } @else {
    @warn 'there is no item "#{$name}" in this list; Choose one of: #{$z-indexes}';

    @return null;
  }
}

// use the class name of the element on which the zindex is applied
// the highest element on the $z-indexes list is also the highest on the front layers

$z-indexes: (
  "Splash-screen",
  "Modal__header",
  "Modal",
  "Header-modal",
  "Header-modal__overlay",
  "Dropdown-account",
  "Dropdown-modal",
  "Overlay",
  "Sidebar-accordion"
);
