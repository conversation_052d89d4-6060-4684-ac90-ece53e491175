// -----------------------------------------------------------------------------
// This file contains all application-wide Sass functions.
// -----------------------------------------------------------------------------

@use "sass:map";
@use "@sb-config/animation" as anim;

$base-url: "../";

// Native `url(..)` function wrapper
// @param {String} $base - base URL for the asset
// @param {String} $type - asset type folder (e.g. `fonts/`)
// @param {String} $path - asset path
// @return {Url}
@function asset($base, $type, $path) {
  @return url($base + $type + $path);
}

// Returns URL to an image based on its path
// @param {String} $path - image path
// @param {String} $base [$base-url] - base URL
// @return {Url}
// @require $base-url
@function image($path, $base: $base-url) {
  @return asset($base, "images/", $path);
}

// Returns URL to a font based on its path
// @param {String} $path - font path
// @param {String} $base [$base-url] - base URL
// @return {Url}
// @require $base-url
@function font($path, $base: $base-url) {
  @return asset($base, "fonts/", $path);
}

// Easing Map Get Function
// @link https://css-tricks.com/snippets/sass/easing-map.get-function/
// @usage `animation: name 2.5s ease(in-quad) infinite alternate;`
@function ease($key) {
  @if map.has-key(anim.$ease, $key) {
    @return map.get(anim.$ease, $key);
  }

  @warn "Unkown '#{$key}' in $ease.";
  @return null;
}
