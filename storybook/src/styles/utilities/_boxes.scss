@use "sass:map";
@use "@sb-config/boxes";
@use "@sb-config/colors";
@use "@sb-utilities/mq";

@each $name, $radius in boxes.$radii {
  .radius-#{$name} {
    border-radius: $radius;
  }
}

@each $name, $border in boxes.$borders {
  .border-#{$name} {
    border: $border;
  }
}

@each $name, $shadow in boxes.$shadows {
  .shadow-#{$name} {
    box-shadow: $shadow;
  }
}

.custom-scrollbar-v,
.custom-scrollbar-h {
  @include mq.media(">=medium") {
    &::-webkit-scrollbar-thumb {
      background-color: map.get(colors.$blue, "03");
      border-radius: map.get(boxes.$radii, "rounded");
    }
    &::-webkit-scrollbar-track {
      background-color: transparent;
      border-radius: map.get(boxes.$radii, "rounded");
    }
  }
}

.custom-scrollbar-h {
  @include mq.media(">=medium") {
    &::-webkit-scrollbar {
      height: 8px;
    }
  }
}

.custom-scrollbar-v {
  @include mq.media(">=medium") {
    &::-webkit-scrollbar {
      width: 8px;
    }
  }
}
