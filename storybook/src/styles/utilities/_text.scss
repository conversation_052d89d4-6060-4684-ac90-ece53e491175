@use "sass:map";
@use "@sb-config/fonts";
@use "@sb-config/text";
@use "@sb-utilities/mq";
@use "@sb-utilities/spaces";

@each $name, $value in text.$sizes {
  .text-#{$name} {
    font-size: $value;
  }
}

@each $name, $value in text.$weight {
  .weight-#{$name} {
    font-weight: $value;
  }
}

@each $name, $value in text.$line-height {
  .line-height-#{$name} {
    line-height: #{$value};
  }
}

.exp-heading-01 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "5xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "2xs");
  letter-spacing: -1.28px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-heading-01-alt {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "5xl");
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "2xs");
  letter-spacing: -1.28px;
  text-decoration: unset;
  text-transform: uppercase;
}

.exp-heading-02 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "4xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "xs");
  letter-spacing: -0.96px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-heading-02-alt {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "4xl");
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "xs");
  letter-spacing: -0.96px;
  text-decoration: unset;
  text-transform: uppercase;
}

.exp-heading-03 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "3xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "s");
  letter-spacing: -0.64px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-heading-03-alt {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "3xl");
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "s");
  letter-spacing: -0.64px;
  text-decoration: unset;
  text-transform: uppercase;
}

.exp-heading-04 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "2xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "m");
  letter-spacing: -0.56px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-heading-04-alt {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "2xl");
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "m");
  letter-spacing: -0.56px;
  text-decoration: unset;
  text-transform: uppercase;
}

.exp-heading-05 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: -0.48px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-heading-05-underline {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "xl");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: -0.48px;
  text-decoration: underline;
  text-transform: unset;
}

.exp-heading-06 {
  font-family: fonts.$silkSerif;
  font-size: map.get(text.$sizes, "l");
  font-style: italic;
  font-weight: map.get(text.$weight, "light");
  line-height: map.get(text.$line-height, "xl");
  letter-spacing: -0.4px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-subheading-01 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "l");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "xl");
  letter-spacing: 0.2px;
  text-decoration: unset;
  text-transform: unset;
}

.exp-subheading-02 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "m");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "3xl");
  letter-spacing: 0.18px;
  text-decoration: unset;
  text-transform: unset;
}

.heading-01 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xl");
  font-weight: map.get(text.$weight, "semiBold");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.72px;
  text-decoration: unset;
  text-transform: uppercase;
}

.heading-01-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xl");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.6px;
  text-decoration: underline;
  text-transform: uppercase;
}

.heading-02 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "l");
  font-weight: map.get(text.$weight, "semiBold");
  line-height: map.get(text.$line-height, "xl");
  letter-spacing: 0.6px;
  text-decoration: unset;
  text-transform: uppercase;
}

.heading-02-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "l");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "xl");
  letter-spacing: 0;
  text-decoration: underline;
  text-transform: uppercase;
}

.heading-03 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "semiBold");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.48px;
  text-decoration: unset;
  text-transform: uppercase;
}

.heading-03-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0;
  text-decoration: underline;
  text-transform: uppercase;
}

.body-01 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.16px;
  text-decoration: unset;
  text-transform: unset;
}

.body-01-strong {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.16px;
  text-decoration: unset;
  text-transform: unset;
}

.body-01-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.16px;
  text-decoration: underline;
  text-transform: unset;
}

.body-01-uppercase {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.8px;
  text-decoration: unset;
  text-transform: uppercase;
}

.body-01-uppercase-strong {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "s");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "4xl");
  letter-spacing: 0.8px;
  text-decoration: unset;
  text-transform: uppercase;
}

.body-02 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "5xl");
  letter-spacing: 0.14px;
  text-decoration: unset;
  text-transform: unset;
}

.body-02-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "2xl");
  letter-spacing: 0.14px;
  text-decoration: unset;
  text-decoration: underline;
}

.body-02-strong {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xs");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "2xl");
  letter-spacing: 0.14px;
  text-decoration: unset;
  text-transform: unset;
}

.body-02-uppercase {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "2xl");
  letter-spacing: 0.7px;
  text-decoration: unset;
  text-transform: uppercase;
}

.label-01 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "xs");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "5xl");
  letter-spacing: 0.14px;
  text-decoration: unset;
  text-transform: uppercase;
}

.caption-01 {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "2xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.12px;
  text-decoration: unset;
  text-transform: unset;
}

.caption-01-underline {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "2xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.12px;
  text-decoration: unset;
  text-decoration: underline;
}

.caption-01-strong {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "2xs");
  font-weight: map.get(text.$weight, "medium");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.12px;
  text-decoration: unset;
  text-transform: unset;
}

.caption-01-uppercase {
  font-family: fonts.$base;
  font-size: map.get(text.$sizes, "2xs");
  font-weight: map.get(text.$weight, "regular");
  line-height: map.get(text.$line-height, "l");
  letter-spacing: 0.24px;
  text-decoration: unset;
  text-transform: uppercase;
}

.page-title {
  text-align: center;

  @extend .my-10;
  @extend .my-medium-11;
}
//.longtext {
//  white-space: pre-line;
//}
