// --------------------
// Spaces Configuration
// --------------------

@use "sass:map";

$size-base-unit: 0.4rem;
// legacy map, keep it to avoid big refactor
$sizes: (
  "auto": auto,
  //0px
  "1": $size-base-unit * 0,
  // 2px
  "2": $size-base-unit * 0.5,
  // 4px
  "3": $size-base-unit * 1,
  // 8px
  "4": $size-base-unit * 2,
  // 12px
  "5": $size-base-unit * 3,
  // 16px
  "6": $size-base-unit * 4,
  // 24px
  "7": $size-base-unit * 6,
  // 32px
  "8": $size-base-unit * 8,
  // 40px
  "9": $size-base-unit * 10,
  // 48px
  "10": $size-base-unit * 12,
  // 64px
  "11": $size-base-unit * 16,
  // 80px
  "12": $size-base-unit * 20,
  // 96px
  "13": $size-base-unit * 24,
  // 128px
  "14": $size-base-unit * 32
);
