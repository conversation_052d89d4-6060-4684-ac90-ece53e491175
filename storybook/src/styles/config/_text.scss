@use "sass:map";
@use "@sb-config/fonts";

// ------------------
// Text Configuration
// ------------------

// Font-size
$sizes: (
  "3xs": 1rem,
  "2xs": 1.2rem,
  "xs": 1.4rem,
  "s": 1.6rem,
  "m": 1.8rem,
  "l": 2rem,
  "xl": 2.4rem,
  "2xl": 2.8rem,
  "3xl": 3.2rem,
  "4xl": 4.8rem,
  "5xl": 6.4rem
);

$weight: (
  "light": 300,
  "regular": 400,
  "medium": 500,
  "semiBold": 600
);

$line-height: (
  "3xs": 1,
  "2xs": 1.12,
  "xs": 1.16,
  "s": 1.25,
  "m": 1.28,
  "l": 1.33,
  "xl": 1.4,
  "2xl": 1.42,
  "3xl": 1.44,
  "4xl": 1.5,
  "5xl": 1.57
);

// @deprecated - Only use this for nested (::deep)
@mixin lbf-text($name) {
  @if $name == "exp-heading-01" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "5xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "2xs");
    letter-spacing: -1.28px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-heading-01-alt" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "5xl");
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "2xs");
    letter-spacing: -1.28px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "exp-heading-02" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "4xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "xs");
    letter-spacing: -0.96px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-heading-02-alt" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "4xl");
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "xs");
    letter-spacing: -0.96px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "exp-heading-03" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "3xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "s");
    letter-spacing: -0.64px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-heading-03-alt" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "3xl");
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "s");
    letter-spacing: -0.64px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "exp-heading-04" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "2xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "m");
    letter-spacing: -0.56px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-heading-04-alt" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "2xl");
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "m");
    letter-spacing: -0.56px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "exp-heading-05" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "l");
    letter-spacing: -0.48px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-heading-05-underline" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "xl");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "l");
    letter-spacing: -0.48px;
    text-decoration: underline;
    text-transform: unset;
  }

  @if $name == "exp-heading-06" {
    font-family: fonts.$silkSerif;
    font-size: map.get($sizes, "l");
    font-style: italic;
    font-weight: map.get($weight, "light");
    line-height: map.get($line-height, "xl");
    letter-spacing: -0.4px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-subheading-01" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "l");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "xl");
    letter-spacing: 0.2px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "exp-subheading-02" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "m");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "3xl");
    letter-spacing: 0.18px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "heading-01" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xl");
    font-weight: map.get($weight, "semiBold");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.72px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "heading-01-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xl");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.6px;
    text-decoration: underline;
    text-transform: uppercase;
  }

  @if $name == "heading-02" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "l");
    font-weight: map.get($weight, "semiBold");
    line-height: map.get($line-height, "xl");
    letter-spacing: 0.6px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "heading-02-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "l");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "xl");
    letter-spacing: 0;
    text-decoration: underline;
    text-transform: uppercase;
  }

  @if $name == "heading-03" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "semiBold");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.48px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "heading-03-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0;
    text-decoration: underline;
    text-transform: uppercase;
  }

  @if $name == "body-01" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.16px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "body-01-strong" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.16px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "body-01-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.16px;
    text-decoration: underline;
    text-transform: unset;
  }

  @if $name == "body-01-uppercase" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.8px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "body-01-uppercase-strong" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "s");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "4xl");
    letter-spacing: 0.8px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "body-02" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "5xl");
    letter-spacing: 0.14px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "body-02-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "2xl");
    letter-spacing: 0.14px;
    text-decoration: unset;
    text-decoration: underline;
  }

  @if $name == "body-02-strong" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xs");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "2xl");
    letter-spacing: 0.14px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "body-02-uppercase" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "2xl");
    letter-spacing: 0.7px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "label-01" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "xs");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "5xl");
    letter-spacing: 0.14px;
    text-decoration: unset;
    text-transform: uppercase;
  }

  @if $name == "caption-01" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "2xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.12px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "caption-01-underline" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "2xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.12px;
    text-decoration: unset;
    text-decoration: underline;
  }

  @if $name == "caption-01-strong" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "2xs");
    font-weight: map.get($weight, "medium");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.12px;
    text-decoration: unset;
    text-transform: unset;
  }

  @if $name == "caption-01-uppercase" {
    font-family: fonts.$base;
    font-size: map.get($sizes, "2xs");
    font-weight: map.get($weight, "regular");
    line-height: map.get($line-height, "l");
    letter-spacing: 0.24px;
    text-decoration: unset;
    text-transform: uppercase;
  }
}
