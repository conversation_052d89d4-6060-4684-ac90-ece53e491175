// -------------------
// Boxes Configuration
// -------------------

@use "sass:map";
@use "@sb-config/colors";

// Radius
$radii: (
  "none": 0px,
  "soft": 2px,
  "rounded": 9999px
);

// Borders
$borders: (
  "none": none,
  "transparent": 1px solid transparent,
  "decorative": 1px solid map.get(colors.$basics, "black"),
  "interactive": 1px solid map.get(colors.$neutral, "500"),
  "interactiveHover": 1px solid map.get(colors.$neutral, "600"),
  "interactiveSelected": 2px solid map.get(colors.$basics, "black")
);

// Shadows
$shadows: (
  "default": 0px 0px 10px 0px rgba(201, 201, 201, 0.25),
  "top": 0px -2px 6px 0px rgba(0, 0, 0, 0.1),
  "topStrong": 0px -2px 6px 0px rgba(0, 0, 0, 0.2),
  "bottom": 0px 2px 6px 0px rgba(0, 0, 0, 0.1),
  "bottomStrong": 0px 2px 6px 0px rgba(0, 0, 0, 0.2),
  "topBottom": 0px 8px 16px 0px rgba(0, 0, 0, 0.1)
);
