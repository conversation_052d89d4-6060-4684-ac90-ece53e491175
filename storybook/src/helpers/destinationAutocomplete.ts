import { DestinationOptionsKeys } from "../components/organisms/UiDestination/UiDestinationDesktop/enums"
import Fuse from "fuse.js"
import { type Ref } from "vue"
import type { UiDestinationListDropdownItem } from "../components/molecules/UiDestinationListDropdown/types"
import type { UiDestinationListItemType } from "../components/molecules/UiDestinationList/types"

const fuseOptions = {
  distance: 100,
  includeScore: true,
  keys: [
    { name: DestinationOptionsKeys.NAME, weight: 0.4 },
    { name: DestinationOptionsKeys.CITY, weight: 0.3 },
    { name: DestinationOptionsKeys.STATE, weight: 0.2 },
    { name: DestinationOptionsKeys.COUNTRY, weight: 0.1 }
  ],
  minMatchCharLength: 1,
  threshold: 0.3
}

export function filterDestinationsDesktop(items: UiDestinationListItemType[], searchQuery: Ref<string>) {
  const allItems = [...new Map(items.flatMap((group) => group.items).map((item) => [item.id, item])).values()]
  const fuse = new Fuse(allItems, fuseOptions)

  if (!searchQuery.value.length) {
    return items
  }

  const results = fuse.search(searchQuery.value)

  return items
    .map((group) => {
      return {
        ...group,
        items: group.items.filter((item) => results.some((result) => result.item.id === item.id))
      }
    })
    .filter((group) => group.items.length > 0)
}

export function filterDestinationsMobile(items: UiDestinationListDropdownItem[], searchQuery: Ref<string | undefined>) {
  if (!searchQuery?.value) {
    return items
  }

  const allItems = [
    ...new Map(
      items.flatMap((dropdown) => dropdown.items.flatMap((region) => region.items || [])).map((item) => [item.id, item])
    ).values()
  ]
  const fuse = new Fuse(allItems, fuseOptions)

  if (!searchQuery.value.length) {
    return items
  }

  const results = fuse.search(searchQuery.value)

  return items
    .map((dropdown) => ({
      ...dropdown,
      items: dropdown.items
        .map((region) => {
          return {
            ...region,
            items: region.items?.filter((item) => results.some((result) => result.item.id === item.id)) || []
          }
        })
        .filter((region) => region.items && region.items.length > 0)
    }))
    .filter((dropdown) => dropdown.items.length > 0)
}
