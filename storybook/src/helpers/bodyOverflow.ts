export const preventBodyOverflow = () => {
  const body = document.querySelector("body")
  if (body) {
    // Calculer la largeur de la barre de défilement avant de la cacher
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth

    // Ajouter un padding-right pour compenser la disparition de la scrollbar
    body.style.paddingRight = `${scrollbarWidth}px`
    body.classList.add("overflow-hidden")
  }
}

export const allowBodyOverflow = () => {
  const body = document.querySelector("body")
  if (body) {
    // Restaurer le padding-right original
    body.style.paddingRight = ""
    body.classList.remove("overflow-hidden")
  }
}
