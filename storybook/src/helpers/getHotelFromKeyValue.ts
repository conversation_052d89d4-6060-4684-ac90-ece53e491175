import type { UiDestinationListDropdownItem } from "../components/molecules/UiDestinationListDropdown/types"
import type { UiDestinationListItemProps } from "../components/atoms/UiDestinationListItem/interface"
import type { UiDestinationListItemType } from "../components/molecules/UiDestinationList/types"

/**
 *
 * @param destinations
 * @param key
 * @param value
 */
export function getHotelFromDestinationListDropdown(
  destinations: UiDestinationListDropdownItem[],
  key: keyof UiDestinationListItemProps,
  value: string
) {
  return destinations
    .flatMap((destination) => destination.items.flatMap((region) => region.items))
    .find((item) => {
      if (item) {
        return item[key] === value
      }
    })
}

export function getHotelFromDestinationList(
  destinations: UiDestinationListItemType[],
  key: keyof UiDestinationListItemProps,
  value: string
) {
  return destinations
    .flatMap((destination) => destination.items)
    .find((item) => {
      if (item) {
        return item[key] === value
      }
    })
}
