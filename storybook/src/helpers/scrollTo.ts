/**
 *
 * Allows to smooth scroll to an element, inside a container or not
 * @param el
 * @param container
 * @param offset
 */
export function scrollToElement(el: HTMLElement, container?: HTMLElement, offset = 0) {
  const elementTop = el.getBoundingClientRect().top
  const containerTop = container?.getBoundingClientRect().top ?? 0

  if (container) {
    const relativeY = elementTop - containerTop + container?.scrollTop - offset
    container.scrollTo({ behavior: "smooth", top: relativeY })
  } else {
    const elementTopOffset = window.scrollY + el.getBoundingClientRect().top + offset
    window.scrollTo({ behavior: "smooth", top: elementTopOffset })
  }
}
