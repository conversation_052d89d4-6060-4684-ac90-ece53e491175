export const focusableElements =
  'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'

/**
 *
 * @param el // el is the parent of the list of elements we want
 */
export function getFocusableElements(el: HTMLElement) {
  const focusableEls = el.querySelectorAll(focusableElements)

  return focusableEls
    ? [...(focusableEls as unknown as HTMLElement[])].filter((el: HTMLElement) => el.getAttribute("tabindex") !== "-1")
    : undefined
}

/**
 *
 * @param el // el is the parent of the list of elements from witch we want the first element
 */
export function getFirstFocusableElement(el: HTMLElement) {
  const focusableNodeList = getFocusableElements(el)

  return focusableNodeList ? focusableNodeList[0] : undefined
}

/**
 *
 * @param el //el is the currently focused element
 * @param elList
 */
export function getPreviousFocusableElement(el: HTMLElement, elList: HTMLElement[]) {
  const currentIndex = elList?.findIndex((listEl) => listEl === el)
  const previousIndex =
    (currentIndex - 1) % elList?.length >= 0 ? (currentIndex - 1) % elList?.length : elList?.length - 1

  return elList[previousIndex]
}

/**
 *
 * @param el // el is the currently focused element
 * @param elList
 */
export function getNextFocusableElement(el: HTMLElement, elList: HTMLElement[]) {
  const currentIndex = elList.findIndex((listEl) => listEl === el)
  const nextIndex = (currentIndex + 1) % elList?.length

  return elList[nextIndex]
}

/**
 *
 * @param parentElement
 */
export function focusFirstElement(parentElement: HTMLElement) {
  const focusable = parentElement?.querySelector(focusableElements) as HTMLElement

  focusable?.focus()
}

/**
 * @param el
 * @param focusableElementList
 */
export function addTrapFocusToElement(el?: HTMLElement, focusableElementList: string = focusableElements): void {
  el?.addEventListener("keydown", function (event) {
    const isTabPressed = event.key === "Tab"
    if (!isTabPressed) {
      return
    }

    const firstFocusableElement = el?.querySelectorAll(focusableElementList)[0] as HTMLElement
    const focusableContent = el?.querySelectorAll(focusableElementList) ?? []
    const lastFocusableElement = focusableContent[focusableContent.length - 1] as HTMLElement

    if (event.shiftKey) {
      // if shift key pressed for shift + tab combination
      if (document.activeElement === firstFocusableElement) {
        lastFocusableElement.focus()
        event.preventDefault()
      }
      // if tab key is pressed
    } else if (document.activeElement === lastFocusableElement) {
      firstFocusableElement?.focus()
      event.preventDefault()
    }
  })
}

/**
 *
 * @param el
 */
export function removeTrapFocusToElement(el?: HTMLElement): void {
  el?.removeEventListener("keydown", () => null)
}
