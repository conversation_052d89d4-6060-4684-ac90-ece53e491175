import Account from "./account.svg"
import AccountLogged from "./accountLogged.svg"
import Add from "./add.svg"
import AllIcon from "./allIcon.svg"
import AllMonogram from "./allMonogram.svg"
import AllStatusSquareIcons from "./allStatusSquareIcons.svg"
import Arrow from "./arrow.svg"
import BedDouble from "./bedDouble.svg"
import Breakfast from "./breakfast.svg"
import Check from "./check.svg"
import CheckDefault from "./checkDefault.svg"
import CheckRounded from "./checkRounded.svg"
import ChevronDown from "./chevronDown.svg"
import ChevronRight from "./chevronRight.svg"
import ChevronUp from "./chevronUp.svg"
import Close from "./close.svg"
import CreditCard from "./creditCard.svg"
import Divider from "./divider.svg"
import Edit from "./edit.svg"
import Ellipsis from "./ellipsis.svg"
import Error from "./error.svg"
import IconGallery from "./iconGallery.svg"
import ListIcon from "./listIcon.svg"
import Lock from "./lock.svg"
import Minus from "./minus.svg"
import NotRefundable from "./notRefundable.svg"
import Occupant from "./occupant.svg"
import Plus from "./plus.svg"
import Restaurant from "./restaurant.svg"
import Size from "./size.svg"
import Time from "./time.svg"
import Trash from "./trash.svg"
import UsFlag from "./usFlag.svg"
import View from "./view.svg"
import Warn from "./warn.svg"
import Wheelchair from "./wheelchair.svg"

export const iconMapping = {
  account: Account,
  accountLogged: AccountLogged,
  add: Add,
  allIcon: AllIcon,
  allMonogram: AllMonogram,
  allStatusSquareIcons: AllStatusSquareIcons,
  arrow: Arrow,
  bedDouble: BedDouble,
  breakfast: Breakfast,
  check: Check,
  checkDefault: CheckDefault,
  checkRounded: CheckRounded,
  chevronDown: ChevronDown,
  chevronRight: ChevronRight,
  chevronUp: ChevronUp,
  close: Close,
  creditCard: CreditCard,
  divider: Divider,
  edit: Edit,
  ellipsis: Ellipsis,
  error: Error,
  iconGallery: IconGallery,
  listIcon: ListIcon,
  lock: Lock,
  minus: Minus,
  notRefundable: NotRefundable,
  occupant: Occupant,
  plus: Plus,
  restaurant: Restaurant,
  size: Size,
  time: Time,
  trash: Trash,
  usFlag: UsFlag,
  view: View,
  warn: Warn,
  wheelchair: Wheelchair
}

export type Icon = keyof typeof iconMapping
