services:
  frontend-fairmont:
    build:
      context: ./infra/local/apps
      args:
        PROJECT: Fairmont
    user: ${FIXUID:-1000}:${FIXGID:-1000} # keep this for moment
    ports:
      - "3001:3001"
    volumes:
      - /etc/hosts:/etc/hosts:ro
      - .:/usr/src/app
    command: pnpm run dev --host
    networks:
      - luxury-booking
    environment:
      - INSTALL_MODE=${INSTALL_MODE}
      - PROJECT_ENV=development

  proxy:
    image: nginx:1.17-alpine
    ports:
      - "443:443"
      - "8000:80"
    volumes:
      - ./infra/local/proxy/conf.d:/etc/nginx/conf.d
      - nginx_cache:/var/cache/nginx # Add nginx cache volume
    depends_on:
      - frontend-fairmont # add all frontend containers below
    networks:
      - luxury-booking

  storybook:
    build:
      context: ./infra/local/storybook
    user: ${FIXUID:-1000}:${FIXGID:-1000} # keep this for moment
    environment:
      - WAIT_FOR_SERVICES=frontend-fairmont:3001
    ports:
      - "6006:6006"
    volumes:
      - .:/usr/src/app
    command: npm run storybook
    networks:
      - luxury-booking

  oidc-lambda:
    build:
      context: ./infra/local/lambda
    user: ${FIXUID:-1000}:${FIXGID:-1000} # keep this for moment
    working_dir: /usr/src/app/oidc
    volumes:
      - ./lambda:/usr/src/app
    command: npm run dev
    networks:
      - luxury-booking
    environment:
      - INSTALL_MODE=bypass # as api-proxy will install everything needed for oidc-lambda
      - WAIT_FOR_SERVICES=api-proxy:3000

  api-proxy:
    build:
      context: ./infra/local/lambda
    user: ${FIXUID:-1000}:${FIXGID:-1000} # keep this for moment
    working_dir: /usr/src/app/api
    volumes:
      - ./lambda:/usr/src/app
    command: npm run dev
    networks:
      - luxury-booking
    environment:
      - INSTALL_MODE=${INSTALL_MODE}

networks:
  luxury-booking:
    driver: bridge

volumes:
  nginx_cache:
