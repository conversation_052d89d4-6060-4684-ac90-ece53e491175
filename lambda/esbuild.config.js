const esbuild = require("esbuild")
const dotenv = require("dotenv")

const assetName = process.argv[2]
dotenv.config({ path: `${assetName}/.env` })

// Prepare environment variables for injection
const defineEnv = Object.entries(process.env).reduce((acc, [key, value]) => {
  // Only include valid JavaScript identifiers
  if (/^[a-zA-Z_$][a-zA-Z_$0-9]*$/.test(key)) {
    acc[`process.env.${key}`] = JSON.stringify(value)
  }
  return acc
}, {})

esbuild
  .build({
    entryPoints: [`${assetName}/src/index.mjs`], // or .js
    bundle: true,
    platform: "node",
    target: "node22",
    format: "cjs",
    outfile: `${assetName}/dist/index.js`,
    sourcemap: false,
    minify: true,
    external: ["aws-sdk"], // skip bundling native modules like aws-sdk
    define: defineEnv
  })
  .catch((e) => {
    console.error("Build failed for asset : ", assetName)
    console.error(e)
    process.exit(1)
  })
