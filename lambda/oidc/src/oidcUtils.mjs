export const extractCookies = (event) => {
  const cookieHeader = event.headers.Cookie || event.headers.cookie
  const cookies = {}
  if (cookieHeader) {
    cookieHeader.split(";").forEach((cookie) => {
      const [name, value] = cookie.trim().split("=")
      cookies[name] = value
    })
  }
  return cookies
}

export const decodeBase64 = (encodedString) => {
  try {
    const decoded = Buffer.from(encodedString, "base64").toString()
    return JSON.parse(decoded)
  } catch (error) {
    console.error("Error decoding state:", error)
    return null
  }
}
