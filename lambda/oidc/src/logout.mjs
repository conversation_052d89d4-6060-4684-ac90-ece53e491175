import axios from "axios"
import { extractCookies } from "./oidcUtils.mjs"
import { getKeysForBrand } from "../../utils/lambdaUtils.mjs"

export const logout = async (event, baseUrl, cookieDomain, logoutUrl, revokeUrl, errorPageUrl, redirectUrl) => {
  try {
    //TODO : Refactor clientid management here, on refresh and redirect
    const clientId =
      process.env.ENVIRONMENT === "dev" ? process.env.FAIRMONT_CLIENT_ID_DEV : process.env.FAIRMONT_CLIENT_ID_PROD
    const { partnerSecretKey } = await getKeysForBrand("fairmont") //TODO : rework for a generic way to manage brand name
    const credentials = `${clientId}:${partnerSecretKey}`
    const base64Credentials = Buffer.from(credentials).toString("base64")

    const cookies = extractCookies(event)
    const piSri = cookies.oidc_pi_sri

    if (!piSri) {
      return {
        statusCode: 302,
        headers: {
          Location: errorPageUrl
        },
        body: JSON.stringify({ error: "Error during log out", error_description: "No active session to logout" })
      }
    }

    // Revoke session
    await axios.post(
      logoutUrl,
      { id: piSri },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${base64Credentials}`,
          "X-XSRF-HEADER": "PingFederate"
        }
      }
    )

    // Revoke refresh token
    const data = {
      token: cookies.oidc_refresh_token,
      token_type_hint: "refresh_token"
    }

    await axios.post(revokeUrl, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${base64Credentials}`
      }
    })

    const redirectApplicationUri = redirectUrl || baseUrl + process.env.FAIRMONT_APPLICATION_DEFAULT_REDIRECT_URL

    return {
      statusCode: 302,
      headers: {
        Location: redirectApplicationUri
      },
      multiValueHeaders: {
        "Set-Cookie": [
          `oidc_user_logged=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_access_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_refresh_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_id_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_expires_in=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_pi_sri=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`
        ]
      },
      body: JSON.stringify({ message: "Logged out successfully" })
    }
  } catch (error) {
    console.error("Error during logout:", error)
    return {
      statusCode: 302,
      headers: {
        Location: errorPageUrl
      },
      body: JSON.stringify({
        error: error.response?.data?.error || "Error during log out",
        error_description: error.response?.data?.error_description || error.message
      })
    }
  }
}
