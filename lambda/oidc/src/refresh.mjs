import axios from "axios"
import { extractCookies } from "./oidcUtils.mjs"
import { getKeysForBrand } from "../../utils/lambdaUtils.mjs"

export const refresh = async (event, baseUrl, cookieDomain, targetUrl) => {
  try {
    const clientId =
      process.env.ENVIRONMENT === "dev" ? process.env.FAIRMONT_CLIENT_ID_DEV : process.env.FAIRMONT_CLIENT_ID_PROD
    const { partnerSecretKey } = await getKeysForBrand("fairmont") //TODO : rework for a generic way to manage brand name
    const credentials = `${clientId}:${partnerSecretKey}`
    const base64Credentials = Buffer.from(credentials).toString("base64")

    const cookies = extractCookies(event)
    const data = {
      grant_type: "refresh_token",
      refresh_token: cookies.oidc_refresh_token
    }

    const response = await axios.post(targetUrl, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${base64Credentials}`
      }
    })

    const {
      access_token: accessToken,
      refresh_token: refreshToken,
      id_token: idToken,
      expires_in: tokenExpiresIn
    } = response.data

    const isSecure = new URL(baseUrl).protocol === "https:"
    const cookieOptions = `HttpOnly; Path=/; SameSite=Lax; Domain=${cookieDomain}${isSecure ? "; Secure" : ""}`

    return {
      statusCode: 200,
      multiValueHeaders: {
        "Set-Cookie": [
          `oidc_user_logged=true; Path=/; SameSite=Lax; Domain=${cookieDomain}${isSecure ? "; Secure" : ""}`,
          `oidc_access_token=${accessToken}; ${cookieOptions}`,
          `oidc_refresh_token=${refreshToken}; ${cookieOptions}`,
          `oidc_id_token=${idToken}; ${cookieOptions}`,
          `oidc_expires_in=${tokenExpiresIn}; ${cookieOptions}`
        ]
      },
      body: JSON.stringify({ message: "Token refreshed" })
    }
  } catch (error) {
    console.error("Error during token refreshing:", error)
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.response?.data?.error || "Error during refresh token",
        error_description: error.response?.data?.error_description || error.message
      })
    }
  }
}
