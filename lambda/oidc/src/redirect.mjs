import axios from "axios"
import { getKeysFor<PERSON>rand } from "../../utils/lambdaUtils.mjs"
import { decodeBase64 } from "./oidcUtils.mjs"

export const redirect = async (event, baseUrl, cookieDomain, targetUrl, errorPageUrl, redirectUrl) => {
  const { code: authorizationCode } = event.queryStringParameters || {}

  if (!authorizationCode) {
    console.error(`Authorization error: No authorization code provided`)
    return {
      statusCode: 302,
      headers: {
        Location: errorPageUrl
      },
      body: JSON.stringify({ error: "Authorization error", error_description: "No authorization code provided" })
    }
  }

  try {
    const clientId =
      process.env.ENVIRONMENT === "dev" ? process.env.FAIRMONT_CLIENT_ID_DEV : process.env.FAIRMONT_CLIENT_ID_PROD
    const { partnerSecretKey } = await getKeysForBrand("fairmont") //TODO : rework for a generic way to manage brand name
    const credentials = `${clientId}:${partnerS<PERSON>ret<PERSON><PERSON>}`
    const base64Credentials = Buffer.from(credentials).toString("base64")

    const redirectApplicationUri = redirectUrl || baseUrl + process.env.FAIRMONT_APPLICATION_DEFAULT_REDIRECT_URL

    const data = {
      grant_type: "authorization_code",
      code: authorizationCode,
      redirect_uri: baseUrl + process.env.FAIRMONT_REDIRECT_PATH
    }

    const response = await axios.post(targetUrl, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${base64Credentials}`
      }
    })

    const {
      access_token: accessToken,
      refresh_token: refreshToken,
      id_token: idToken,
      expires_in: tokenExpiresIn
    } = response.data

    const idTokenDecode = decodeBase64(idToken.split(".")[1])
    const isSecure = new URL(baseUrl).protocol === "https:"
    const cookieOptions = `HttpOnly; Path=/; SameSite=Lax; Domain=${cookieDomain}${isSecure ? "; Secure" : ""}`

    return {
      statusCode: 302,
      headers: {
        Location: redirectApplicationUri
      },
      multiValueHeaders: {
        "Set-Cookie": [
          `oidc_user_logged=true; Path=/; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_access_token=${accessToken}; ${cookieOptions}`,
          `oidc_refresh_token=${refreshToken}; ${cookieOptions}`,
          `oidc_id_token=${idToken}; ${cookieOptions}`,
          `oidc_expires_in=${tokenExpiresIn}; ${cookieOptions}`,
          `oidc_pi_sri=${idTokenDecode?.["pi.sri"]}; ${cookieOptions}`
        ]
      },
      body: JSON.stringify({ message: `Redirecting to ${redirectApplicationUri}` })
    }
  } catch (error) {
    console.error("Error during token exchange:", error)
    return {
      statusCode: 302,
      headers: {
        Location: errorPageUrl
      },
      body: JSON.stringify({
        error: error.response?.data?.error || "Error during sign in",
        error_description: error.response?.data?.error_description || error.message
      })
    }
  }
}
