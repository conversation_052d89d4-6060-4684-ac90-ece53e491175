import { decodeBase64 } from "./oidcUtils.mjs"
import { logout } from "./logout.mjs"
import { redirect } from "./redirect.mjs"
import { refresh } from "./refresh.mjs"

export const handler = async (event) => {
  const { error, error_description: errorDescription, state } = event.queryStringParameters || {}
  const { path: requestPath = "" } = event

  const { redirectUrl, baseUrl, cookieDomain, userLang = "en" } = decodeBase64(state) || {}

  const currentEnv = process.env.ENVIRONMENT || "dev" //dev by default
  const errorPageUrl = `${baseUrl}/${userLang}${process.env.FAIRMONT_APPLICATION_ERROR_URL}`

  if (error || errorDescription || !redirectUrl) {
    console.error(`Authorization error: ${error} - ${errorDescription}`)

    return {
      statusCode: 302,
      headers: {
        Location: baseUrl + "/" + userLang + process.env.FAIRMONT_APPLICATION_ERROR_URL
      },
      body: JSON.stringify({ error, error_description: errorDescription })
    }
  }

  const loginUrl =
    currentEnv === "dev"
      ? process.env.ACCOR_CONVERT_URL.replace("https://", "https://rec-")
      : process.env.ACCOR_CONVERT_URL
  const logoutUrl =
    currentEnv === "dev"
      ? process.env.ACCOR_LOGOUT_URL.replace("https://", "https://rec-")
      : process.env.ACCOR_LOGOUT_URL
  const revokeUrl =
    currentEnv === "dev"
      ? process.env.ACCOR_REVOKE_REFRESH_TOKEN_URL.replace("https://", "https://rec-")
      : process.env.ACCOR_REVOKE_REFRESH_TOKEN_URL

  switch (requestPath) {
    case process.env.FAIRMONT_REDIRECT_PATH:
      return redirect(event, baseUrl, cookieDomain, loginUrl, errorPageUrl, redirectUrl)
    case process.env.FAIRMONT_REFRESH_PATH:
      return refresh(event, baseUrl, cookieDomain, loginUrl)
    case process.env.FAIRMONT_LOGOUT_PATH:
      return logout(event, baseUrl, cookieDomain, logoutUrl, revokeUrl, errorPageUrl, redirectUrl)
    default:
      return {
        statusCode: 302,
        headers: {
          Location: errorPageUrl
        },
        body: JSON.stringify({
          error: "Wrong Path",
          error_description: `OIDC lambda can't handle the path : ${requestPath}`
        })
      }
  }
}
