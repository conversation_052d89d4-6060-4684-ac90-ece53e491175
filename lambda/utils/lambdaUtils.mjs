import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";

const smClient = new SecretsManagerClient({
  region: process.env.AWS_REGION || "eu-west-1", // adjust region as needed
});

export async function getKeysForBrand(brand) {
  console.log("Getting keys for Brand:", brand)

  // const API_KEYS = JSON.parse(process.env.API_KEYS || '{}')
  const apiKeyName = `API_KEY_BRAND_${brand.toUpperCase()}`
  const secretKeyName = `PARTNER_SECRET_KEY_BRAND_${brand.toUpperCase()}`

  if (process.env.API_KEYS) {
    console.warn("Using clair ENV injected API_KEYS");
    const secret = JSON.parse(process.env.API_KEYS);
    return {
      apiKey: secret[apiKeyName],
      partnerSecretKey: secret[secretKeyName],
    };
  } else {
    const secretName = process.env.API_KEYS_SEC;
    const secretValueResponse = await smClient.send(
      new GetSecretValueCommand({ SecretId: secretName })
    );

    const secret = JSON.parse(secretValueResponse.SecretString);

    return {
      apiKey: secret[apiKeyName],
      partnerSecretKey: secret[secretKeyName]
    }
  }  
}