service: brands-service-api

provider:
  name: aws
  runtime: nodejs20.x
  region: us-east-1
  environment:
    API_KEYS: '{ "API_KEY_BRAND_FAIRMONT": "egwcVFGcv6IEgELDoPWcJ0bxjSUdOYaX", "PARTNER_SECRET_KEY_BRAND_FAIRMONT": "0F8bKOa46geOej2G7oCs9aUxWif4kZGY6MdxEoYZ2PKxMmE8nddYXS5LoXDnEIch" }'
    ENVIRONMENT: dev
    

functions:
  api:
    handler: src/index.handler
    events:
      - http:
          path: /booking/api/{proxy+}
          method: "*"

plugins:
  - serverless-dotenv-plugin
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0
    timeout: 30
    keepAliveTimeout: 60000 # Add this line

