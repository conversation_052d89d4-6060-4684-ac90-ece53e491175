import express from "express"
import serverless from "@vendia/serverless-express"
import { createProxyMiddleware } from "http-proxy-middleware"
import { getKeysForBrand } from "../../utils/lambdaUtils.mjs"

//Get correct env
const currentEnv = process.env.ENVIRONMENT || "dev"; //dev by default
const targetApiUrl = (currentEnv === "dev") ? process.env.ACCOR_API_URL.replace('https://', 'https://rec-') : process.env.ACCOR_API_URL;

const noHttpOnlyCookies = ["affcookie"]
const handlerPromise = (async () => {
  //Get API key from ENV
  const { apiKey } = await getKeysForBrand('fairmont') //TODO : rework for a generic way to manage brand name (using client id)
  const app = express()

  
  app.use(
    createProxyMiddleware({
      changeOrigin: true,
  
      on: {
        proxyReq: (proxyReq, req) => {
          proxyReq.setHeader("Apikey", apiKey)
          proxyReq.removeHeader("via")
  
          if (req.url.includes(process.env.CONTACT_ME_URL)) {
            const cookies = extractCookies(req)
  
            if (cookies.oidc_access_token) {
              const oidcAccessToken = cookies.oidc_access_token
              proxyReq.setHeader("Authorization", `Bearer ${oidcAccessToken}`)
            }
          }
        },
        proxyRes: (proxyRes, req) => {
          const { app_domain: appDomain } = extractCookies(req)
  
          // Remove transfer-encoding because api gateway don't handle chunk
          delete proxyRes.headers["transfer-encoding"]
          // Rewrite set cookie on dev env to be able to set it
          if (proxyRes.headers["set-cookie"]) {
            proxyRes.headers["set-cookie"] = proxyRes.headers["set-cookie"].map((setCookie) => {
              //TODO remove samesite and secure replace when https is merged on develop (feature/CONVERT-479)
              let modifiedCookie = setCookie
                .replace("SameSite=none", "SameSite=lax")
                .replace("SameSite=None", "SameSite=lax")
                .replace(".accor.com", appDomain); //keep cookies on running domain
              
              // Remove HttpOnly for specific cookies
              for (const noHttpOnlyCookie of noHttpOnlyCookies) {
                if (setCookie.toLowerCase().includes(noHttpOnlyCookie.toLowerCase())) {
                  modifiedCookie = modifiedCookie.replace(/;\s*HttpOnly/gi, '');
                }
              }
              return modifiedCookie;
            })
          }
        },
        error: (err, req, res) => {
          console.log("Proxy error:", err)
          res.status(504).send("Proxy failed: " + err.message)
        }
      },
      logger: console,
      target: targetApiUrl,
    })
  )

  return serverless({ app });
})()


function extractCookies(req) {
  const cookieIndex = req.rawHeaders.indexOf("cookie")
  const cookieHeader = cookieIndex !== -1 ? req.rawHeaders[cookieIndex + 1] : null
  if (cookieHeader) {
    return cookieHeader.split(";").reduce((acc, cookie) => {
      const [name, value] = cookie.trim().split("=")
      acc[name] = value
      return acc
    }, {})
  }
  return {}
}


// export const handler = serverless({ app })

export const handler = async (event, context) => {
  const handler = await handlerPromise;
  return handler(event, context);
};
