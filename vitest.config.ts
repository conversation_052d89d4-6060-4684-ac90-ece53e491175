import { configDefaults, defineConfig, mergeConfig } from "vitest/config"
import viteConfig from "./vite.config"

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      browser: {
        headless: true
      },
      coverage: {
        all: true,
        exclude: [
          "**/node_modules/**",
          "**/coverage/**",
          "**/e2e/**",
          "**/*.config.*",
          "**/*.eslintrc.cjs",
          "**/__tests__/*",
          "**/dist/*",
          "create_pack.js",
          "./*"
        ],
        include: ["apps/**"],
        provider: "istanbul",
        reporter: ["text", "lcov", "json"],
        reportsDirectory: "coverage"
      },
      environment: "happy-dom",
      exclude: [
        ...configDefaults.exclude,
        "**/e2e/**",
        "**/*.config.*",
        "**/*config.*",
        "**/coverage/",
        "**/dist/*",
        "**/**" //Exclude all files in workspace root
      ],
      fakeTimers: {
        toFake: [...(configDefaults.fakeTimers.toFake ?? []), "performance", "Date"]
      },
      globals: false
    }
  })
)
