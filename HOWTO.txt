### DEPRECATED ###

#install pnpm & init
npm install -g pnpm
pnpm init -y

#create workspace : pnpm-workspace.yaml
packages:
  - 'apps/*' #Application
  - 'shared/*' #Common components

#create app
pnpm create vite apps/app1

#Install all dependencies
pnpm install

#Add global dependecies
pnpm add vue -w
pnpm add -D vitest -w

#Add global dependencies for apps
pnpm --filter "apps/*" add vue-router @vueuse/core 

#Create a common vue component
cd shared/components
mkdir header & cd header
