# vim: syntax=nginx:ft=conf:

ssl_certificate     conf.d/server.crt;
ssl_certificate_key conf.d/server.key;

proxy_http_version 1.1;
proxy_buffering off;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection "upgrade";
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_pass_request_headers  on;

access_log off;

server {
    listen 80 default_server;
    listen 443 default_server ssl;
    server_name local.booking.fairmont.com;

    location /booking/oidc/ {
        proxy_pass http://oidc-lambda:3000;
    }

    location /booking/api/ {
        proxy_pass http://api-proxy:3000;
    }

    location / {
        proxy_pass http://frontend-fairmont:3001;
    }
}

server {
    listen 80;
    listen 443 ssl;
    server_name local.storybook.fairmont.com;

    location / {
        proxy_pass http://storybook:6006;
    }

    location /storybook-server-channel {
        proxy_pass http://storybook:6006/storybook-server-channel;
    }
}
