FROM node:22.13.0-alpine

RUN apk update && apk add curl bash git

# Fix user / node ID
RUN USER=node && \
    GROUP=node && \
    curl -SsL https://github.com/boxboat/fixuid/releases/download/v0.5.1/fixuid-0.5.1-linux-amd64.tar.gz | tar -C /usr/local/bin -xzf - && \
    chown root:root /usr/local/bin/fixuid && \
    chmod 4755 /usr/local/bin/fixuid && \
    mkdir -p /etc/fixuid && \
    printf "user: $USER\ngroup: $GROUP\n" > /etc/fixuid/config.yml

RUN curl -SsL https://raw.githubusercontent.com/eficode/wait-for/v2.2.3/wait-for -o /usr/local/bin/wait-for && \
    chown root:root /usr/local/bin/wait-for && \
    chmod 4755 /usr/local/bin/wait-for

# Will be overrided by docker-compose
WORKDIR /usr/src/app

COPY --chmod=0755 ./entrypoint.sh /entrypoint.sh
# Convert line endings and make executable
RUN chmod +x /entrypoint.sh && \
    sed -i 's/\r$//' /entrypoint.sh
    
ENTRYPOINT ["/entrypoint.sh"]
