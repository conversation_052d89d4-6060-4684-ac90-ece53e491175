import eslint from "@eslint/js"
import eslintConfig<PERSON>rettier from "eslint-config-prettier"
import eslintPluginVue from "eslint-plugin-vue"
import importPlugin from "eslint-plugin-import"
import pluginPlaywright from "eslint-plugin-playwright"
import pluginVitest from "@vitest/eslint-plugin"
import skipFormatting from "@vue/eslint-config-prettier/skip-formatting"
import typescriptEslint from "typescript-eslint"

export default typescriptEslint.config(
  { ignores: ["**/dist/**", "**/dist-ssr/**", "**/coverage/**", "**/*.{js}", "**/public/**"] },
  {
    settings: {
      "import/resolver": {
        node: {
          extensions: [".js", ".ts", ".vue"]
        }
      }
    },
    extends: [
      eslintConfigPrettier,
      eslint.configs.recommended,
      importPlugin.flatConfigs.recommended,
      ...typescriptEslint.configs.recommended,
      ...eslintPluginVue.configs["flat/recommended"]
    ],
    files: ["**/*.{ts,vue}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        parser: typescriptEslint.parser
      }
    },
    rules: {
      "import/extensions": [
        "error",
        "never",
        {
          vue: "always",
          json: "always",
          svg: "always",
          js: "none"
        }
      ],
      "import/named": "off",
      "import/no-unresolved": "off",
      "no-console": "error",
      "sort-keys": "warn",
      "sort-imports": [
        "error",
        {
          ignoreCase: false,
          ignoreDeclarationSort: false,
          ignoreMemberSort: false,
          memberSyntaxSortOrder: ["none", "all", "multiple", "single"],
          allowSeparatedGroups: false
        }
      ],
      "vue/component-name-in-template-casing": "error",
      "vue/multi-word-component-names": "off",
      "vue/order-in-components": "off"
    }
  },
  {
    ...pluginVitest.configs.recommended,
    files: ["src/**/__tests__/*"]
  },

  {
    ...pluginPlaywright.configs["flat/recommended"],
    files: ["e2e/**/*.{test,spec}.{js,ts,jsx,tsx}"]
  },
  skipFormatting
)
