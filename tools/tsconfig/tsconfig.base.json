{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    // Emit more modern JavaScript for smaller bundles & faster execution in modern browsers.
    // You can also try "ESNext" or "ES2022".
    "target": "ES2020",

    // The standard DOM library plus ESNext built-ins.
    // 'DOM.Iterable' adds support for things like NodeList iteration, etc.
    "lib": ["DOM", "DOM.Iterable", "ESNext"],

    // Tells TS to generate native ES modules and rely on bundlers like Vite to handle them.
    "module": "ESNext",

    // Needed so Node-based imports (like 'path', 'fs') are resolved as you’d expect.
    "moduleResolution": "node",

    // Turn on strict mode for better type-checking.
    "strict": true,

    // Often used in Vue or React environments to keep JSX/TSX syntax untransformed by TS
    // so that Vite (or Babel) can do the final transformation.
    // If you’re not using JSX/TSX, you can remove it.
    "jsx": "preserve",

    // If you have any JS files in your project that you want TypeScript to check, keep this true.
    // If everything is TS, you can set it to false.
    "allowJs": true,

    // Allows default imports from modules with no default export
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,

    // Skips type checking of all declaration files (speeds up compilation).
    "skipLibCheck": true,

    // Ensures consistent file casing.
    "forceConsistentCasingInFileNames": true,

    // Makes sure each file is treated as a separate module.
    // Recommended if you’re compiling each file separately (as Vite does) rather than with a full program-based compilation.
    "isolatedModules": true,

    // Allows importing JSON files.
    "resolveJsonModule": true,

    // Turn off emitting .js files because Vite (or another bundler) will handle it.
    "noEmit": true,

    // Set a baseUrl so that you can create nice aliases if you want (with "paths").
    "baseUrl": ".",

    // Example aliasing so you can do import ... from "@/something" instead of relative paths.
    // You can remove or adjust if you don’t need it.
    "paths": {
      "@/*": ["src/*"]
    }
  },
  // Include any source files you want TS to type-check.
  // Make sure to pick up .vue files if you’re using Vue Single File Components.
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue"],

  // Exclude node_modules and your build output folder.
  "exclude": ["node_modules", "dist"]
}
