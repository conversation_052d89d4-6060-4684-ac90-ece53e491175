import { createP<PERSON>, setActive<PERSON><PERSON> } from "pinia"
import { beforeEach } from "vitest"
import { config } from "@vue/test-utils"
import { i18n } from "../../apps/Common/src/i18n"

// ⚠ mocking external packages (i.e. axios) from here won't work!
//    vi.mock requires to be run under the same root as the project
//    @see apps/Common/vitest.config.ts for an example of using a
//    local setup file

// @ts-expect-error Description: This error is expected.
config.global = {
  plugins: [i18n]
}

beforeEach(() => {
  setActivePinia(createPinia())
})

Element.prototype.scrollBy = () => {}
