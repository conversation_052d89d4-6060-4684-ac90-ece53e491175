const { exec } = require("child_process")
const util = require("util")
const path = require("path")
const execAsync = util.promisify(exec)

// Helper function to normalize paths
function normalizePath(filePath) {
  // Convert Windows paths to posix style and ensure forward slashes
  const currentWS = path.resolve(__dirname)
  //.replace(/\\/g, '/').replace(/^[A-Z]:\//, '');
  const normalizedPath = path
    .relative(currentWS, filePath)
    .replace(/\\/g, "/")
    .replace(/^[A-Z]:\//, "")
  return normalizedPath
}

async function getLastReleaseRef() {
  try {
    // Get all branches matching release/* pattern, sorted by committerdate
    const { stdout } = await execAsync('git branch -r --list "origin/release/*" --sort=-committerdate')
    const releases = stdout
      .split("\n")
      .filter(Boolean)
      .filter((branch) => !branch.includes("release/0.0.0"))
    const previousRelease = releases[1] // Get second release branch instead of first
    if (!previousRelease) {
      throw new Error("No previous release branch found")
    }

    console.log("Updating version compared to previous version : ", previousRelease)

    // Remove 'origin/' prefix if present
    return previousRelease
  } catch (error) {
    console.error("Error getting last release branch:", error)
    throw error
  }
}

async function detectChanges(fromRef, toRef = "HEAD") {
  const currentVersion = require("./package.json").version
  console.log("Current version : ", currentVersion)
  try {
    const { stdout } = await execAsync(`git diff --name-only ${fromRef}^ ${toRef}`)

    // Get all workspace packages
    const { stdout: workspaces } = await execAsync("pnpm ls -r --json")
    const packages = JSON.parse(workspaces)

    // Filter packages with changes
    const changedFiles = stdout.trim().split("\n")
    const changedPackages = packages.filter((pkg) => {
      const normalizedPkgPath = normalizePath(pkg.path)

      return changedFiles.some((file) => {
        const normalizedFile = normalizePath(file)
        return normalizedFile.startsWith(normalizedPkgPath)
      })
    })

    return changedPackages
  } catch (error) {
    console.error("Error detecting changes:", error)
    return []
  }
}

async function updatePackageVersions(versionBump = "patch") {
  let lastRelease = "HEAD"
  if (versionBump !== "patch") {
    lastRelease = await getLastReleaseRef()
  }
  const updatedPackages = await detectChanges(lastRelease)

  if (updatedPackages.length === 0) {
    console.log("No packages have changed")
    return
  }

  for (const pkg of updatedPackages) {
    try {
      console.log(`Updating version for ${pkg.name}...`)
      // Execute pnpm version in the package directory
      await execAsync(`cd "${pkg.path}" && pnpm version ${versionBump} --no-git-tag-version`)
    } catch (error) {
      console.error(`Error updating version for ${pkg.name}:`, error)
    }
  }
}

const bumpType = process.argv[2] || "patch"
updatePackageVersions(bumpType)
