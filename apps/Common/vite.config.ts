/* eslint-disable sort-keys */
import { AliasOptions, defineConfig, loadEnv } from "vite"
import { URL, fileURLToPath } from "node:url"
import copy from "rollup-plugin-copy"
import svgLoader from "vite-svg-loader"
import { visualizer } from "rollup-plugin-visualizer"
import vue from "@vitejs/plugin-vue"
//import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const base_url = env.VITE_BASE_URL

  const alias: AliasOptions = {
    "@": fileURLToPath(new URL("./src", import.meta.url)),
    "@stores": fileURLToPath(new URL("../Common/src/stores", import.meta.url)),
    "@relative-assets": fileURLToPath(new URL("./src/assets", import.meta.url)),
    "@sb-base": "design-system/styles/base",
    "@sb-config": "design-system/styles/config",
    "@sb-fonts": "design-system/styles/fonts",
    "@sb-styles": fileURLToPath(new URL("../../storybook/src/styles", import.meta.url)),
    "@sb-utilities": "design-system/styles/utilities"
  }

  /**
   * Bypass the builded library to use the files directly
   */
  const devMode = mode === "development"
  if (devMode) {
    Object.assign(alias, {
      "design-system/main.scss": fileURLToPath(new URL("../../storybook/src/styles/main.scss", import.meta.url)),
      "design-system/base/*": fileURLToPath(new URL("../../storybook/src/styles/base/*", import.meta.url)),
      "design-system/config/*": fileURLToPath(new URL("../../storybook/src/styles/config/*", import.meta.url)),
      "design-system/fonts/*": fileURLToPath(new URL("../../storybook/public/fonts/*", import.meta.url)),
      "design-system/utilities/*": fileURLToPath(new URL("../../storybook/src/styles/utilities/*", import.meta.url)),
      "design-system": fileURLToPath(new URL("../../storybook/src", import.meta.url))
    })
  }

  return {
    base: base_url,
    build: {
      assetsDir: "",
      outDir: `dist/${base_url}`,
      target: "es2022",
      cssMinify: true,
      minify: true,
      emptyOutDir: true,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes("intlify") || id.includes("i18n")) {
              return "localization"
            }

            if (id.includes("opentelemetry") || id.includes("splunk")) {
              return "monitoring"
            }

            if (id.includes("storybook/dist/lib")) {
              return "design-system"
            }

            if (id.includes("node_modules")) {
              return "vendors"
            }

            return undefined
          }
        }
      },
      sourcemap: devMode
    },
    css: {
      preprocessorOptions: {
        sass: {
          // api: "modern"
          silenceDeprecations: ["legacy-js-api"]
        },
        scss: {
          // api: "modern"
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    },
    plugins: [
      vue(),
      svgLoader(),
      {
        ...copy({
          hook: "never",
          targets: [
            {
              dest: "public/assets/locales",
              src: "../../node_modules/@accor/ads-components-locales/src/locales/*"
            }
          ]
        }),
        apply: "build"
      },
      {
        name: "html-transform",
        transformIndexHtml(html: string) {
          let output = html

          const prefetchUrls = [process.env.VITE_APP_API_BASE_URL!].filter(Boolean) as string[]
          const prefetchLinks = prefetchUrls.map((url) => `<link rel="dns-prefetch" href="${url.trim()}">`).join("\n")
          const preconnectLinks = prefetchUrls.map((url) => `<link rel="preconnect" href="${url.trim()}">`).join("\n")

          if (preconnectLinks.length || prefetchLinks.length) {
            output = html.replace("<!-- PREFETCH_PLACEHOLDER -->", [...preconnectLinks, ...prefetchLinks].join("\n"))
          }

          output = html
            .split("\n")
            .map((line) => {
              if (line.includes('<link rel="modulepreload"')) {
                return line.replace('<link rel="modulepreload"', '<link rel="modulepreload" as="script"')
              }
              return line
            })
            .join("\n")

          return output
        }
      },
      visualizer()
    ],
    resolve: {
      alias
    },
    server: {
      host: "0.0.0.0",
      port: 3001
    }
  }
})
