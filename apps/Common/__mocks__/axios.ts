import { vi } from "vitest"

const mockMe = (...args) => {
  throw new Error(`Trying to execute an axios http request!! Missing a mock?\n${JSON.stringify(args)}`)
}

export default {
  create: vi.fn().mockReturnThis(),
  delete: vi.fn(mockMe),
  get: vi.fn(mockMe),
  interceptors: {
    request: {
      eject: vi.fn(),
      use: vi.fn()
    },
    response: {
      eject: vi.fn(),
      use: vi.fn()
    }
  },
  post: vi.fn(mockMe),
  put: vi.fn(mockMe)
}
