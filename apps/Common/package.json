{"name": "@apps/common", "version": "0.17.1", "private": false, "type": "module", "scripts": {"clean": "rm -rf node_modules && rm -rf dist", "dev": "vite", "preview": "vite preview --host local.booking.fairmont.com --open", "test:unit": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@accor/business-referer": "0.0.3-RC.0", "@shared/components": "workspace:*", "@shared/composables": "workspace:*", "@shared/payment": "workspace:*", "@shared/types": "workspace:*", "@shared/utils": "workspace:*", "@splunk/otel-web": "^0.22.0", "@vueuse/core": "^13.4.0", "@vueuse/integrations": "^13.1.0", "@vueuse/router": "^13.1.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "design-system": "workspace:*", "include-media": "^2.0.0", "js-cookie": "^3.0.5", "json-stable-stringify": "^1.3.0", "normalize.css": "^8.0.1", "pinia": "^2.2.4", "pino": "^9.7.0", "qs": "^6.14.0", "sass": "^1.83.4", "universal-cookie": "^7", "vue-i18n": "^11.1.7", "vue-router": "^4.4.5", "zod": "^3.24.3"}, "devDependencies": {"@playwright/test": "^1.48.0", "@tools/tsconfig": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/qs": "^6.9.18", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^6.0.1", "sass-embedded": "^1.89.2", "vite-svg-loader": "^5.1.0"}}