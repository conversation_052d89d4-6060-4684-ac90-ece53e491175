import { computed } from "vue"
import { useSearch } from "./useSearch"

export function useCurrentRoom() {
  const { rooms } = useSearch()

  const currentRoomIndex = computed(() => {
    const index = rooms.value.findIndex((room) => !room.productCode || !room.rateCode)

    if (index === -1) {
      return rooms.value.length - 1
    }

    return index
  })

  const currentRoom = computed({
    get() {
      return rooms.value[currentRoomIndex.value]
    },
    set(newValue) {
      rooms.value[currentRoomIndex.value] = newValue
    }
  })

  const haveNextRoom = computed(() => {
    const lastRoom = rooms.value[rooms.value.length - 1]
    return !lastRoom.productCode || !lastRoom.rateCode
  })

  return {
    currentRoom,
    currentRoomIndex,
    haveNextRoom
  }
}
