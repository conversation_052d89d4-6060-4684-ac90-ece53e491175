import { AppRoutes } from "../router/router.enum"
import { computed } from "vue"
import { useRoute } from "vue-router"

export function useStepRoutes() {
  const stepRoutes = [AppRoutes.SEARCH, AppRoutes.STAY]

  const route = useRoute()

  const currentStepIndex = computed(() => stepRoutes.indexOf((route.name?.toString() || "") as AppRoutes))

  const currentStep = computed(() => currentStepIndex.value + 1)

  return { currentStep, currentStepIndex, stepRoutes }
}
