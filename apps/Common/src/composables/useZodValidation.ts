import { Ref, isRef } from "vue"
import { Schema, z } from "zod"
import { i18n } from "../i18n"

// Validation with Zod
// Documentation is here : https://zod.dev/
export const useZodValidation = <T>(data: Ref<T> | T, schema: Schema<T>) => {
  const value = isRef(data) ? data.value : data
  return schema.safeParse(value)
}

// iataSchema validation :
const iataErrorMessage = i18n.global.t("errors.invalid_iata_format")

export const iataSchema = z.union([
  // first case for string convertible as a number with 8 digits
  z.string().regex(/^[0-9]{8}$/, { message: iataErrorMessage }),
  // second case for string with zero characters because the input is optional
  z.string().length(0, { message: iataErrorMessage }),
  // third case for the undefined
  z.void()
])
