import { computed, nextTick, reactive, ref, watch } from "vue"
import { Currency } from "../global/enums"
import { fetchPosCountryLanguageAuto } from "../services/pos/pos.fetch"
import { i18n } from "../i18n"
import router from "../router"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { usePosLanguageHandler } from "./usePosLanguageHandler"

type UserPOS = {
  countryMarket: string
  currency: Currency | string
  fullLocale: string
  locale: string
}

const initialSynchronization = ref(false)

const userPOS = reactive<UserPOS>({
  countryMarket: "",
  currency: "",
  fullLocale: "",
  locale: i18n.global.locale.value
})

export const useUserPOS = () => {
  const route = computed(() => router.currentRoute.value)
  const cookies = useCookies()
  const routeLocale = computed(() => route.value?.params.locale as string | undefined)
  const { resetState: resetLanguageState } = usePosLanguageHandler()

  const refreshPOS = () => {
    userPOS.currency = cookies.get("userCurrency")
    userPOS.locale = cookies.get("userLang")
    userPOS.countryMarket = cookies.get("userLocalization")?.toUpperCase() || ""

    const locale = userPOS.locale && userPOS.countryMarket ? `${userPOS.locale}-${userPOS.countryMarket}` : ""

    if (locale) {
      const resolved = new Intl.DateTimeFormat(locale).resolvedOptions().locale
      userPOS.fullLocale = resolved === locale ? locale : ""
    } else {
      userPOS.fullLocale = ""
    }
  }

  const getPosCountryLanguageAutoParams = () => {
    const [language, country] = navigator.language.split("-")
    return {
      // TODO: Add geoIP from the CDN (waiting for CDN access infos https://accor-it.atlassian.net/browse/CONVERT-121)
      country: country ? country.toLowerCase() : "us",
      language: language ? language.toLowerCase() : "en"
    }
  }

  /**
   * Refresh POS when the locale of the route changed.
   * It's done at this moment to ensure that all the set cookie which are API call related
   * are done.
   */
  const onAppLanguageRouteSet = async (
    newLocale: string | string[] | undefined,
    oldLocale: string | string[] | undefined
  ) => {
    if (!newLocale || newLocale === oldLocale || initialSynchronization.value) return

    initialSynchronization.value = true

    const userCurrencyCookie = cookies.get("userCurrency")
    const userLangCookie = cookies.get("userLang")
    const userLocalizationCookie = cookies.get("userLocalization")

    if (
      !userCurrencyCookie ||
      !userLangCookie ||
      !userLocalizationCookie ||
      userLangCookie?.toLowerCase() !== newLocale
    ) {
      const { country } = getPosCountryLanguageAutoParams()
      await fetchPosCountryLanguageAuto(
        country,
        typeof newLocale === "string" ? newLocale : newLocale[0],
        userCurrencyCookie
      )
    }

    nextTick(() => {
      refreshPOS()
      resetLanguageState()
      listener.stop()
    })
  }

  const listener = watch(routeLocale, onAppLanguageRouteSet, {
    immediate: true
  })

  return {
    refreshPOS,
    userPOS
  }
}
