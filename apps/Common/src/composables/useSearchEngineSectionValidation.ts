import { DateMoment, UiRoomSelectDefault, filterDestinationsDesktop, scrollToElement } from "design-system"
import { computed, ref } from "vue"
import {
  datePickerInputsValidation,
  datePickerValidation,
  destinationValidation,
  iataSchema,
  useGA4Event,
  useIdentificationPlainText,
  useLoader,
  useSearch,
  useSearchQueryParams,
  useZodValidation
} from "../composables"
import { AppRoutes } from "../router/router.enum"
import { EventNameEnum } from "../global/enums"
import { differenceInCalendarDays } from "date-fns"
import { mapApiHotelsToDestinationDesktop } from "../services/hotels/hotels.mapper"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useRouter } from "vue-router"
import { useSearchValidation } from "./useSearchValidation"
import { validateSpecialRateWithPromoCode } from "../helpers/specialRatesValidation"

const destinationDropdownIsOpen = ref(false)
const destinationDropdownIsValid = ref(true)
const destinationErrorMessage = ref<string | undefined>()

const datePickerErrorMessage = ref<string | undefined>()
const datePickerInputsAreValid = ref({ endDate: true, startDate: true })
const datePickerDropdownReference = ref()

const iataCodeErrorMessage = ref<string>()
const promoCodeErrorMessage = ref<string>()

export const useSearchEngineSectionValidation = () => {
  const { loading } = useLoader("search")
  const {
    dates,
    iataCode,
    hasRedirectIataCode,
    hotel,
    isHotelInBlacklist,
    promoCode,
    promoCodeIsValid,
    rooms,
    specialRate
  } = useSearch()
  const { hotelsList } = useHotelsStore()
  const { pushGA4Event } = useGA4Event()
  const { queryParams } = useSearchQueryParams()
  const { refreshIdentification } = useIdentificationPlainText()
  const router = useRouter()
  const { t } = useI18n()

  const destinationInput = ref<string>(hotel.value?.name || "")

  const localStartDate = ref<string | undefined>(dates.value?.[DateMoment.START]?.toString())
  const localEndDate = ref<string | undefined>(dates.value?.[DateMoment.END]?.toString())

  const scrollOffset = -30 // the px value offset on the scrollTo

  const mappedHotels = computed(() => mapApiHotelsToDestinationDesktop(hotelsList))

  const roomsAndGuestsInError = computed(() => {
    const errorIndexes: number[] = []
    rooms.value?.forEach((room, index) => {
      if (room.hasErrorMessage) {
        errorIndexes.push(index)
      }
    })
    return errorIndexes
  })

  const filteredDestinations = computed(() => {
    return filterDestinationsDesktop(mappedHotels.value, destinationInput)
  })

  const filteredDestinationsFallback = computed(() => {
    return filteredDestinations.value.length === 0 ? mappedHotels.value : filteredDestinations.value
  })

  const destinationErrorMessageValidation = (allowEmptyString?: boolean) => {
    destinationErrorMessage.value = destinationValidation(
      destinationInput.value,
      filteredDestinations.value,
      allowEmptyString
    )

    if (hotel.value || !destinationInput.value || destinationInput.value === "") {
      destinationDropdownIsValid.value = true
    }
  }

  const validateDestinationDropdown = () => {
    destinationErrorMessageValidation()
    destinationDropdownIsValid.value = !!hotel.value
  }

  const clearLocalDates = () => {
    localStartDate.value = undefined
    localEndDate.value = undefined
  }

  const generateFixedDates = (datesModel: Date[], datesObject?: { endDate?: Date; startDate?: Date }) => {
    let startDate = ""
    let endDate = ""

    if (datesModel.length === 1 && datesObject?.startDate && !datesObject?.endDate) {
      startDate = datesModel?.[DateMoment.START]?.toString()
    } else if (datesModel.length === 1 && !datesObject?.startDate && datesObject?.endDate) {
      endDate = datesModel?.[DateMoment.START]?.toString()
    } else if (datesModel.length === 2) {
      startDate = datesModel?.[DateMoment.START]?.toString()
      endDate = datesModel?.[DateMoment.END]?.toString()
    }

    return { endDate, startDate }
  }

  const datePickerDatesValidation = (datesObject?: { endDate?: Date; startDate?: Date }) => {
    const { endDate, startDate } = generateFixedDates(dates.value, datesObject)

    datePickerErrorMessageValidation({ endDate: endDate, startDate: startDate }, true)
  }

  const datePickerErrorMessageValidation = (
    datesObject: { startDate: string; endDate: string },
    allowEmptyDates?: boolean
  ) => {
    localStartDate.value = datesObject.startDate
    localEndDate.value = datesObject.endDate

    if (
      datesObject?.startDate &&
      datesObject?.endDate &&
      Math.abs(differenceInCalendarDays(datesObject.startDate, datesObject.endDate)) <= 30
    ) {
      datePickerInputsAreValid.value.startDate = true
      datePickerInputsAreValid.value.endDate = true
    } else if (datesObject?.startDate && datesObject?.startDate !== "" && datesObject?.startDate !== "-") {
      datePickerInputsAreValid.value.startDate = true
    } else if (datesObject?.endDate && datesObject?.endDate !== "" && datesObject?.endDate !== "-") {
      datePickerInputsAreValid.value.endDate = true
    }

    datePickerErrorMessage.value = datePickerValidation(localStartDate.value, localEndDate.value, allowEmptyDates)
  }

  const validateDatePicker = () => {
    const datesObject = {
      endDate: localEndDate.value || "",
      startDate: localStartDate.value || ""
    }

    datePickerErrorMessageValidation(datesObject)
    datePickerInputsAreValid.value = datePickerInputsValidation(localStartDate.value, localEndDate.value)
  }

  const roomsAndGuestsAllDropdownsValidation = () => {
    rooms.value.forEach((room) => {
      const roomDropdownsInError: number[] = []

      room.childrenAges.forEach((age, index) => {
        if (age === UiRoomSelectDefault) {
          room.hasErrorMessage = true
          roomDropdownsInError.push(index)
        }
      })

      room.dropdownsInError = [...new Set(roomDropdownsInError)]
    })
  }

  const roomsAndGuestsSingleDropdownValidation = (dropdown: { roomIndex: number; dropdownIndex: number }) => {
    if (rooms.value[dropdown.roomIndex].childrenAges[dropdown.dropdownIndex] === UiRoomSelectDefault) {
      if (rooms.value[dropdown.roomIndex].dropdownsInError) {
        rooms.value[dropdown.roomIndex]?.dropdownsInError?.push(dropdown.dropdownIndex)
      } else {
        rooms.value[dropdown.roomIndex].dropdownsInError = [dropdown.dropdownIndex]
      }
    }

    // if dropdown and not the default value, we have to filter the index out of the array
    else {
      const filteredErrors = rooms.value[dropdown.roomIndex].dropdownsInError?.filter(
        (item) => item !== dropdown.dropdownIndex
      )
      rooms.value[dropdown.roomIndex].dropdownsInError = filteredErrors

      // At this point, there might be no more errors for the room,
      // so we revalidate the rooms to remove the error message if applicable
      roomsAndGuestsRoomsValidation(true)
    }
  }

  const roomsAndGuestsRoomsValidation = (onlyRemoveErrors?: boolean) => {
    rooms.value.forEach((room) => {
      if (room.dropdownsInError?.length && !onlyRemoveErrors) {
        room.hasErrorMessage = true
      } else if (room.dropdownsInError?.length === 0) {
        room.hasErrorMessage = false
      }
    })
  }

  const roomsAndGuestsErrorMessageValidation = () => {
    roomsAndGuestsAllDropdownsValidation()
    roomsAndGuestsRoomsValidation()
  }

  const validateSpecialRatesSection = () => {
    iataCodeErrorMessage.value = undefined
    promoCodeErrorMessage.value = undefined

    const validatedSchema = useZodValidation(iataCode.value, iataSchema)

    if (!validatedSchema.success) {
      const error = validatedSchema.error.format()
      iataCodeErrorMessage.value = error._errors[0]
    }

    if (!promoCodeIsValid.value) {
      promoCodeErrorMessage.value = t("errors.invalid_promo_code")
    }
  }

  const handleSearchEngineSectionValidation = async () => {
    pushGA4Event("step1", "check availability", {
      eventName: EventNameEnum.booking_form_submit
    })

    await refreshIdentification()

    validateDestinationDropdown()
    validateDatePicker()
    roomsAndGuestsErrorMessageValidation()
    validateSpecialRatesSection()

    const { errorFields, errorFieldsString } = useSearchValidation({
      datePickerIsValid: datePickerInputsAreValid.value?.endDate && datePickerInputsAreValid.value?.startDate,
      destinationIsValid: destinationDropdownIsValid.value,
      iataCodeError: iataCodeErrorMessage.value,
      promoCodeError: promoCodeErrorMessage.value
    })

    if (errorFields.value.length > 0) {
      pushGA4Event("step1", "check availability", {
        eventName: EventNameEnum.booking_form_interact,
        event_data: {
          error_field: errorFieldsString.value,
          // TODO: Wait po return, for the moment same to legacy
          error_type: "invalid value",
          form_action: "error"
        }
      })
    }

    if (!destinationDropdownIsValid.value) {
      const searchEngineSection = document.querySelector("#Destination-dropdown")
      if (searchEngineSection) {
        scrollToElement(searchEngineSection as HTMLElement, undefined, scrollOffset)
      }
      destinationDropdownIsOpen.value = true
    }

    // datepicker
    else if (!datePickerInputsAreValid.value?.endDate || !datePickerInputsAreValid.value?.startDate) {
      const datePicker = document.querySelector("#Date-picker-dropdown")
      if (datePicker) {
        scrollToElement(datePicker as HTMLElement, undefined, scrollOffset)
      }
      const moment = datePickerInputsAreValid.value?.startDate ? DateMoment.END : DateMoment.START
      datePickerDropdownReference.value.openDropdownModal(moment)
    }

    // rooms and guests
    else if (roomsAndGuestsInError.value?.length > 0) {
      const roomsAndGuests = document.querySelector("#Rooms-and-guests-section")
      if (roomsAndGuests) {
        scrollToElement(roomsAndGuests as HTMLElement, undefined, scrollOffset)
      }
    }

    // special rates
    else if (iataCodeErrorMessage.value || promoCodeErrorMessage.value) {
      const specialRates = document.querySelector("#Special-rates-section")
      if (specialRates) {
        scrollToElement(specialRates as HTMLElement, undefined, scrollOffset)
      }
    }

    // OK
    else {
      return true
    }
  }

  const handleSearchSubmit = async (blocInteractionPosition: string) => {
    const isValid = await handleSearchEngineSectionValidation()

    if (isValid) {
      await handleSearchEventLoading(blocInteractionPosition)
    }
  }

  const handleSearchEventLoading = async (blocInteractionPosition: string) => {
    if (hasRedirectIataCode() || isHotelInBlacklist()) {
      return
    }

    specialRate.value = validateSpecialRateWithPromoCode(specialRate.value, promoCode.value)

    pushGA4Event("step1", "check availability", {
      eventName: EventNameEnum.bloc_interact,
      event_data: {
        bloc_interaction: blocInteractionPosition,
        bloc_name: "check rates"
      }
    })

    loading.value = true

    if (hotel.value?.id && dates.value.length && promoCodeIsValid.value) {
      await router.push({
        path: AppRoutes.STAY,
        query: queryParams.value
      })
    }
  }

  return {
    clearLocalDates,
    datePickerDatesValidation,
    datePickerDropdownReference,
    datePickerErrorMessage,
    datePickerErrorMessageValidation,
    datePickerInputsAreValid,
    destinationDropdownIsOpen,
    destinationDropdownIsValid,
    destinationErrorMessage,
    destinationErrorMessageValidation,
    destinationInput,
    filteredDestinationsFallback,
    generateFixedDates,
    handleSearchEventLoading,
    handleSearchSubmit,
    iataCodeErrorMessage,
    promoCodeErrorMessage,
    roomsAndGuestsErrorMessageValidation,
    roomsAndGuestsInError,
    roomsAndGuestsSingleDropdownValidation,
    validateSpecialRatesSection
  }
}
