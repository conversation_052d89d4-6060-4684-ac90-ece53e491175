import { buildOidcUri, encodeState } from "../helpers/uriHelper"
import { useRoute, useRouter } from "vue-router"
import { AppRoutes } from "../router/router.enum"
import { OidcEnpoint } from "../global/enums"
import { computed } from "vue"
import { getGlobalConfig } from "../global/config"
import { useBasket } from "./useBasket"
import { useI18n } from "vue-i18n"
import { useSearch } from "./useSearch"
import { useSearchQueryParams } from "../composables"
import { useUserStore } from "@stores/user/user"

export function useOidcUri() {
  const { locale } = useI18n()
  const route = useRoute()
  const config = getGlobalConfig()
  const { queryParams } = useSearchQueryParams()
  const router = useRouter()
  const { basket } = useBasket()
  const { rooms } = useSearch()
  const { clearUserStore } = useUserStore()

  const uriInfos = computed(() => {
    return { locale: locale.value, origin: window.location.origin }
  })

  const getOidcState = computed(() => {
    const redirectUrl = `${uriInfos.value.origin}${config.app.baseUrl}${route.fullPath}`
    const baseUrl = uriInfos.value.origin
    const userLang = locale.value
    const cookieDomain = config.appDomain

    return { baseUrl, cookieDomain, redirectUrl, userLang }
  })

  const getOidcStateLogout = computed(() => {
    const stayRoute = router.resolve({
      name: AppRoutes.STAY,
      params: { locale: locale.value },
      query: queryParams.value
    }).href

    const redirectUrl =
      route.name !== AppRoutes.SEARCH
        ? stayRoute
        : `${uriInfos.value.origin}${import.meta.env.VITE_BASE_URL}${route.fullPath}`

    const baseUrl = uriInfos.value.origin
    const userLang = locale.value
    const cookieDomain = config.appDomain

    return { baseUrl, cookieDomain, redirectUrl, userLang }
  })

  const getOidcUri = (signup: boolean = false) => {
    return buildOidcUri(
      config.oidc.url,
      uriInfos.value.origin,
      config.oidc.ui,
      config.oidc.logo,
      uriInfos.value.locale,
      config.oidc.clientId,
      config.oidc.params,
      signup,
      getOidcState.value
    )
  }

  const signInUri = computed(() => getOidcUri(false))
  const signUpUri = computed(() => getOidcUri(true))
  const logOutUri = computed(() => {
    return OidcEnpoint.logout + "?state=" + encodeState(getOidcStateLogout.value)
  })
  const refreshUri = computed(() => {
    return OidcEnpoint.refresh + "?state=" + encodeState(getOidcState.value)
  })

  const logOut = () => {
    clearUserStore()

    basket.value = undefined

    rooms.value.forEach((room) => {
      room.productCode = undefined
      room.rateId = undefined
      room.rateCode = undefined
      room.classCode = undefined
    })

    window.location.href = logOutUri.value
  }

  return {
    logOut,
    logOutUri,
    refreshUri,
    signInUri,
    signUpUri,
    uriInfos
  }
}
