import { reactive } from "vue"

export enum Semaphores {
  cookieAttribution = "cookieAttribution",
  identification = "identification",
  userDetails = "userDetails"
}

const buildSemaphore = () => {
  let resolve: () => void = () => {}
  const promise = new Promise<void>((resolver) => {
    resolve = resolver
  })

  return {
    isResolved: false,
    promise,
    resolve
  }
}

// Default to true to ensure that all these calls are blocking others and ensure they are done in the right order
const callsInProgress = reactive<
  Record<Semaphores, { promise: Promise<unknown>; resolve: () => void; isResolved: boolean }>
>({
  cookieAttribution: buildSemaphore(),
  identification: buildSemaphore(),
  userDetails: buildSemaphore()
})

export const useApiSemaphore = () => {
  const setSemaphore = (semaphoreName: Semaphores, status: boolean) => {
    if (status) {
      const currentSemaphore = callsInProgress[semaphoreName]
      if (currentSemaphore.isResolved) {
        callsInProgress[semaphoreName] = buildSemaphore()
      }
    } else {
      callsInProgress[semaphoreName].isResolved = true
      callsInProgress[semaphoreName].resolve()
    }
  }

  const waitUntil = async (semaphoreName: Semaphores) => {
    await callsInProgress[semaphoreName].promise
  }

  return {
    setSemaphore,
    waitUntil
  }
}
