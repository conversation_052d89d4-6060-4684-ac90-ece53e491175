import { DefaultRoomDetails, Destination, RoomType, SpecialRates } from "design-system"
import { computed, ref } from "vue"
import { AllHotels } from "../global/consts"
import { differenceInCalendarDays } from "date-fns"
import { mapApiHotelsToDestinationDesktop } from "../services/hotels/hotels.mapper"
import { useHotelsStore } from "../stores/hotels/hotels"
import { useModal } from "./useModal"

export interface Search {
  dates: Date[]
  hotel?: Destination
  iataCode?: string
  promoCode?: string
  rooms: RoomType[]
  specialRate: SpecialRates
}

const today = new Date()
const tomorrow = new Date()
tomorrow.setDate(tomorrow.getDate() + 1)

const dates = ref<Date[]>([today, tomorrow])
const hotelCode = ref<string>()
const iataCode = ref<string>()
const promoCode = ref<string>()
const promoCodeIsValid = ref(true)
const rooms = ref<RoomType[]>([
  {
    ...DefaultRoomDetails,
    id: 0
  }
])
const specialRate = ref<SpecialRates>(SpecialRates.NONE)

export function useSearch() {
  const { hotelsList } = useHotelsStore()

  const { openModal: openHotelModal } = useModal("hotel")

  const dateIn = computed(() => dates.value[0])

  const dateOut = computed(() => dates.value[1])

  const flatHotels = computed(() => mapApiHotelsToDestinationDesktop(hotelsList).flatMap((list) => list.items))

  const hotel = computed<Destination | undefined>({
    get() {
      return flatHotels.value.find((h) => h.id === hotelCode.value)
    },
    set(newValue) {
      hotelCode.value = newValue?.id
    }
  })

  const lengthOfStay = computed(() => {
    if (dates.value.length !== 2) return 0

    return differenceInCalendarDays(dates.value[1], dates.value[0])
  })

  const nbAdults = computed(() =>
    rooms.value.reduce((acc: number, room) => {
      return acc + room.adults
    }, 0)
  )

  const nbChildren = computed(() =>
    rooms.value.reduce((acc: number, room) => {
      return acc + room.children
    }, 0)
  )

  const rateIds = computed(() =>
    rooms.value.reduce<string[]>((acc, room) => {
      if (typeof room.rateId === "string") {
        acc.push(room.rateId)
      }

      return acc
    }, [])
  )

  const productCodes = computed(() =>
    rooms.value.reduce<string[]>((acc, room) => {
      if (typeof room.productCode === "string") {
        acc.push(room.productCode)
      }

      return acc
    }, [])
  )

  const isHotelInBlacklist = () => {
    if (hotel.value?.id && AllHotels.includes(hotel.value?.id)) {
      openHotelModal()
      return true
    }

    return false
  }

  const { openModal: openTravelProModal } = useModal("travel-pro")
  const hasRedirectIataCode = () => {
    if (iataCode.value) {
      openTravelProModal()
      return true
    }
  }

  return {
    dateIn,
    dateOut,
    dates,
    hasRedirectIataCode,
    hotel,
    hotelCode,
    iataCode,
    isHotelInBlacklist,
    lengthOfStay,
    nbAdults,
    nbChildren,
    productCodes,
    promoCode,
    promoCodeIsValid,
    rateIds,
    rooms,
    specialRate
  }
}
