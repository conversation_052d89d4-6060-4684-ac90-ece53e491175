import { ApiManager } from "../services"
import { getGlobalConfig } from "../global/config"

let apiManagerInstance: ApiManager | null = null

export function useApiManager() {
  if (!apiManagerInstance) {
    //build singleton of first access
    const config = getGlobalConfig()
    if (!config) {
      throw new Error(
        "[ApiManager] brandConfig is not available yet. Make sure it's provided before calling useApiManager."
      )
    }

    apiManagerInstance = new ApiManager(config.apiUrl, {
      clientId: config.apiHeaders.clientId
    })
  }

  return {
    apiManager: apiManagerInstance
  }
}
