import { computed, reactive } from "vue"

type ModalType = "currency" | "hotel" | "language" | "logout" | "travel-pro"

const state = reactive<Record<ModalType, boolean>>({
  currency: false,
  hotel: false,
  language: false,
  logout: false,
  "travel-pro": false
})

export function useModal(type: ModalType) {
  const isModalOpen = computed(() => state[type])

  const openModal = () => {
    state[type] = true
  }

  const closeModal = () => {
    state[type] = false
  }

  const toggleModal = () => {
    state[type] = !state[type]
  }

  return { closeModal, isModalOpen, openModal, toggleModal }
}
