import { onMounted, ref } from "vue"
import { PaymentModule } from "@shared/payment"
import { getGlobalConfig } from "../global/config"

const paymentModule = ref<PaymentModule>()

/**
 * @see https://accor-it.atlassian.net/wiki/spaces/PAYM/pages/5649694755/HOW+TO+Payment+Card+Module+User+Guide
 */
export function usePaymentIntegration() {
  const config = getGlobalConfig()
  onMounted(async () => {
    if (paymentModule.value) return

    /* @vite-ignore */
    paymentModule.value = await import(config.app.payModule)
  })

  const iframeMounted = ref(false)

  const mountIframe = (selector: string, transactionId: string) => {
    if (iframeMounted.value || !paymentModule.value) return

    paymentModule.value.initIframe({
      containerSelector: selector,
      params: {
        apiKey: config.app.payApiKey,
        tId: transactionId
      }
    })

    iframeMounted.value = true
  }

  return {
    mountIframe,
    paymentModule
  }
}
