import { DefaultRoomDetails, RoomType, SPECIAL_RATES_MAPPING, SpecialRates } from "design-system"
import { LengthOfStayUnit, PricingCategory } from "../services/offer/offer.enums"
import { LocationQuery, LocationQueryRaw, useRouter } from "vue-router"
import { QueryParameter, extractRoomIndicesFromQuery, isChildAgeKey } from "../helpers/queryParameters"
import { computed, onMounted } from "vue"
import { getGlobalConfig } from "../global/config"
import { useBasket } from "../composables/useBasket"
import { useSearch } from "./useSearch"
import { useUserPOS } from "./useUserPOS"
import { useUserStore } from "../stores/user/user"

/**
 * Warning: should not be used as standalone hook - exported for unit tests
 */
export function useToQueryParameters() {
  const { dateIn, iataCode, lengthOfStay, promoCode, rooms, hotelCode, specialRate } = useSearch()

  const accessibleProducts = computed(() => rooms.value.some((room) => room.accessibility))
  const qpRooms = computed(() => {
    const rawQueryRooms: LocationQueryRaw = {}

    for (let i = 0; i < rooms.value.length; i++) {
      const room = rooms.value[i]

      Object.assign(rawQueryRooms, {
        [QueryParameter.Room[i].Adults]: room.adults
      })

      if (room.productCode) {
        Object.assign(rawQueryRooms, {
          [QueryParameter.Room[i].RoomType]: room.productCode
        })
      }

      if (room.rateId) {
        Object.assign(rawQueryRooms, {
          [QueryParameter.Room[i].RateId]: room.rateId
        })
      }

      if (room.rateCode) {
        Object.assign(rawQueryRooms, {
          [QueryParameter.Room[i].RateCode]: room.rateCode
        })
      }

      if (room.classCode) {
        Object.assign(rawQueryRooms, {
          [QueryParameter.Room[i].ClassCode]: room.classCode
        })
      }

      Object.assign(rawQueryRooms, {
        [QueryParameter.Room[i].Accessibility]: room.accessibility?.toString() || false
      })

      for (let j = 0; j < room.childrenAges.length; j++) {
        Object.assign(rawQueryRooms, {
          [QueryParameter.Room[i].ChildrenAge[j]]: room.childrenAges[j]
        })
      }
    }

    return rawQueryRooms
  })

  return computed<LocationQuery>(() => {
    const year = dateIn.value.getFullYear()
    const month = (dateIn.value.getMonth() + 1).toString().padStart(2, "0")
    const day = dateIn.value.getDate().toString().padStart(2, "0")
    const isoDateIn = `${year}-${month}-${day}`

    const queryParams = {
      [QueryParameter.AccessibleProducts]: accessibleProducts.value.toString(),
      [QueryParameter.AgencyId]: iataCode.value,
      [QueryParameter.DateIn]: isoDateIn,
      [QueryParameter.HotelCodes]: hotelCode.value,
      [QueryParameter.LengthOfStayUnit]: LengthOfStayUnit.NIGHT, // Always "NIGHT" (could be day but isn't suppose to be supported on our side)
      [QueryParameter.LengthOfStayValue]: lengthOfStay.value,
      // Only include preferredRateCodes for PROMO_CODE special rate
      ...(specialRate.value === SpecialRates.PROMO_CODE
        ? { [QueryParameter.PreferredRateCodes]: promoCode.value }
        : {}),
      [QueryParameter.SpecialRate]: specialRate.value,
      ...qpRooms.value
    }

    for (const key in queryParams) {
      const value = queryParams[key as keyof typeof queryParams]
      if (value === undefined || value === null) {
        delete queryParams[key as keyof typeof queryParams]
      }
    }

    return queryParams as unknown as LocationQuery
  })
}

/**
 * Warning: should not be used as standalone hook - exported for unit tests
 */
export function useQueryParametersSynchronizer() {
  const { dates, hotelCode, iataCode, promoCode, rooms, specialRate } = useSearch()

  const synchronize = (query: LocationQuery) => {
    if (query.hotelCodes) {
      hotelCode.value = query.hotelCodes as string
    }

    if (query[QueryParameter.DateIn]) {
      const qpDateIn = new Date(query[QueryParameter.DateIn] as string)

      if (query[QueryParameter.LengthOfStayValue]) {
        const lengthOfStayValue = Number(query[QueryParameter.LengthOfStayValue])

        const qpDateOut = new Date(qpDateIn)
        qpDateOut.setDate(qpDateOut.getDate() + lengthOfStayValue)

        dates.value = [qpDateIn, qpDateOut]
      } else {
        dates.value = [qpDateIn]
      }
    }

    if (
      query[QueryParameter.SpecialRate] &&
      Object.values(SpecialRates).includes(query[QueryParameter.SpecialRate] as SpecialRates)
    ) {
      specialRate.value = query[QueryParameter.SpecialRate] as SpecialRates
    }

    if (query[QueryParameter.PreferredRateCodes]) {
      const preferredRateCode = query[QueryParameter.PreferredRateCodes] as string

      // Check if preferredRateCode matches any special rate codes
      let matchedSpecialRate: SpecialRates | null = null
      for (const [specialRate, codes] of Object.entries(SPECIAL_RATES_MAPPING)) {
        if (codes) {
          const codeArray = Array.isArray(codes) ? codes : [codes]
          if (codeArray.includes(preferredRateCode)) {
            matchedSpecialRate = specialRate as SpecialRates
            break
          }
        }
      }

      if (matchedSpecialRate) {
        // If it's a special rate code, set the special rate and clear promo code
        specialRate.value = matchedSpecialRate
        promoCode.value = ""
      } else {
        // Otherwise treat as promo code
        promoCode.value = preferredRateCode
        specialRate.value = SpecialRates.PROMO_CODE
      }
    }

    if (query[QueryParameter.AgencyId]) {
      iataCode.value = query[QueryParameter.AgencyId] as string
    }

    const roomIndices = extractRoomIndicesFromQuery(query)

    if (roomIndices.length > 0) {
      const parsedRooms: RoomType[] = []

      for (const roomIndex of roomIndices) {
        const adultKey = QueryParameter.Room[roomIndex].Adults

        if (!Number.isSafeInteger(Number(query[adultKey]))) continue

        const room: RoomType = {
          accessibility: false,
          adults: Number(query[adultKey]),
          children: 0,
          childrenAges: [],
          id: roomIndex
        }

        for (const [key, value] of Object.entries(query)) {
          if (!isChildAgeKey(key, roomIndex)) continue

          room.children++
          room.childrenAges.push(Number(value))
        }

        if (query[QueryParameter.Room[roomIndex].RoomType]) {
          room.productCode = query[QueryParameter.Room[roomIndex].RoomType] as string
        }

        if (query[QueryParameter.Room[roomIndex].RateId]) {
          room.rateId = query[QueryParameter.Room[roomIndex].RateId] as string
        }

        if (query[QueryParameter.Room[roomIndex].RateCode]) {
          room.rateCode = query[QueryParameter.Room[roomIndex].RateCode] as string
        }

        if (query[QueryParameter.Room[roomIndex].ClassCode]) {
          room.classCode = query[QueryParameter.Room[roomIndex].ClassCode] as string
        }

        if (query[QueryParameter.Room[roomIndex].Accessibility]) {
          room.accessibility = query[QueryParameter.Room[roomIndex].Accessibility] === "true"
        } else {
          room.accessibility = query[QueryParameter.AccessibleProducts] === "true"
        }

        parsedRooms.push(room)
      }

      rooms.value = parsedRooms
    } else {
      rooms.value = [{ ...DefaultRoomDetails, id: 0 }]
    }
  }

  return { synchronize }
}

export function useSearchQueryParams() {
  const router = useRouter()
  const queryParams = useToQueryParameters()
  const { synchronize } = useQueryParametersSynchronizer()
  const { userPOS } = useUserPOS()
  const { loggedUser } = useUserStore()
  const { selectedOffers } = useBasket()

  const config = getGlobalConfig()

  const enhancePermalink = computed(() => {
    const { client, host, path } = config.step3Permalink

    if (!client || !host || !path) return

    const url = new URL(path, host)

    url.searchParams.set("client", client)
    url.searchParams.set("languageCode", userPOS.locale)
    url.searchParams.set("currencyCode", userPOS.currency)

    for (const rawKey in queryParams.value) {
      const key = rawKey as keyof typeof queryParams.value

      if (key.includes("rateCode") && loggedUser.id) {
        const rateCode = queryParams.value[key] as string

        const offer = selectedOffers.value.find((offer) => offer.rate.id === rateCode)

        const isMemberRate = offer?.pricing.main.categories.includes(PricingCategory.MEMBER_RATE)

        if (isMemberRate) {
          url.searchParams.set(key, `${rateCode}-DR`)
        } else {
          url.searchParams.set(key, rateCode)
        }
      } else {
        url.searchParams.set(key, queryParams.value[key] as string)
      }
    }

    return url
  })

  onMounted(() => {
    synchronize(router.currentRoute.value.query)
  })

  router.beforeEach((to) => {
    synchronize(to.query)
  })

  return { enhancePermalink, queryParams }
}
