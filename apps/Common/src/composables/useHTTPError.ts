import { CustomError, ErrorTypes, useHTTPErrors } from "./useHTTPErrors"
import { computed } from "vue"

export { ErrorTypes }

export const useHTTPError = (name: string) => {
  const { errors } = useHTTPErrors()

  const error = computed({
    get() {
      return errors[name]
    },
    set(value) {
      errors[name] = value
    }
  })

  const generateError = (errorType: ErrorTypes, params: Record<string, unknown> = {}): CustomError => {
    const error = { code: errorType } as CustomError

    if (params) {
      error.params = params
    }

    return error
  }

  return { error, generateError }
}
