import { ErrorTypes, useHTTPError } from "./useHTTPError"
import { Semaphores, useApiSemaphore } from "./useApiSemaphore"
import { SpecialRates } from "design-system"
import { getUserIdentification } from "../services/users/users.fetch"
import { reactive } from "vue"
import { useSearch } from "./useSearch"

export interface Identification {
  affiliation: {
    merchantId: string
    sourceId: string
  } | null
  booking: object | null
  agency: object | null
  application: string | null
  cardNumber: string | null
  cardType: string | null
  clientType: string | null
  company: object | null
  context: string | null
  identificationId: string | null
  identificationToken: string | null
  lcahmember: boolean
  loyaltyCard: boolean
  loyaltyMember: boolean
  offerPreference: {
    offer: {
      code: string
      type: string
    }[]
    regularOfferAllowed: boolean
  } | null
  partnerLoyaltyPrograms: string[]
  preferentialCode: number | null
  referer: string | null
}

const identification = reactive<Identification>({
  affiliation: null,
  agency: null,
  application: null,
  cardNumber: null,
  cardType: null,
  clientType: null,
  company: null,
  context: null,
  identificationId: null,
  identificationToken: null,
  lcahmember: false,
  loyaltyCard: false,
  loyaltyMember: false,
  offerPreference: null,
  partnerLoyaltyPrograms: [],
  preferentialCode: null,
  referer: null
})

export const useIdentificationPlainText = () => {
  const { setSemaphore } = useApiSemaphore()
  const { promoCodeIsValid, specialRate } = useSearch()
  const { error, generateError } = useHTTPError("identification")

  const refreshIdentification = async () => {
    try {
      error.value = false
      setSemaphore(Semaphores.identification, true)
      const newIdentification = await getUserIdentification(identification)
      Object.assign(identification, newIdentification)

      promoCodeIsValid.value = true
    } catch {
      // There's no specific error related to the promo code in the error returned
      // So when an error is caught here, if there was a promo code set,
      // we set the promo code in error. Otherwise, we display the generic error message.
      if (specialRate.value === SpecialRates.PROMO_CODE) {
        promoCodeIsValid.value = false
      } else {
        error.value = generateError(ErrorTypes.GENERIC)
      }
    } finally {
      setSemaphore(Semaphores.identification, false)
    }
  }

  return {
    identification,
    refreshIdentification
  }
}
