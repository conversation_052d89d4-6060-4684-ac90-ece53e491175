import { Breakpoints } from "../global/breakpoints"
import { computed } from "vue"
import { useWindowSize } from "@vueuse/core"

export const useCurrentWindowSize = () => {
  const { width } = useWindowSize()

  const isDesktop = computed(() => {
    return width.value !== Infinity ? width.value >= Breakpoints.md : false
  })

  const isMobile = computed(() => {
    return width.value !== Infinity ? width.value < Breakpoints.sm : false
  })

  return {
    isDesktop,
    isMobile
  }
}
