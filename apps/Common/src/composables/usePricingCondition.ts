import { computed, ref } from "vue"
import { ApiPricingCondition } from "../services/offer/offer.types"
import { cancellationPoliciesMapper } from "../services/offer/offer.mapper"
import { getPricingCondition } from "../services/offer/offer.fetch"
import { useSearch } from "@/composables/useSearch"

const pricingConditionsMap = ref<Map<string, ApiPricingCondition>>(new Map())

export const usePricingCondition = () => {
  const { rooms } = useSearch()

  const fetchMultiplePricingCondition = async (rateIds: string[]) => {
    if (rateIds.length === 0) {
      pricingConditionsMap.value.clear()
      return
    }

    // Only fetch rateIds that we don't already have
    const rateIdsToFetch = rateIds.filter((id) => !pricingConditionsMap.value.has(id))

    if (rateIdsToFetch.length === 0) {
      return
    }

    const pricingConditions = await Promise.all(rateIdsToFetch.map((id) => getPricingCondition(id)))

    rateIdsToFetch.forEach((id, index) => {
      pricingConditionsMap.value.set(id, pricingConditions[index])
    })
  }

  const getPricingConditionById = async (rateCode: string) => {
    let pricingCondition = pricingConditionsMap.value.get(rateCode)

    if (pricingCondition) return pricingCondition

    pricingCondition = await getPricingCondition(rateCode)
    pricingConditionsMap.value.set(rateCode, pricingCondition)

    return pricingCondition
  }

  const cancellationPolicies = computed(() => {
    const selectedRooms = rooms.value.filter((room) => room.rateId)
    if (!selectedRooms.length) return null

    // Get unique rateIds to check if we have pricing conditions
    const uniqueRateIds = [...new Set(selectedRooms.map((room) => room.rateId).filter(Boolean))]
    const availablePricingConditions = uniqueRateIds
      .map((rateId) => pricingConditionsMap.value.get(rateId!))
      .filter(Boolean) as ApiPricingCondition[]

    if (!availablePricingConditions.length) return null

    const hasAllSimplifiedPolicies = availablePricingConditions.every(
      (pricing) => pricing.offer?.pricing?.main?.simplifiedPolicies
    )
    if (!hasAllSimplifiedPolicies) return null

    // Main processing - create policy for each selected room
    const allRoomPolicies = selectedRooms
      .map((room, roomIndex) => {
        if (!room.rateId) return null

        const pricingCondition = pricingConditionsMap.value.get(room.rateId)
        if (!pricingCondition) return null

        const roomPolicies = pricingCondition?.policies
        const roomSimplifiedPolicies = pricingCondition.offer?.pricing?.main?.simplifiedPolicies

        if (!roomPolicies || !roomSimplifiedPolicies) return null

        const roomResult = cancellationPoliciesMapper(roomPolicies, [roomSimplifiedPolicies])
        const roomPolicy = roomResult?.roomsCancellationPolicy?.[0]

        return roomPolicy ? { ...roomPolicy, id: (roomIndex + 1).toString() } : null
      })
      .filter((policy) => policy !== null)

    return allRoomPolicies.length > 0 ? { roomsCancellationPolicy: allRoomPolicies } : null
  })

  return {
    cancellationPolicies,
    fetchMultiplePricingCondition,
    getPricingConditionById,
    pricingConditionsMap
  }
}
