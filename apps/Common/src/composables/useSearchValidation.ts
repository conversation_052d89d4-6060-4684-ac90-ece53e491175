import { computed } from "vue"

interface ValidationState {
  destinationIsValid: boolean
  datePickerIsValid: boolean
  iataCodeError?: string
  promoCodeError?: string
  roomsAndGuestsInError?: boolean
}

export const useSearchValidation = (validationState: ValidationState) => {
  const errorFields = computed(() => {
    const fields: string[] = []

    if (!validationState.destinationIsValid) {
      fields.push("destination")
    }
    if (!validationState.datePickerIsValid) {
      fields.push("datePicker")
    }
    if (validationState.roomsAndGuestsInError) {
      fields.push("roomsAndGuests")
    }
    if (validationState.iataCodeError) {
      fields.push("iataCode")
    }
    if (validationState.promoCodeError) {
      fields.push("promoCode")
    }
    return fields
  })

  const firstError = computed(() => errorFields.value[0])
  const errorFieldsString = computed(() => errorFields.value.join("|"))

  return {
    errorFields,
    errorFieldsString,
    firstError
  }
}
