import { ErrorTypes, useHTTPError } from "./useHTTPError"
import {
  updateBasketSummary as apiUpdateBasketSummary,
  createBasket,
  getBasket,
  updateBasketItems
} from "../services/basket/basket.fetch"
import { Basket } from "../services/basket/basket.types"
import { Offer } from "../services/offer/offer.types"
import { basketMapper } from "../services/basket/basket.mapper"
import { ref } from "vue"
import { useLoader } from "./useLoader"
import { useRouter } from "vue-router"
import { useSearch } from "./useSearch"
import { useSearchQueryParams } from "./useSearchQueryParams"
import { useSessionStorage } from "@vueuse/core"
import { useUserPOS } from "./useUserPOS"

const basket = ref<Basket>()
const selectedOffers = ref<Offer[]>([])

export const useBasket = () => {
  const { userPOS } = useUserPOS()
  const { error, generateError } = useHTTPError("basket")
  const { loading } = useLoader("basket")

  const upsertBasket = async (offerIds: string[]) => {
    const basketId = useSessionStorage("basketId", "")

    try {
      error.value = false
      loading.value = true

      if (basketId.value) {
        await updateBasketItems(basketId.value, offerIds, userPOS.currency, userPOS.countryMarket)
      } else {
        const response = await createBasket(offerIds, userPOS.currency, userPOS.countryMarket)
        basketId.value = response.basket.id
        sessionStorage.setItem("basketId", basketId.value)
      }

      const apiBasket = await getBasket(basketId.value, userPOS.currency, userPOS.countryMarket)
      basket.value = basketMapper(apiBasket.basket)

      basketId.value = basket.value?.id
      return basket.value
    } catch {
      error.value = generateError(ErrorTypes.GENERIC)
    } finally {
      loading.value = false
    }
  }

  const ensureEmptyBasket = () => {
    const { rateIds, rooms } = useSearch()
    const { queryParams } = useSearchQueryParams()
    const router = useRouter()

    if (rateIds.value.length > 0) {
      basket.value = undefined
      sessionStorage.removeItem("basketId")
      rooms.value = rooms.value.map((room) => ({
        ...room,
        offerId: undefined,
        productCode: undefined,
        rateCode: undefined,
        rateId: undefined
      }))

      router.replace({
        name: "stay",
        query: {
          ...queryParams.value
        }
      })
    }
  }

  const updateBasketSummary = async (basketSummary: unknown) => {
    if (!basket.value?.id) return

    const apiBasket = await apiUpdateBasketSummary(
      basket.value.id,
      basketSummary,
      userPOS.currency,
      userPOS.countryMarket
    )
    basket.value = basketMapper(apiBasket.basket)
    sessionStorage.setItem("basketId", basket.value.id)

    return basket.value
  }

  return { basket, ensureEmptyBasket, selectedOffers, updateBasketSummary, upsertBasket }
}
