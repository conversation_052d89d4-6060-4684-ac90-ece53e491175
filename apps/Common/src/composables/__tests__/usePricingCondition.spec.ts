import {
  AggregationPricingType,
  CancellationPolicy,
  CodePolicy,
  LengthOfStayUnit,
  MealPlan,
  OfferType,
  PolicyGuarantee,
  TaxType
} from "../../services/offer/offer.enums"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { ApiPricingCondition } from "../../services/offer/offer.types"
import { RoomType } from "design-system"
import { getPricingCondition } from "../../services/offer/offer.fetch"
import { nextTick } from "vue"
import { usePricingCondition } from "../usePricingCondition"
import { useSearch } from "@/composables/useSearch"

// Mock dependencies
vi.mock("../../services/offer/offer.fetch")

const mockGetPricingCondition = vi.mocked(getPricingCondition)

// Test data
const mockPricingCondition: ApiPricingCondition = {
  offer: {
    categories: [],
    compliances: [],
    dateIn: "2024-01-01",
    id: "offer-1",
    lengthOfStay: { unit: LengthOfStayUnit.NIGHT, value: 2 },
    matches: [],
    mealPlan: { code: MealPlan.EUROPEAN_PLAN },
    occupancy: { adults: 2, babies: 0, childrenAges: [], childrenAgesAsAdult: [] },
    pricing: {
      aggregationType: AggregationPricingType.TOTAL_STAY,
      alternative: {
        amount: 200,
        average: { amount: 100, formattedAmount: "€100" },
        bookable: true,
        categories: [],
        formattedAmount: "€200",
        id: "alt-1",
        label: "Alternative rate",
        reference: { amount: 220, formattedAmount: "€220" }
      },
      currency: "EUR",
      deduction: [],
      formattedAggregationType: "Total Stay",
      formattedTaxType: "All taxes included",
      main: {
        amount: 180,
        average: { amount: 90, formattedAmount: "€90" },
        categories: [],
        feesTotalAmount: {
          excluded: { breakdown: ["Resort fee", "€30"], label: "Excluded fees" },
          included: { breakdown: ["Tax", "€20"], label: "Included fees" }
        },
        formattedAmount: "€180",
        label: "Main rate",
        reference: { amount: 200, categories: [], formattedAmount: "€200" },
        simplifiedPolicies: {
          cancellation: {
            code: CancellationPolicy.FREE_CANCELLATION,
            label: "Free cancellation until 6 PM"
          },
          guarantee: {
            code: PolicyGuarantee.NO_PREPAY,
            label: "No prepayment required"
          }
        }
      },
      taxType: TaxType.ALL_TAXES_AND_FEES_INCLUDED
    },
    product: {
      accessible: false,
      bedding: { details: [], extra: {} },
      classification: {
        standard: { code: "ROOM", label: "Room" },
        type: { code: "STANDARD", label: "Standard" }
      },
      id: "product-1",
      keyFeatures: [],
      label: "Deluxe Room",
      quantity: 1
    },
    rate: {
      description: "Best available rate",
      id: "rate-1",
      label: "Best Rate"
    },
    type: OfferType.ROOM
  },
  perUnit: [],
  policies: [
    {
      code: CodePolicy.CANPOL,
      description: "You can cancel your reservation free of charge until 6 PM on the day of arrival.",
      label: "Cancellation Policy"
    }
  ],
  taxesAndFees: { excluded: [], included: [] }
}

const mockRoom: RoomType = {
  accessibility: false,
  adults: 2,
  children: 0,
  childrenAges: [],
  id: 0,
  productCode: "PROD1",
  rateCode: "RATE1",
  rateId: "rate-123"
}

describe("usePricingCondition", () => {
  beforeEach(() => {
    // Reset all mocks and clear state before each test
    vi.resetAllMocks()

    // Set up default search state
    const { rooms } = useSearch()
    rooms.value = []
  })

  describe("fetchMultiplePricingCondition", () => {
    it("should clear pricing conditions map when no rate IDs provided", async () => {
      const { fetchMultiplePricingCondition, pricingConditionsMap } = usePricingCondition()

      // Add some initial data
      pricingConditionsMap.value.set("rate-1", mockPricingCondition)
      expect(pricingConditionsMap.value.size).toBe(1)

      await fetchMultiplePricingCondition([])

      expect(pricingConditionsMap.value.size).toBe(0)
      expect(mockGetPricingCondition).not.toHaveBeenCalled()
    })

    it("should fetch and store pricing conditions for new rate IDs", async () => {
      mockGetPricingCondition.mockResolvedValue(mockPricingCondition)

      const { fetchMultiplePricingCondition, pricingConditionsMap } = usePricingCondition()

      // Ensure map is empty before test
      pricingConditionsMap.value.clear()

      await fetchMultiplePricingCondition(["rate-123", "rate-456"])

      expect(mockGetPricingCondition).toHaveBeenCalledTimes(2)
      expect(mockGetPricingCondition).toHaveBeenCalledWith("rate-123")
      expect(mockGetPricingCondition).toHaveBeenCalledWith("rate-456")
      expect(pricingConditionsMap.value.size).toBe(2)
      expect(pricingConditionsMap.value.get("rate-123")).toStrictEqual(mockPricingCondition)
      expect(pricingConditionsMap.value.get("rate-456")).toStrictEqual(mockPricingCondition)
    })

    it("should only fetch missing rate IDs when some are already cached", async () => {
      mockGetPricingCondition.mockResolvedValue(mockPricingCondition)

      const { fetchMultiplePricingCondition, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then pre-populate with one rate
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)

      await fetchMultiplePricingCondition(["rate-123", "rate-456"])

      expect(mockGetPricingCondition).toHaveBeenCalledTimes(1)
      expect(mockGetPricingCondition).toHaveBeenCalledWith("rate-456")
      expect(pricingConditionsMap.value.size).toBe(2)
    })

    it("should not fetch when all rate IDs are already cached", async () => {
      const { fetchMultiplePricingCondition, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then pre-populate with both rates
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)
      pricingConditionsMap.value.set("rate-456", mockPricingCondition)

      await fetchMultiplePricingCondition(["rate-123", "rate-456"])

      expect(mockGetPricingCondition).not.toHaveBeenCalled()
      expect(pricingConditionsMap.value.size).toBe(2)
    })
  })

  describe("getPricingConditionById", () => {
    it("should return cached pricing condition if available", async () => {
      const { getPricingConditionById, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the pricing condition to cache
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)

      const result = await getPricingConditionById("rate-123")

      expect(result).toStrictEqual(mockPricingCondition)
      expect(mockGetPricingCondition).not.toHaveBeenCalled()
    })

    it("should fetch and cache pricing condition if not available", async () => {
      mockGetPricingCondition.mockResolvedValue(mockPricingCondition)

      const { getPricingConditionById, pricingConditionsMap } = usePricingCondition()

      // Ensure map is empty before test
      pricingConditionsMap.value.clear()

      const result = await getPricingConditionById("rate-123")

      expect(mockGetPricingCondition).toHaveBeenCalledWith("rate-123")
      expect(result).toStrictEqual(mockPricingCondition)
      expect(pricingConditionsMap.value.get("rate-123")).toStrictEqual(mockPricingCondition)
    })
  })

  describe("cancellationPolicies", () => {
    it("should return null when no rooms are selected", () => {
      const { rooms } = useSearch()
      rooms.value = [{ ...mockRoom, rateId: undefined }]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Ensure map is empty before test
      pricingConditionsMap.value.clear()

      expect(cancellationPolicies.value).toBeNull()
    })

    it("should return null when no pricing conditions are available", () => {
      const { rooms } = useSearch()
      rooms.value = [mockRoom]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Ensure map is empty before test (no pricing conditions available)
      pricingConditionsMap.value.clear()

      expect(cancellationPolicies.value).toBeNull()
    })

    it("should return null when pricing condition has no simplified policies", async () => {
      const pricingConditionWithoutPolicies: ApiPricingCondition = structuredClone(mockPricingCondition)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      pricingConditionWithoutPolicies.offer.pricing.main.simplifiedPolicies = undefined as any

      const { rooms } = useSearch()
      rooms.value = [mockRoom]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the specific pricing condition
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", pricingConditionWithoutPolicies)

      await nextTick()

      expect(cancellationPolicies.value).toBeNull()
    })

    it("should return null when no CANPOL policy is found", async () => {
      const pricingConditionWithoutCanpol: ApiPricingCondition = structuredClone(mockPricingCondition)
      pricingConditionWithoutCanpol.policies = [
        {
          code: "OTHERPOL",
          description: "Some other policy description",
          label: "Other Policy"
        }
      ]

      const { rooms } = useSearch()
      rooms.value = [mockRoom]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the specific pricing condition
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", pricingConditionWithoutCanpol)

      await nextTick()

      expect(cancellationPolicies.value).toBeNull()
    })

    it("should return cancellation policies for selected rooms", async () => {
      const { rooms } = useSearch()
      rooms.value = [mockRoom]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the pricing condition
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)

      await nextTick()

      const result = cancellationPolicies.value
      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(1)

      const policy = result?.roomsCancellationPolicy[0]
      expect(policy?.id).toBe("1")
      expect(policy?.description).toBe(
        "You can cancel your reservation free of charge until 6 PM on the day of arrival."
      )
      expect(policy?.cancellationPolicy.label).toBe("Free cancellation until 6 PM")
    })

    it("should handle multiple rooms with different rates", async () => {
      const room2: RoomType = { ...mockRoom, id: 1, rateId: "rate-456" }
      const pricingCondition2: ApiPricingCondition = structuredClone(mockPricingCondition)
      pricingCondition2.offer.pricing.main.simplifiedPolicies = {
        cancellation: {
          code: CancellationPolicy.NO_CANCELLATION,
          label: "Non-refundable"
        },
        guarantee: {
          code: PolicyGuarantee.NO_PREPAY,
          label: "No prepayment required"
        }
      }
      pricingCondition2.policies = [
        {
          code: CodePolicy.CANPOL,
          description: "This rate is non-refundable.",
          label: "Cancellation Policy"
        }
      ]

      const { rooms } = useSearch()
      rooms.value = [mockRoom, room2]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add both pricing conditions
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)
      pricingConditionsMap.value.set("rate-456", pricingCondition2)

      await nextTick()

      const result = cancellationPolicies.value
      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(2)

      const policy1 = result?.roomsCancellationPolicy[0]
      expect(policy1?.id).toBe("1")
      expect(policy1?.description).toBe(
        "You can cancel your reservation free of charge until 6 PM on the day of arrival."
      )

      const policy2 = result?.roomsCancellationPolicy[1]
      expect(policy2?.id).toBe("2")
      expect(policy2?.description).toBe("This rate is non-refundable.")
    })

    it("should handle rooms with duplicate rate IDs", async () => {
      const room2: RoomType = { ...mockRoom, id: 1, rateId: "rate-123" } // Same rate ID

      const { rooms } = useSearch()
      rooms.value = [mockRoom, room2]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the pricing condition
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", mockPricingCondition)

      await nextTick()

      const result = cancellationPolicies.value
      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(2)

      // Both rooms should have the same policy but different IDs
      expect(result?.roomsCancellationPolicy[0]?.id).toBe("1")
      expect(result?.roomsCancellationPolicy[1]?.id).toBe("2")
      expect(result?.roomsCancellationPolicy[0]?.description).toBe(result?.roomsCancellationPolicy[1]?.description)
    })

    it("should handle error cases gracefully", async () => {
      const malformedPricingCondition: ApiPricingCondition = structuredClone(mockPricingCondition)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      malformedPricingCondition.policies = null as any

      const { rooms } = useSearch()
      rooms.value = [mockRoom]

      const { cancellationPolicies, pricingConditionsMap } = usePricingCondition()

      // Start with empty map, then add the malformed pricing condition
      pricingConditionsMap.value.clear()
      pricingConditionsMap.value.set("rate-123", malformedPricingCondition)

      await nextTick()

      expect(cancellationPolicies.value).toBeNull()
    })
  })
})
