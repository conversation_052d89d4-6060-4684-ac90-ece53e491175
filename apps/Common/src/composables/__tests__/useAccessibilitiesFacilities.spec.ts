import { describe, expect, it, vi } from "vitest"
import { ApiHotel } from "../../services/hotels/hotels.types"
import { HotelAccessibilities } from "../../services/facilities/facilities.types"
import { useAccessibilitiesFacilities } from "../useAccessibilitiesFacilities"

const mocks = vi.hoisted(() => ({
  accessibilities: [
    {
      category: {
        // ⚠ cannot use enum as imports are made **after** hoisted
        code: "AVAILABLE_AREA",
        label: "access category label"
      },
      facilities: [
        {
          code: "AVAILABLE_SERVICES",
          label: "facility service label"
        }
      ]
    }
  ] as HotelAccessibilities[]
}))

vi.mock("../../services/facilities/facilities.fetch", () => ({
  fetchGeneralInformationFacilities: async () => ({
    generalInformation: {
      hotel: {
        accessibilities: mocks.accessibilities
      }
    }
  })
}))
vi.mock("../../services/hotels/hotels.fetch", () => ({
  fetchHotel: async (): Promise<Partial<ApiHotel>> => ({
    contact: {
      mail: "<EMAIL>",
      phone: "234567890",
      phonePrefix: "1"
    }
  })
}))

describe("accessibilitiesFacilities", () => {
  describe("loadAccessibilities", () => {
    it("should load data from the api", async () => {
      const { accessibilities, loadAccessibilities } = useAccessibilitiesFacilities()

      expect(accessibilities.value).toStrictEqual({})

      await loadAccessibilities("myHotelId")

      expect(accessibilities.value).toStrictEqual({
        accessibilities: mocks.accessibilities,
        phoneNumber: "234567890",
        phonePrefix: "1"
      })
    })
  })
})
