// import { createRouter, createWebHistory } from "vue-router"
import { describe, vi } from "vitest"
import { Currency } from "@/global/enums"
// import StayView from "../../views/StayView.vue"
import { TotalAmountMode } from "../../services/basket/basket.enums"
import { getBasket } from "../../services/basket/basket.fetch"
// import { shallowMount } from "@vue/test-utils"
// import { useBasket } from "../useBasket"
// import { useSearch } from "../useSearch"

vi.mock("../../services/basket/basket.fetch")
vi.mock("../useUserPOS", () => ({
  useUserPOS: () => ({
    userPOS: {
      countryMarket: "FR",
      currency: "EUR",
      fullLocale: "",
      locale: "fr"
    }
  })
}))

// vi.mock(import("vue-router"), async (importOriginal) => {
//   const actual = await importOriginal()
//   return {
//     ...actual,
//     useRouter: () => ({
//       currentRoute: { value: { query: {} } },
//       beforeEach: vi.fn(),
//       beforeResolve: vi.fn(),
//       afterEach: vi.fn(),
//       push: vi.fn(),
//       replace: vi.fn()
//     })
//   }
// })

// const routes = [
//   { component: { template: "<div>Home Page</div>" }, path },
//   { path: "/search", component: { template: "<div>Search Page</div>" } },
//   { path: "/enhance", component: { template: "<div>Enhance Page</div>" } },
//   { path: "/complete", component: { template: "<div>Complete Page</div>" } },
//   { path: "/summary", component: { template: "<div>Summary Page</div>" } },
//   { path: "/about", component: { template: "<div>About Page</div>" } }
// ]

// const router = createRouter({
//   history: createWebHistory(),
//   routes
// })

// const useBasketSpy = vi.fn(() => useBasket())

describe("useBasket", () => {
  vi.mocked(getBasket).mockImplementation(async () => ({
    basket: {
      currency: Currency.EUR,
      currencySymbol: "€",
      hotel: {
        brand: {
          code: "brand code",
          label: "brand label"
        },
        checkInHour: "check-in hour",
        checkOutHour: "check-out hour",
        formattedCheckInHour: "formatted check-in hour",
        formattedCheckOutHour: "formatted check-out hour",
        id: "get hotel id",
        localRating: 5,
        media: {
          category: "get hotel media category",
          formats: [],
          type: "get hotel media type"
        },
        name: "get hotel name"
      },
      hotelCurrency: Currency.EUR,
      hotelCurrencySymbol: "€",
      id: "get hotel id",
      itemTotalAmountMode: TotalAmountMode.AFTER_TAXES,
      items: [],
      pricing: {
        formattedTotalAmount: "formatted total amount",
        formattedTotalAmountHotelCurrency: "formatted total amount hotel currency",
        totalAmount: 42,
        totalAmountHotelCurrency: 12
      },
      request: {} as any, // eslint-disable-line @typescript-eslint/no-explicit-any,
      simplifiedCanpol: {
        code: 1,
        label: "simplified canpol label"
      }
    },
    modificationReport: [],
    operationWarnings: []
  }))

  // afterAll(() => {
  //   vi.resetAllMocks()
  // })

  // beforeEach(() => {
  //   delete router.currentRoute.value.query.basketId

  //   const wrapper = mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })
  //   wrapper.vm.basket = undefined as any // eslint-disable-line @typescript-eslint/no-explicit-any

  //   vi.clearAllMocks()
  // })

  // it("mount stayView", () => {
  //   const mockBrandConfig = {
  //     brandCode: "luxury-brand",
  //     logo: "logo-url"
  //   }

  //   const wrapper = shallowMount(StayView, {
  //     global: {
  //       plugins: [router],
  //       provide: {
  //         // abstractionLayer: mockAbstractionLayer,
  //         brandConfig: mockBrandConfig
  //       }
  //     }
  //   })

  //   // eslint-disable-next-line no-console
  //   console.log(wrapper.vm)
  // })

  // it("should ensure that all basket informations are cleaned at the invocation of the method", () => {
  //   const { ensureEmptyBasket, basket } = useBasketSpy()

  //   const { rooms, rateIds } = useSearch()
  //   rooms.value = [
  //     {
  //       accessibility: false,
  //       adults: 2,
  //       children: 1,
  //       childrenAges: [10],
  //       id: 0,
  //       productCode: "PROD1",
  //       rateCode: "RATE1",
  //       rateId: "rate-123"
  //     }
  //   ]

  //   expect(rateIds.value).toEqual(["rate-123"])

  //   const ensureEmptyBasketSpy = vi.fn(() => ensureEmptyBasket())
  //   ensureEmptyBasketSpy()

  //   expect(rateIds.value).toEqual([])
  //   expect(basket.value).toBeUndefined()

  //   /**
  //    * basket.value = undefined
  //     sessionStorage.removeItem("basketId")
  //     rooms.value = rooms.value.map((room) => ({
  //       ...room,
  //       offerId: undefined,
  //       productCode: undefined,
  //       rateCode: undefined,
  //       rateId: undefined
  //     }))
  //    */
  // })
  // it("should do nothing if no basket id is set in query string", () => {
  //   mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })

  //   expect(getBasket).toHaveBeenCalledTimes(0)
  // })

  // it("should do nothing if the basket ids match and not forced to refresh", async () => {
  //   const wrapper = mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })
  //   expect(getBasket).toHaveBeenCalledTimes(0)

  //   wrapper.vm.basket = structuredClone(basketMock)
  //   router.currentRoute.value.query.basketId = "basketId"

  //   mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })
  //   expect(useBasketSpy).toBeCalledTimes(2)
  //   expect(getBasket).toHaveBeenCalledTimes(0)
  // })

  // it("should fetch the basket if not loaded yet", async () => {
  //   router.currentRoute.value.query.basketId = "basketId"
  //   mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })
  //   expect(getBasket).toHaveBeenCalledTimes(1)
  // })

  // it("should fetch the basket if ids match but forced", async () => {
  //   const wrapper = mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     }
  //   })
  //   expect(getBasket).toHaveBeenCalledTimes(0)

  //   wrapper.vm.basket = structuredClone(basketMock)
  //   router.currentRoute.value.query.basketId = "basketId"

  //   mount(TestComponent, {
  //     global: {
  //       plugins: [router]
  //     },
  //     props: {
  //       forceRefresh: true
  //     }
  //   })
  //   expect(useBasketSpy).toBeCalledTimes(2)
  //   expect(getBasket).toHaveBeenCalledTimes(1)
  // })
})
