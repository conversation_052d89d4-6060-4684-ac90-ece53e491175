import { BestOffersError, useOffersAndAccommodations } from "../useOffersAndAccommodations"
import { ErrorTypes, useHTTPError } from "../useHTTPError"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { AxiosError } from "axios"
import { SpecialRates } from "design-system"
import { useSearch } from "../useSearch"

// Mock the composables
vi.mock("../useUserPOS")
vi.mock("../stores/user/user")

// Mock refreshIdentification
const refreshIdentification = vi.fn()
vi.mock("../useIdentificationPlainText", () => ({
  useIdentificationPlainText: () => ({
    refreshIdentification
  })
}))

describe("useOffersAndAccommodations - handleError", () => {
  const { error } = useHTTPError("offers")
  let composable: ReturnType<typeof useOffersAndAccommodations>

  beforeEach(() => {
    vi.clearAllMocks()
    error.value = false

    const { dates, hotel, iataCode, promoCode, rooms, specialRate } = useSearch()
    const today = new Date()
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    dates.value = [today, tomorrow]
    hotel.value = undefined
    iataCode.value = undefined
    promoCode.value = undefined
    rooms.value = [
      {
        accessibility: false,
        adults: 1,
        children: 0,
        childrenAges: [],
        id: 0
      }
    ]
    specialRate.value = SpecialRates.NONE

    composable = useOffersAndAccommodations()
  })

  function makeAxiosError({
    code,
    status = 400,
    fieldErrors = []
  }: {
    code?: string
    status?: number
    fieldErrors?: {
      code: string
      input: string
      detail: string
    }[]
  }) {
    return {
      response: {
        data: { code, fieldErrors },
        status
      }
    } as unknown as AxiosError<BestOffersError>
  }

  it("should set error to GENERIC for missing code/fieldErrors/status", async () => {
    await composable.handleError({} as AxiosError<BestOffersError>)
    expect(error.value).toMatchObject({ code: ErrorTypes.GENERIC })
  })

  it("should set error to GENERIC for unknown status", async () => {
    await composable.handleError(makeAxiosError({ code: "UNKNOWN", status: 418 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.GENERIC })
  })

  // it("should call refreshIdentification and refetch for IDENT_TOKEN_INVALID (401)", async () => {
  //   const fetchAllOffersSpy = vi.spyOn(composable, "fetchOffers")
  //   await composable.handleError(makeAxiosError({ code: "IDENT_TOKEN_INVALID", status: 401 }))

  //   expect(refreshIdentification).toBeCalled()
  //   expect(fetchAllOffersSpy).toBeCalled()
  // })

  it("should set error to GENERIC for 401 with unknown code", async () => {
    await composable.handleError(makeAxiosError({ code: "SOMETHING_ELSE", status: 401 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.GENERIC })
  })

  // TODO: fix this test
  it("should reset dates and refetch for INVALID_INPUT dateIn", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "dateIn" }]

    const { dates } = useSearch()

    dates.value[0] = new Date("2025-04-16")
    dates.value[1] = new Date("2025-04-17")

    // const fetchAllOffersSpy = vi.spyOn(composable, "fetchOffers").mockResolvedValue(undefined)
    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))

    const today = new Date()
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    expect(dates.value[0].toDateString()).toContain(today.toDateString())
    expect(dates.value[1].toDateString()).toContain(tomorrow.toDateString())
    // expect(fetchAllOffersSpy).toHaveBeenCalled()
  })

  it("should set error to BEST_OFFERS_MAX_ADULTS_EXCEEDED for INVALID_INPUT maxAdult", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "adults" }]
    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_MAX_ADULTS_EXCEEDED })
  })

  it("should set error to BEST_OFFERS_MAX_PAX_EXCEEDED for INVALID_INPUT maxPax", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "pax" }]

    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_MAX_PAX_EXCEEDED })
  })

  it("should set error to BEST_OFFERS_MAX_CHILD_EXCEEDED for INVALID_INPUT child", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "childrenAges" }]

    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_MAX_CHILD_EXCEEDED })
  })

  it("should set error to BEST_OFFERS_MAX_ROOM_EXCEEDED for INVALID_INPUT maxRoom", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "room" }]

    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_MAX_ROOM_EXCEEDED })
  })

  it("should set error to BEST_OFFERS_INPUT_PROBLEM for INVALID_INPUT with other field", async () => {
    const fieldErrors = [{ code: "INVALID_INPUT", detail: "", input: "other" }]

    await composable.handleError(makeAxiosError({ code: "INVALID_INPUT", fieldErrors, status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_INPUT_PROBLEM })
  })

  it("should set error to BEST_OFFERS_INPUT_PROBLEM for USER_HAS_NOT_ENOUGH_STAY_PLUS", async () => {
    await composable.handleError(makeAxiosError({ code: "USER_HAS_NOT_ENOUGH_STAY_PLUS", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_INPUT_PROBLEM })
  })

  it("should set error to BEST_OFFERS_INPUT_PROBLEM for USER_HAS_NOT_ENOUGH_SNUS", async () => {
    await composable.handleError(makeAxiosError({ code: "USER_HAS_NOT_ENOUGH_SNUS", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.BEST_OFFERS_INPUT_PROBLEM })
  })

  it("should set error to IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID", async () => {
    await composable.handleError(makeAxiosError({ code: "IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID })
  })

  it("should set error to INVALID_HOTEL_ID", async () => {
    await composable.handleError(makeAxiosError({ code: "INVALID_HOTEL_ID", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.INVALID_HOTEL_ID })
  })

  it("should set error to GENERIC for OOPS", async () => {
    await composable.handleError(makeAxiosError({ code: "OOPS", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.GENERIC })
  })

  it("should set error to GENERIC for unknown code in 400", async () => {
    await composable.handleError(makeAxiosError({ code: "UNKNOWN", status: 400 }))
    expect(error.value).toMatchObject({ code: ErrorTypes.GENERIC })
  })
})
