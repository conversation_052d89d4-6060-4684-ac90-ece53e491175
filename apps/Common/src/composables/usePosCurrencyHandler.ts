import { computed, ref, watch } from "vue"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { usePosStore } from "@stores/pos/pos"
import { useUserPOS } from "./useUserPOS"

const currenciesGeographicalArea = ref("")
const localCurrencyCode = ref("")

export function usePosCurrencyHandler() {
  const { userPOS } = useUserPOS()
  const { get: getCookies } = useCookies()
  const { pos, findContinentCodeByCurrencyCode } = usePosStore()

  const currenciesGeographicalAreaOptions = computed(() => {
    const options =
      pos.posCurrenciesGroupedByContinent?.map((continent) => ({
        label: continent.continentLabel,
        value: continent.continentCode
      })) || []

    return options
  })

  const posCurrenciesGroupedByContinent = computed(() => pos.posCurrenciesGroupedByContinent)

  const currencyCodeOptions = computed(() => {
    const continent = posCurrenciesGroupedByContinent.value?.find(
      (group) => group.continentCode === currenciesGeographicalArea.value
    )

    const options =
      continent?.currencyList.map((currency) => ({
        currency,
        currencyCode: currency.currencyCode,
        label: `${currency.currencyCode} - ${currency.currencyLabel}`,
        value: currency.currencyCode
      })) || []

    if (!localCurrencyCode.value || !options.some((option) => option.value === localCurrencyCode.value)) {
      localCurrencyCode.value = options[0]?.value || ""
    }

    return options
  })

  const currentCurrencyLabel = computed(() => {
    const currentOption = currencyCodeOptions.value.find((option) => option.value === userPOS.currency)

    if (!currentOption) return userPOS.currency

    const { currencyCode, currencySymbol } = currentOption.currency

    if (currencySymbol) {
      return `${currencyCode} (${currencySymbol})`
    }

    return currencyCode
  })

  const resetStateCurrency = async () => {
    const geographicalArea = await findContinentCodeByCurrencyCode(
      getCookies("userCurrency"),
      getCookies("userLocalization")
    )

    localCurrencyCode.value = userPOS.currency
    currenciesGeographicalArea.value = geographicalArea
  }

  watch(userPOS, async () => {
    await resetStateCurrency()
  })

  return {
    currenciesGeographicalArea,
    currenciesGeographicalAreaOptions,
    currencyCode: localCurrencyCode,
    currencyCodeOptions,
    currentCurrencyLabel,
    resetStateCurrency
  }
}
