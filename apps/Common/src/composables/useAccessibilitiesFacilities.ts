import { UiAccessibilityModalContentCategoryType } from "design-system"
import { fetchGeneralInformationFacilities } from "../services/facilities/facilities.fetch"
import { fetchHotel } from "../services/hotels/hotels.fetch"
import { ref } from "vue"

export function useAccessibilitiesFacilities() {
  const isLoading = ref(false)
  const accessibilities = ref<{
    accessibilities?: UiAccessibilityModalContentCategoryType[]
    phoneNumber?: string
    phonePrefix?: string
  }>({})

  async function loadAccessibilities(hotelId: string) {
    isLoading.value = true
    try {
      const [facilitiesResponse, hotelResponse] = await Promise.all([
        fetchGeneralInformationFacilities(hotelId),
        fetchHotel(hotelId)
      ])

      accessibilities.value = {
        accessibilities: facilitiesResponse.generalInformation.hotel.accessibilities,
        phoneNumber: hotelResponse.contact.phone,
        phonePrefix: hotelResponse.contact.phonePrefix
      }
    } finally {
      isLoading.value = false
    }
  }

  return {
    accessibilities,
    isLoading,
    loadAccessibilities
  }
}
