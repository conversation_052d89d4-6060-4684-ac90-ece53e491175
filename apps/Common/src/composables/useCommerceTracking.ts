import { ErrorTypes, useHTTPError } from "./useHTTPError"
import { Semaphores, useApiSemaphore } from "./useApiSemaphore"
import { ApiCommerceTracking } from "../services/tracking/tracking.types"
import { getCommerceTracking } from "../services/tracking/tracking.fetch"
import { ref } from "vue"

const commerceTracking = ref<ApiCommerceTracking>()

export const useCommerceTracking = () => {
  const { error, generateError } = useHTTPError("commerceTracking")
  const { setSemaphore } = useApiSemaphore()

  const fetchCommerceTracking = async (appDomain: string) => {
    commerceTracking.value = undefined

    try {
      error.value = false
      setSemaphore(Semaphores.cookieAttribution, true)
      const apiCommerceTracking = await getCommerceTracking(appDomain)

      commerceTracking.value = apiCommerceTracking
    } catch {
      error.value = generateError(ErrorTypes.GENERIC)
    } finally {
      setSemaphore(Semaphores.cookieAttribution, false)
    }
  }

  return {
    commerceTracking,
    fetchCommerceTracking
  }
}
