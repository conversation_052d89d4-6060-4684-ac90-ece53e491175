import { computed, ref } from "vue"
import { RouteLocationRaw } from "vue-router"
import { i18n } from "../i18n"
import router from "../router"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { usePosStore } from "@stores/pos/pos"

const countriesGeographicalArea = ref("")
const countryRegionLabel = ref("")

export function usePosLanguageHandler() {
  const { get: getCookies } = useCookies()
  const { pos, findContinentCodeByCountry, findCountryLabelByCountryCodeAndLocale } = usePosStore()

  const countriesGeographicalAreaOptions = computed(() => {
    const options =
      pos.posCountriesGroupedByContinent?.map((continent) => ({
        label: continent.continentLabel,
        value: continent.continentCode
      })) || []

    if (!countriesGeographicalArea.value) {
      countriesGeographicalArea.value = options[0]?.value || ""
    }

    return options
  })

  const countryRegionOptions = computed(() => {
    const continent = pos.posCountriesGroupedByContinent?.find(
      (group) => group.continentCode === countriesGeographicalArea.value
    )

    const options =
      continent?.countryList.map((country) => ({
        label: country.accorCountryLanguageLabel,
        languageCode: country.languageCode,
        value: country.accorCountryLanguageLabel
      })) || []

    if (!countryRegionLabel.value || !options.some((option) => option.value === countryRegionLabel.value)) {
      countryRegionLabel.value = options[0]?.value || ""
    }

    return options
  })

  const handleLanguageChange = async () => {
    const langToChange = countryRegionOptions.value.find(
      (option) => option.value === countryRegionLabel.value
    )?.languageCode

    if (
      !langToChange ||
      !(i18n.global.availableLocales as string[]).includes(langToChange) ||
      langToChange === i18n.global.locale.value
    )
      return

    window.location.href = router.resolve({
      ...router.currentRoute.value,
      params: {
        locale: langToChange
      }
    } as RouteLocationRaw).href
  }

  const resetState = () => {
    countriesGeographicalArea.value = findContinentCodeByCountry(getCookies("userLocalization"))
    countryRegionLabel.value = findCountryLabelByCountryCodeAndLocale(
      getCookies("userLocalization"),
      getCookies("userLang")
    )
  }

  return {
    countriesGeographicalArea,
    countriesGeographicalAreaOptions,
    countryRegionLabel,
    countryRegionOptions,
    handleLanguageChange,
    resetState
  }
}
