import { reactive } from "vue"
import router from "../router"

export enum ErrorTypes {
  GENERIC = 1,
  BEST_OFFERS_MAX_ADULTS_EXCEEDED = 2,
  BEST_OFFERS_MAX_CHILD_EXCEEDED = 3,
  BEST_OFFERS_MAX_PAX_EXCEEDED = 4,
  BEST_OFFERS_MAX_ROOM_EXCEEDED = 5,
  BEST_OFFERS_INPUT_PROBLEM = 6,
  BEST_OFFERS_SELECTED_OFFER_UNAVAILABLE = 7,
  INVALID_HOTEL_ID = 8,
  IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID = 9
}

export type CustomError = {
  code: ErrorTypes
  params: Record<string, unknown>
}

const errors = reactive<Record<string, false | CustomError>>({})

export const useHTTPErrors = () => {
  router.beforeResolve(() => {
    Object.keys(errors).forEach((key) => {
      errors[key] = false
    })
  })

  return { errors }
}

export const errorMapping: Record<
  ErrorTypes,
  { message: string; description: string; links?: { text: string; to: string; target?: string }[] }
> = {
  [ErrorTypes.GENERIC]: {
    description: "errors.generic.description",
    links: [
      {
        text: "errors.generic.cta",
        to: "homepage"
      }
    ],
    message: "errors.generic.title"
  },
  [ErrorTypes.BEST_OFFERS_MAX_ADULTS_EXCEEDED]: {
    description: "errors.best_offers.max_adults_exceeded.description",
    message: "errors.best_offers.max_adults_exceeded.title"
  },
  [ErrorTypes.BEST_OFFERS_MAX_CHILD_EXCEEDED]: {
    description: "errors.best_offers.max_child_exceeded.description",
    message: "errors.best_offers.max_child_exceeded.title"
  },
  [ErrorTypes.BEST_OFFERS_MAX_PAX_EXCEEDED]: {
    description: "errors.best_offers.max_pax_exceeded.description",
    message: "errors.best_offers.max_pax_exceeded.title"
  },
  [ErrorTypes.BEST_OFFERS_MAX_ROOM_EXCEEDED]: {
    description: "errors.best_offers.max_room_exceeded.description",
    message: "errors.best_offers.max_room_exceeded.title"
  },
  [ErrorTypes.BEST_OFFERS_INPUT_PROBLEM]: {
    description: "errors.best_offers.input_problem.description",
    links: [
      {
        text: "errors.best_offers.input_problem.cta",
        to: "homepage"
      }
    ],
    message: "errors.best_offers.input_problem.title"
  },
  [ErrorTypes.BEST_OFFERS_SELECTED_OFFER_UNAVAILABLE]: {
    description: "errors.best_offers.selected_offer_unavailable.description",
    links: [
      {
        text: "errors.best_offers.input_problem.cta",
        to: "homepage"
      }
    ],
    message: "errors.best_offers.selected_offer_unavailable.title"
  },
  [ErrorTypes.INVALID_HOTEL_ID]: {
    description: "errors.invalid_hotel_id.description",
    links: [
      {
        text: "errors.best_offers.input_problem.cta",
        to: "homepage"
      }
    ],
    message: "errors.invalid_hotel_id.title"
  },
  [ErrorTypes.IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID]: {
    description: "errors.ident_pmid_not_equal_to_bearer_pmid.description",
    links: [
      {
        target: "_blank",
        text: "errors.ident_pmid_not_equal_to_bearer_pmid.cta",
        to: "login"
      }
    ],
    message: "errors.ident_pmid_not_equal_to_bearer_pmid.title"
  }
}
