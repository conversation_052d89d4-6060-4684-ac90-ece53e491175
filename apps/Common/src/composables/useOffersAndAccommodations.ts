import { Api<PERSON>estOffers, Offer } from "../services/offer/offer.types"
import { type AvailabilityStatus, RoomClassCode } from "../services/offer/offer.enums"
import { DateMoment, UiRoomDetailsModalContentProps } from "design-system"
import { ErrorTypes, useHTTPError } from "./useHTTPError"
import { computed, ref } from "vue"
import { fetchOffers, getAccommodations } from "../services/offer/offer.fetch"
import { Accommodation } from "../services/hotels/hotels.types"
import { AxiosError } from "axios"
import { MatchedPair } from "../services/offer/offer.interface"
import { accommodationMapper } from "../services/accommodation/accommodation.mapper"
import { mapAndGroupByRoomClass } from "../services/offer/offer.mapper"
import { useBasket } from "./useBasket"
import { useCurrentRoom } from "./useCurrentRoom"
import { useIdentificationPlainText } from "./useIdentificationPlainText"
import { useLoader } from "./useLoader"
import { useSearch } from "./useSearch"
import { useSearchConstraints } from "./useSearchConstraints"
import { useUserPOS } from "./useUserPOS"
import { useUserStore } from "../stores/user/user"

export type FetchAllOffersOptions = {
  adults?: number
  childrenAges?: number[]
}

type Section = {
  code: RoomClassCode
  pairs: MatchedPair[]
  title: string
}

export type BestOffersError = {
  code: string
  detail: string
  fieldErrors: {
    code: string
    input: string
    detail: string
  }[]
  status: number
  title: string
}

const availabilityStatus = ref<AvailabilityStatus | "">("")
const offers = ref<Offer[]>([])
const accommodations = ref<Map<string, Accommodation>>(new Map())
const selectedRoomsDetails = ref<Map<string, UiRoomDetailsModalContentProps>>(new Map())
const groupBy = ref<ApiBestOffers["groupBy"]>()
const sections = ref<Section[]>([])

export const useOffersAndAccommodations = () => {
  const { selectedOffers } = useBasket()
  const { currentRoom } = useCurrentRoom()
  const { hotelCode, rooms, lengthOfStay, dates } = useSearch()
  const { occupancyConstraints } = useSearchConstraints()
  const { refreshIdentification } = useIdentificationPlainText()
  const { userPOS } = useUserPOS()
  const user = useUserStore()
  const { loading: isLoadingOffers } = useLoader("search")
  const { error, generateError } = useHTTPError("offers")

  const fetchAllOffers = async () => {
    try {
      error.value = false

      //Empty composable to avoid displaying data during loading times of the API
      offers.value = []
      accommodations.value.clear()
      groupBy.value = undefined
      sections.value = []
      availabilityStatus.value = ""

      if (!hotelCode.value || !userPOS.countryMarket) return

      const apiResult = await fetchOffers(
        hotelCode.value,
        dates.value[DateMoment.START].toISOString().split("T")[0],
        lengthOfStay.value,
        currentRoom.value.adults,
        currentRoom.value.childrenAges,
        userPOS.currency,
        currentRoom.value.accessibility ?? false,
        userPOS.countryMarket
      )

      availabilityStatus.value = apiResult.availability.status
      offers.value = apiResult.bestOffers
      groupBy.value = apiResult.groupBy

      sections.value = mapAndGroupByRoomClass(
        offers.value,
        userPOS.countryMarket,
        lengthOfStay.value,
        user.isLogged,
        groupBy.value
      )

      // Do not put finally since the handleError might retry the request
    } catch (apiError) {
      await handleError(apiError as AxiosError<BestOffersError>)
    }
  }

  // Exposed for testing
  const handleError = async (apiError: AxiosError<BestOffersError>) => {
    const code = apiError?.response?.data?.code
    const status = apiError?.response?.status
    const fieldErrors = apiError?.response?.data?.fieldErrors

    // eslint-disable-next-line no-console
    console.error(apiError)

    if (!code || !fieldErrors || !status) {
      // Generic error
      error.value = generateError(ErrorTypes.GENERIC)
      return
    }

    if ([401, 403, 500].includes(status)) {
      // TODO: expired and missing are PURE assumption, waiting for API confirmation
      if (["IDENT_TOKEN_INVALID", "IDENT_TOKEN_EXPIRED", "IDENT_TOKEN_MISSING"].includes(code)) {
        // Refresh identification and refetch offers
        isLoadingOffers.value = true

        await refreshIdentification()

        await fetchAllOffers()
      } else {
        error.value = generateError(ErrorTypes.GENERIC)
      }
    } else if (status === 400) {
      if (code === "INVALID_INPUT") {
        // Check dateIn or maxAdult/child/maxPax
        if (fieldErrors.some((field) => field.input === "dateIn")) {
          // Reset dates
          const today = new Date()
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)

          dates.value = [today, tomorrow]

          await fetchAllOffers()

          return
        } else if (fieldErrors.some((field) => field.input === "adults")) {
          error.value = generateError(ErrorTypes.BEST_OFFERS_MAX_ADULTS_EXCEEDED, {
            maxAdult: occupancyConstraints.value?.maxAdult
          })
        } else if (fieldErrors.some((field) => field.input === "pax")) {
          error.value = generateError(ErrorTypes.BEST_OFFERS_MAX_PAX_EXCEEDED, {
            maxPax: occupancyConstraints.value?.maxPax
          })
        } else if (fieldErrors.some((field) => field.input === "childrenAges")) {
          error.value = generateError(ErrorTypes.BEST_OFFERS_MAX_CHILD_EXCEEDED, {
            maxChild: occupancyConstraints.value?.maxChild
          })
        } else if (fieldErrors.some((field) => field.input === "room")) {
          error.value = generateError(ErrorTypes.BEST_OFFERS_MAX_ROOM_EXCEEDED, {
            maxRoom: occupancyConstraints.value?.maxRoom
          })
        } else {
          error.value = generateError(ErrorTypes.BEST_OFFERS_INPUT_PROBLEM)
        }
      } else if (["USER_HAS_NOT_ENOUGH_STAY_PLUS", "USER_HAS_NOT_ENOUGH_SNUS"].includes(code)) {
        error.value = generateError(ErrorTypes.BEST_OFFERS_INPUT_PROBLEM)
      } else if (code === "IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID") {
        error.value = generateError(ErrorTypes.IDENT_PMID_NOT_EQUAL_TO_BEARER_PMID)
      } else if (code === "INVALID_HOTEL_ID") {
        error.value = generateError(ErrorTypes.INVALID_HOTEL_ID)
      } else if (code === "OOPS") {
        error.value = generateError(ErrorTypes.GENERIC)
      } else {
        error.value = generateError(ErrorTypes.GENERIC)
      }
    } else {
      error.value = generateError(ErrorTypes.GENERIC)
    }
  }

  const getOfferAccommodations = async (productId: string) => {
    if (!hotelCode.value || accommodations.value.get(productId)) return

    const response = await getAccommodations(hotelCode.value, productId)

    response.accommodations.forEach((accommodation) => accommodations.value.set(productId, accommodation))
  }

  const getOfferAccommodationById = async (offer: Offer) => {
    await getOfferAccommodations(offer.product.id)

    return accommodations.value.get(offer.product.id)
  }

  const fetchAllSelectedRoomsDetails = async (productCodes: string[]) => {
    if (productCodes.length === 0) {
      selectedRoomsDetails.value.clear()
      return
    }

    // We get all selected offers from the basket and the offers from the API because we can have offers in the basket that are not in the API (due to accessibility boolean)
    const allSelectedOffers = [...offers.value, ...selectedOffers.value].filter((offer) =>
      productCodes.includes(offer.product.id)
    )

    allSelectedOffers.forEach(async (offer) => {
      const accommodation = await getOfferAccommodationById(offer)

      selectedRoomsDetails.value.set(offer.product.id, accommodationMapper(offer.product, accommodation))
    })
  }

  const getRoomDetails = (productCode?: string) => {
    if (!productCode) return null

    return selectedRoomsDetails.value.get(productCode)
  }

  const hasAccessibility = computed(() => rooms.value.some((room) => room.accessibility))

  const higherDeductionPercentageInAllOffers = computed(() =>
    offers.value.reduce((acc, offer) => {
      const deduction = offer.pricing?.deduction?.[0]
      const deductionPercentage = deduction?.percent
      return Math.max(acc, deductionPercentage ?? 0)
    }, 0)
  )

  return {
    accommodations,
    availabilityStatus,
    fetchAllSelectedRoomsDetails,
    fetchOffers: fetchAllOffers,
    getOfferAccommodationById,
    getOfferAccommodations,
    getRoomDetails,
    groupBy,
    handleError,
    hasAccessibility,
    higherDeductionPercentageInAllOffers,
    isLoadingOffers,
    offers,
    sections
  }
}
