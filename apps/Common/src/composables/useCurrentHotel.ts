import { Destination, UiImageProps } from "design-system"
import { onMounted, ref, watch } from "vue"
import { ApiHotel } from "../services/hotels/hotels.types"
import { CrsEnum } from "../services/hotels/hotels.enums"
import { getMedias } from "../services/hotels/hotels.fetch"
import { hotelMediaMapper } from "../services/utils/media/media.mapper"
import { useHotelsStore } from "../stores/hotels/hotels"
import { useSearch } from "./useSearch"

export interface SearchHotel extends Destination {
  medias?: UiImageProps[]
  checkInHour?: string
  checkOutHour?: string
  crs?: CrsEnum
}

const hotel = ref<SearchHotel>()
const isLoading = ref<boolean>(false)

export const useCurrentHotel = () => {
  const { hotelsList } = useHotelsStore()
  const search = useSearch()

  const hydrateHotel = async () => {
    if (isLoading.value || search.hotel.value?.id === hotel.value?.id) return

    isLoading.value = true

    hotel.value = search.hotel.value

    const selectedHotel: ApiHotel | undefined = Object.values(hotelsList)
      .flat()
      .find((apiHotel: ApiHotel) => apiHotel.id === hotel.value?.id)

    if (selectedHotel && hotel.value) {
      const hotelImages = await getMedias(selectedHotel.id)

      hotel.value.medias = hotelMediaMapper(hotelImages, selectedHotel.name) // Use the name as the label for alt text)
      hotel.value.checkInHour = selectedHotel.checkInHour
      hotel.value.checkOutHour = selectedHotel.checkOutHour
      hotel.value.crs = selectedHotel.crs
    }

    isLoading.value = false
  }

  onMounted(hydrateHotel)

  watch(search.hotel, hydrateHotel)

  return { hotel }
}
