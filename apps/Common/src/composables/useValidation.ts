import { differenceInCalendarDays, isAfter } from "date-fns"
import type { UiDestinationListDropdownItem } from "design-system"
import { i18n } from "../i18n"

export const destinationValidation = (
  hotelName: string,
  hotels: UiDestinationListDropdownItem[],
  allowEmptyString?: boolean
) => {
  let errorMessage

  if (!hotelName && !allowEmptyString) {
    errorMessage = i18n.global.t("errors.select_property")
  } else if (hotels.length === 0) {
    errorMessage = i18n.global.t("errors.no_property")
  }

  return errorMessage
}

export const datePickerInputsValidation = (startDate?: string, endDate?: string) => {
  let startIsValid = true
  let endDateIsValid = true

  if (!startDate || startDate === "" || startDate === "-") {
    startIsValid = false
  }

  if (!endDate || endDate === "" || endDate === "-") {
    endDateIsValid = false
  }

  if (startDate && endDate && Math.abs(differenceInCalendarDays(startDate, endDate)) > 30) {
    startIsValid = false
    endDateIsValid = false
  }
  return { endDate: endDateIsValid, startDate: startIsValid }
}

export const datePickerValidation = (startDate: string, endDate: string, allowEmptyDates?: boolean) => {
  // In the desktop's case, we can directly use the composable useSearch.
  // In the mobile's case, we have to use params because the dates are only updated when the user clicks on "apply"
  const fallbackStartDate = startDate
  const fallbackEndDate = endDate

  const today = new Date().setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  const limitDate = new Date(today)
  limitDate.setDate(tomorrow.getDate() + 405)

  let errorMessage = undefined

  // Case for start date input's value is undefined or is "-" or start date < today, unless we allow empty dates
  if (!allowEmptyDates && (!fallbackStartDate || fallbackStartDate === "-" || isAfter(today, startDate as string))) {
    errorMessage = i18n.global.t("errors.start_date_format")
  }

  // Case for end date input's value is undefined or is "-" or end date < tomorrow, unless we allow empty dates
  else if (!allowEmptyDates && (!fallbackEndDate || fallbackEndDate === "-" || isAfter(tomorrow, endDate as string))) {
    errorMessage = i18n.global.t("errors.end_date_format")
  }

  // Case for date > 405 days
  else if (isAfter(startDate as string, limitDate) || isAfter(endDate as string, limitDate)) {
    errorMessage = i18n.global.t("errors.date_limit")
  }

  // 30 days + case, only applicable if both start & end date are correctly filled
  else if (!allowEmptyDates && Math.abs(differenceInCalendarDays(fallbackStartDate, fallbackEndDate)) > 30) {
    errorMessage = i18n.global.t("errors.max_nights")
  }

  return errorMessage
}
