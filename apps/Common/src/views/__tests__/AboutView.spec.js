import { describe, expect, it } from "vitest"

import { mount } from "@vue/test-utils"
import TestedPage from "../AboutView.vue"

describe("About Page", () => {
  it("renders properly", () => {
    const wrapper = mount(TestedPage, {
      global: {
        stubs: {
          RouterLink: true
        }
      }
    })

    // Find the <h1> element
    const heading = wrapper.find("h1")

    // Assert that the <h1> contains the correct text
    expect(heading.exists()).toBe(true)
    expect(heading.text()).toBe("This is an about page")
  })
})
