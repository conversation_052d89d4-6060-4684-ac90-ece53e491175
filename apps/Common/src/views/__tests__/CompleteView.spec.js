import { describe, expect, it, vi } from "vitest"
import { createRouter, createWebHistory } from "vue-router"

import { mount } from "@vue/test-utils"
import TestedPage from "../CompleteView.vue"

vi.mock("../../composables/useUserPOS")

describe("Complete Page", () => {
  const router = createRouter({
    history: createWebHistory(),
    routes: []
  })

  it("renders properly", () => {
    const wrapper = mount(TestedPage, {
      global: {
        plugins: [router]
      }
    })

    // Find the <h1> element
    const heading = wrapper.find("h1")

    // Assert that the <h1> contains the correct text
    expect(heading.exists()).toBe(true)
    expect(heading.text()).contains("complete your booking")
  })
})
