import { describe, expect, it, vi } from "vitest"
import { createRouter, createWeb<PERSON>istory } from "vue-router"

import { shallowMount } from "@vue/test-utils"
import PageTitle from "../../components/PageTitle/PageTitle.vue"
import TestedPage from "../SearchView.vue"

vi.mock("universal-cookie", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn()
    }))
  }
})

vi.mock("../../composables/useUserPOS")

vi.mock("../../services/referentials/referentials.fetch.ts", () => ({
  fetchLists: vi.fn().mockResolvedValue({
    calendar: {
      maxDate: 405,
      maxLengthOfStay: 30,
      minLengthOfStay: 1
    },
    "room-occupancy": {
      maxRoom: 3,
      maxPax: 6,
      maxAdult: 6,
      maxChild: 3,
      maxChildAge: 16
    }
  })
}))

// Mock router links
const routes = [
  { path: "/", component: { template: "<div>Home Page</div>" } },
  { path: "/search", component: { template: "<div>Search Page</div>" } },
  { path: "/enhance", component: { template: "<div>Enhance Page</div>" } },
  { path: "/complete", component: { template: "<div>Complete Page</div>" } },
  { path: "/summary", component: { template: "<div>Summary Page</div>" } },
  { path: "/about", component: { template: "<div>About Page</div>" } }
]
const router = createRouter({
  history: createWebHistory(),
  routes
})

describe("Search Page", () => {
  it("renders properly", () => {
    const wrapper = shallowMount(TestedPage, {
      global: {
        plugins: [router]
      }
    })
    const heading = wrapper.findComponent(PageTitle)
    expect(heading.exists()).toBe(true)

    const headingWrapper = shallowMount(PageTitle, {
      props: {
        left: "SEARCH",
        middle: " your ",
        right: "ROOM"
      }
    })

    // Find the <h1> element
    const h1 = headingWrapper.find("h1")

    // Assert that the <h1> contains the correct text
    expect(h1.exists()).toBe(true)
    expect(h1.text()).contains("SEARCH your ROOM")
  })
})
