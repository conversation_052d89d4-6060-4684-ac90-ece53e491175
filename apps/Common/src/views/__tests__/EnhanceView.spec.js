import { describe, expect, it } from "vitest"

import { mount } from "@vue/test-utils"
import TestedPage from "../EnhanceView.vue"

describe("Enhance Page", () => {
  it("renders properly", () => {
    const wrapper = mount(TestedPage)

    // Find the <h1> element
    const heading = wrapper.find("h1")

    // Assert that the <h1> contains the correct text
    expect(heading.exists()).toBe(true)
    expect(heading.text()).contains("enhance your stay")
  })
})
