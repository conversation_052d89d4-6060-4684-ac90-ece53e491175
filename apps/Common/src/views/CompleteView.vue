<script setup lang="ts">
import { useBasket, useGA4Event, useSearch, useUserPOS } from "../composables"
import { EventNameEnum } from "../global/enums"
import FormBuilder from "../components/FormBuilder/FormBuilder.vue"
import LegalText from "../components/LegalText/LegalText.vue"
import PageTitle from "../components/PageTitle/PageTitle.vue"
import { onMounted } from "vue"
import { useHead } from "@vueuse/head"
import { useI18n } from "vue-i18n"

const { hotel } = useSearch()
const { t } = useI18n()
const { basket } = useBasket()
const { userPOS } = useUserPOS()
const { pushGA4Event } = useGA4Event()

onMounted(() => {
  pushGA4Event("step4", "complete details", {
    eventName: EventNameEnum.funnel_step,
    event_data: {
      // TODO: Need to handle when error occur for the second event https://docs.google.com/spreadsheets/d/1WN3lRicWYF8fahuO2Kssr6zn2iqRUkkT/edit?gid=577679097#gid=577679097&range=N31:O31
      error_type: "",
      step_name: "step4"
    }
  })
})

useHead({
  meta: [
    {
      content: t("seo.step_4_description", { hotel: hotel.value?.name }),
      name: "description"
    }
  ],
  title: t("seo.step_4_title", { hotel: hotel.value?.name })
})
</script>

<template>
  <PageTitle
    :left="$t('page.complete_view.title.left')"
    :middle="$t('page.complete_view.title.middle')"
    :right="$t('page.complete_view.title.right')"
  />

  <FormBuilder
    v-if="basket?.id"
    :basket-id="basket.id"
    :country-market="userPOS.countryMarket"
    :currency="userPOS.currency"
  />

  <LegalText />
</template>
