<script setup lang="ts">
import PageTitle from "../components/PageTitle/PageTitle.vue"
import { useHead } from "@vueuse/head"
import { useI18n } from "vue-i18n"
import { useSearch } from "../composables"

const { hotel } = useSearch()
const { t } = useI18n()

useHead({
  meta: [
    {
      content: t("seo.step_3_description", { hotel: hotel.value?.name }),
      name: "description"
    }
  ],
  title: t("seo.step_3_title", { hotel: hotel.value?.name })
})
</script>

<template>
  <PageTitle
    :left="$t('page.enhance_view.title.left')"
    :middle="$t('page.enhance_view.title.middle')"
    :right="$t('page.enhance_view.title.right')"
  />
</template>
