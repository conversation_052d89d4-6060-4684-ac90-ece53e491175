<script setup lang="ts">
import { CrsEnum, OfferCategoryCode } from "../services/hotels/hotels.enums"
import { DividerDirection, SpecialRates, UiBanner, UiDivider, UiMessage, UiMessageVariation } from "design-system"
import { EcommerceEventNameEnum, EventNameEnum } from "../global/enums"
import { computed, nextTick, onBeforeMount, onMounted, reactive, ref, watch } from "vue"
import {
  useBasket,
  useCommerceTracking,
  useCurrentHotel,
  useCurrentRoom,
  useEcommerceEvent,
  useGA4Event,
  useHTTPErrors,
  useIdentificationPlainText,
  useLoader,
  usePageViewEvent,
  usePricingCondition,
  useSearch,
  useSearchQueryParams
} from "../composables"
import { AvailabilityStatus } from "../services/offer/offer.enums"
import ErrorMessage from "../components/ErrorMessage/ErrorMessage.vue"
import { Offer } from "../services/offer/offer.types"
import PageTitle from "../components/PageTitle/PageTitle.vue"
import RoomSection from "../components/StayView/RoomSection/RoomSection.vue"
import UiRoomSectionSkeleton from "../components/Skeletons/RoomSectionSkeleton/RoomSectionSkeleton.vue"
import { getGlobalConfig } from "../global/config"
import { storeToRefs } from "pinia"
import stringify from "json-stable-stringify"
import { useHead } from "@vueuse/head"
import { useI18n } from "vue-i18n"
import { useOffersAndAccommodations } from "../composables/useOffersAndAccommodations"
import { useOidcUri } from "../composables/useOidcUri"
import { useRouter } from "vue-router"
import { useUserPOS } from "../composables/useUserPOS"
import { useUserStore } from "../stores/user/user"

const {
  availabilityStatus,
  offers,
  fetchOffers,
  sections,
  hasAccessibility,
  higherDeductionPercentageInAllOffers,
  isLoadingOffers,
  fetchAllSelectedRoomsDetails
} = useOffersAndAccommodations()
const { specialRate, rooms, rateIds, hotel, lengthOfStay, productCodes } = useSearch()
const { hotel: currentHotel } = useCurrentHotel()
const { enhancePermalink, queryParams } = useSearchQueryParams()
const { ensureEmptyBasket, upsertBasket, basket, selectedOffers } = useBasket()
const { userPOS } = useUserPOS()
const { pushEcommerceEvent } = useEcommerceEvent()
const { pushPageViewEvent } = usePageViewEvent()
const { commerceTracking } = useCommerceTracking()
const { pushGA4Event } = useGA4Event()
const { identification } = useIdentificationPlainText()
const userStore = useUserStore()
const { isLogged } = storeToRefs(userStore)
const { errors: httpErrors } = useHTTPErrors()
const { currentRoomIndex, currentRoom, haveNextRoom } = useCurrentRoom()
const loadingRateId = ref()
const { loading: searchLoading } = useLoader("search")
const { loading: becomeMemberLoading } = useLoader("become-member")
const { fetchMultiplePricingCondition } = usePricingCondition()

const { t } = useI18n()
const router = useRouter()

const eventsGtmAlreadySent = ref(false)

const { signInUri } = useOidcUri()

const showAccessibilityWarning = computed(() => {
  return (
    hasAccessibility.value && availabilityStatus.value === AvailabilityStatus.AVAILABLE && offers.value.length === 0
  )
})

const showNoResultsBanner = computed(() => {
  return availabilityStatus.value === AvailabilityStatus.UNAVAILABLE
})

function handlePageViewEvent() {
  pushPageViewEvent(
    "step2",
    "select a room",
    !showNoResultsBanner.value
      ? {
          merchant_id: commerceTracking.value?.outputBestVisit?.merchantId ?? "",
          source_id: commerceTracking.value?.outputBestVisit?.sourceId ?? ""
        }
      : {
          error_type: "no room available",
          merchant_id: commerceTracking.value?.outputBestVisit?.merchantId ?? "",
          source_id: commerceTracking.value?.outputBestVisit?.sourceId ?? ""
        }
  )
}

function handleOtherGtmEvents() {
  const roomTypes = sections.value.map((section) => section.title).join(", ")
  const config = getGlobalConfig()

  const eventData = !showNoResultsBanner.value
    ? {
        error_type: "",
        results_nb: sections.value.length.toString(),
        room_number_available: offers.value.length.toString(),
        room_type_available: roomTypes,
        step_name: "step2"
      }
    : {
        error_type: "no room available",
        results_nb: "0",
        room_type_available: roomTypes,
        step_name: "step2::error"
      }

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.funnel_start,
    event_data: eventData
  })

  pushEcommerceEvent(
    {
      items: [
        {
          item_brand: config.siteCode,
          item_category: "room",
          item_id: hotel.value?.id.toLowerCase(),
          item_name: hotel.value?.name.toLowerCase(),
          night_nb: lengthOfStay.value.toString()
        }
      ]
    },
    EcommerceEventNameEnum.begin_checkout
  )
}

const hasSpecialRate = computed(() => specialRate.value !== SpecialRates.NONE)

const missingSpecialOffers = computed(() => {
  if (!hasSpecialRate.value) return false
  const hasPreferredOffers = offers.value.some((offer) =>
    offer.categories.some((category) => category.code === OfferCategoryCode.PREFERRED)
  )
  return !hasPreferredOffers
})

const error = computed(() => {
  return (
    httpErrors.offers ||
    httpErrors.rate ||
    httpErrors.basket ||
    httpErrors.basketSummary ||
    httpErrors.identification ||
    httpErrors.pos
  )
})

const expandedState = reactive<{ [key: string]: boolean }>({})

const reactiveSections = computed(() =>
  (sections.value || []).map((section) => ({
    ...section,
    expanded: expandedState[section.title] || false
  }))
)

const handleBecomeMember = () => {
  try {
    becomeMemberLoading.value = true

    window.location.href = signInUri.value
  } finally {
    becomeMemberLoading.value = false
  }
}

watch(
  queryParams,
  async (newValue, oldValue) => {
    const hasChanged = stringify(newValue) !== stringify(oldValue)

    if (hasChanged) {
      eventsGtmAlreadySent.value = false
      handlePageViewEvent()

      await nextTick()
    }
  },
  { deep: true }
)

watch(
  offers,
  () => {
    if (offers.value.length > 0 && !eventsGtmAlreadySent.value) {
      eventsGtmAlreadySent.value = true
      handleOtherGtmEvents()
    }
  },
  { immediate: true }
)

onBeforeMount(() => {
  ensureEmptyBasket()
})

const fetch = async () => {
  searchLoading.value = true

  try {
    await fetchOffers()

    if (basket.value && basket.value.currency !== userPOS.currency) {
      await upsertBasket([...rateIds.value])
    }
  } finally {
    searchLoading.value = false
  }
}

onMounted(async () => {
  await fetch()
})

watch(userPOS, async () => {
  await fetch()
})

watch(currentRoomIndex, async () => {
  await fetch()
})

const handleOfferChosen = async ({
  apiOffer,
  classCode,
  rateCode,
  rateId,
  productCode
}: {
  apiOffer: Offer
  classCode?: string
  rateId: string
  rateCode: string
  productCode: string
}) => {
  loadingRateId.value = rateId

  const shouldSkipLegacyRedirect =
    (import.meta.env.DEV && router.currentRoute.value.query.skipRedirection) || !enhancePermalink.value

  try {
    currentRoom.value = { ...currentRoom.value, classCode, productCode, rateCode, rateId }

    selectedOffers.value.push(apiOffer)

    // TODO this condition permit to stay on LBF side when we are in dev mode, the user is on the last room, the CRS property is ACRS and the skipRedirection query param is set in the url
    if (
      import.meta.env.DEV &&
      router.currentRoute.value.query.skipRedirection &&
      (haveNextRoom.value || currentHotel.value?.crs === CrsEnum.ACRS)
    ) {
      await upsertBasket([...rateIds.value, rateId])
    }

    const allRateIds = [...new Set([...rateIds.value, rateId])]
    const allProductCodes = [...new Set([...productCodes.value, productCode])]

    // Always fetch pricing conditions for all selected rooms
    await fetchMultiplePricingCondition(allRateIds)
    await fetchAllSelectedRoomsDetails(allProductCodes)

    nextTick(() => {
      if (haveNextRoom.value) {
        router.replace({
          name: "stay",
          query: queryParams.value
        })
      } else {
        if (shouldSkipLegacyRedirect) {
          router.push({
            name: "complete"
          })
        } else {
          window.location.href = enhancePermalink.value.href
        }
      }
    })
  } finally {
    if (haveNextRoom.value || shouldSkipLegacyRedirect) {
      loadingRateId.value = undefined
    }
  }
}

const seoDescription = computed(() => {
  return hotel.value?.name
    ? t("seo.step_2_description", { hotel: hotel.value?.name })
    : t("seo.step_2_description_no_hotel")
})

const seoTitle = computed(() => {
  return hotel.value?.name ? t("seo.step_2_title", { hotel: hotel.value?.name }) : t("seo.step_2_title_no_hotel")
})

watch(
  hotel,
  () => {
    useHead({
      meta: [
        {
          content: seoDescription.value,
          name: "description"
        }
      ],
      title: seoTitle.value
    })
  },
  { immediate: true }
)
</script>

<template>
  <div class="Stay-view">
    <PageTitle
      :left="$t('page.stay_view.title.left')"
      :middle="$t('page.stay_view.title.middle')"
      :right="rooms.length > 1 ? $t('page.stay_view.title.right_multiroom') : $t('page.stay_view.title.right')"
      :down="
        rooms.length > 1
          ? $t('page.stay_view.title.down_multiroom', { room: currentRoomIndex + 1, total: rooms.length })
          : ''
      "
    />

    <div class="Stay-view__content">
      <UiDivider
        v-if="availabilityStatus === AvailabilityStatus.AVAILABLE"
        :direction="DividerDirection.HORIZONTAL"
        class="Stay-view__divider"
      />

      <UiBanner
        v-if="!identification.lcahmember && !isLogged && higherDeductionPercentageInAllOffers > 0"
        class="Stay-view__banner"
        :loading="becomeMemberLoading"
        :percentage="higherDeductionPercentageInAllOffers"
        @become-member:banner="handleBecomeMember"
      />

      <ErrorMessage v-if="error" :error="error" />

      <div
        v-if="!isLoadingOffers && (missingSpecialOffers || showAccessibilityWarning || showNoResultsBanner)"
        class="Stay-view__message"
      >
        <UiMessage
          v-if="missingSpecialOffers && !showNoResultsBanner"
          :description="$t('errors.stay.no_special_offers')"
          :variation="UiMessageVariation.WARNING"
        />

        <UiMessage
          v-if="showAccessibilityWarning && !showNoResultsBanner"
          :description="$t('errors.stay.no_accessibility_room')"
          :variation="UiMessageVariation.WARNING"
        />

        <UiMessage
          v-if="showNoResultsBanner"
          :description="$t('components.stay_view.no_results')"
          :variation="UiMessageVariation.DANGER"
        />
      </div>

      <div class="Stay-view__offer-container" aria-live="polite" :aria-busy="isLoadingOffers">
        <template v-if="!isLoadingOffers">
          <ul v-for="(section, index) in reactiveSections" :key="section.title">
            <li>
              <RoomSection
                v-model:expanded="expandedState[section.title]"
                :title="section.title"
                :pairs="section.pairs"
                :class-code="section.code"
                :has-divider="index !== reactiveSections.length - 1"
                :eager-loading="index === 0"
                :loading-rate-id="loadingRateId"
                @choose-offer="handleOfferChosen"
              />
            </li>
          </ul>
        </template>
        <template v-else>
          <UiRoomSectionSkeleton />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-utilities/spaces" as spacesUtils;
@use "@sb-utilities/mq";

.Stay-view {
  &__divider {
    display: none;

    @include mq.media(">=small") {
      display: unset;
      padding-bottom: map.get(spaces.$sizes, "10");
    }
  }

  &__banner {
    margin-top: map.get(spaces.$sizes, "10");
    margin-bottom: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      margin-bottom: map.get(spaces.$sizes, "8");
    }

    @include mq.media(">=medium") {
      margin-bottom: map.get(spaces.$sizes, "10");
    }
  }

  &__offer-container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "8");

    @include mq.media(">=medium") {
      gap: map.get(spaces.$sizes, "10");
    }
  }

  &__offer-item {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "8");

    @include mq.media(">=medium") {
      gap: map.get(spaces.$sizes, "10");
    }
  }
}

.Stay-view__message {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");
  margin-bottom: map.get(spaces.$sizes, "10");
}
</style>
