<script setup>
import { useHead } from "@vueuse/head"
import { useI18n } from "vue-i18n"
import { useSearch } from "@/composables"

const { hotel } = useSearch()
const { t } = useI18n()

useHead({
  meta: [
    {
      content: t("seo.step_5_description", { hotel: hotel.value?.name }),
      name: "description"
    }
  ],
  title: t("seo.step_5_title", { hotel: hotel.value?.name })
})
</script>

<template>
  <h1>This is Summary page</h1>
</template>
