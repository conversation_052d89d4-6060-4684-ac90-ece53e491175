<script setup lang="ts"></script>

<template>
  <div class="Main-layout">
    <slot name="header" />

    <main>
      <slot name="subheader" />

      <div class="container">
        <div class="grid">
          <div
            class="Main-layout__main-content"
            :class="{ 'Main-layout__main-content--no-sidebar': !$slots['sidebar-content'] }"
          >
            <slot name="main-content" />
          </div>

          <div v-if="$slots['sidebar-content']" class="Main-layout__sidebar-content">
            <slot name="sidebar-content" />
          </div>
        </div>
      </div>
    </main>

    <slot name="footer" />
  </div>
</template>

<style lang="scss" scoped>
@use "./MainLayout.scss";
</style>
