import { BookingInput, Hotel } from "@shared/types"
import { defineStore } from "pinia"
import { ref } from "vue"

export const useAppStore = defineStore("application", () => {
  const hotels = ref<Hotel[]>([])
  const userParams = ref<BookingInput | null>(null)

  function setUserParams(params: BookingInput) {
    userParams.value = params
  }

  function initHotels(hotelList: Hotel[]) {
    hotels.value = hotelList
    //get hotels from other sources if needed
  }

  return { hotels, initHotels, setUserParams, userParams }
})
