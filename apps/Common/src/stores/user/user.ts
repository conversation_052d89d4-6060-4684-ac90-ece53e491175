import { computed, reactive } from "vue"
import { User } from "./interface"
import { defineStore } from "pinia"

export const useUserStore = defineStore("user", () => {
  const loggedUser = reactive<User>({
    cardNumber: null,
    city: "",
    countryCode: "",
    email: "",
    firstName: "",
    id: "",
    isLoyaltyMember: false,
    lastName: "",
    loyaltyCardType: null,
    nightValue: 0,
    rewardPointsValue: 0,
    statusName: "",
    statusPointsValue: 0,
    statusSince: ""
  })

  const isLogged = computed(() => !!loggedUser.id) // TODO we have to fix it as it's sometimes can be not updated at the good time

  const updateInitialUser = (user: User) => {
    Object.assign(loggedUser, user)
  }

  const clearUserStore = () => {
    Object.assign(loggedUser, {
      city: "",
      countryCode: "",
      email: "",
      firstName: "",
      id: "",
      isLoyaltyMember: false,
      lastName: "",
      loyaltyCardType: "",
      nightValue: 0,
      rewardPointsValue: 0,
      statusName: "",
      statusPointsValue: 0,
      statusSince: ""
    })
  }

  return {
    clearUserStore,
    isLogged,
    loggedUser,
    updateInitialUser
  }
})
