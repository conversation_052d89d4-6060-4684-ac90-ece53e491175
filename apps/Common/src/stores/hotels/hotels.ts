import { reactive, ref } from "vue"
import { ApiHotel } from "../../services/hotels/hotels.types"
import { HotelsList } from "./interface"
import { defineStore } from "pinia"

const isLoading = ref<boolean>(true)

export const useHotelsStore = defineStore("hotels", () => {
  const hotelsList = reactive<HotelsList>({
    africa: [],
    asia: [],
    europe: [],
    northAmerica: [],
    southAmerica: []
  })

  const updateHotelsList = (newHotelsList: HotelsList) => {
    Object.assign(hotelsList, newHotelsList)
  }

  const updateHotelsByRegion = (region: keyof HotelsList, hotels: ApiHotel[]) => {
    hotelsList[region] = hotels
  }

  const updateIsLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  return {
    hotelsList,
    isLoading,
    updateHotelsByRegion,
    updateHotelsList,
    updateIsLoading
  }
})
