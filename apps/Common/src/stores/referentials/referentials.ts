import { Ref, ref } from "vue"
import { Country } from "../../services/referentials/referentials.type"
import { Referentials } from "./interface"
import { defineStore } from "pinia"

export const useReferentialsStore = defineStore("referentials", () => {
  const referentials: Ref<Referentials> = ref({
    countries: []
  })

  const updateCountries = (countries: Country[]) => {
    referentials.value.countries = countries
  }

  return {
    referentials,
    updateCountries
  }
})
