<script setup lang="ts">
import { UiButton, UiButtonSize, UiButtonVariation, UiHeaderModal, UiSelect } from "design-system"
import { nextTick, ref, useTemplateRef, watch } from "vue"
import { useLoader, useModal, usePosCurrencyHandler } from "../../composables"
import { CurrencyModalProps } from "./interface"
import { Option } from "@accor/ads-components"
import { moveToTop } from "../../helpers"
import { updateCurrencyCookies } from "../../services/pos/pos.fetch"
import { usePosStore } from "@stores/pos/pos"
import { useUserPOS } from "../../composables/useUserPOS"

const { loading } = useLoader("currency")
const { isModalOpen, closeModal, toggleModal } = useModal("currency")
const { currentCurrencyLabel, resetStateCurrency } = usePosCurrencyHandler()
const { findCurrencyObjectByAccorCurrencyCode } = usePosStore()
const activator = useTemplateRef<HTMLButtonElement>("activator")
const { refreshPOS } = useUserPOS()

const emits = defineEmits(["close:currencyModal"])
const props = defineProps<CurrencyModalProps>()

const geographicalArea = defineModel<string>("geographicalArea")
const currency = defineModel<string>("currency")

const closeCurrencyModal = () => {
  closeModal()
  emits("close:currencyModal")
}

async function handleSubmitCurrencyModal() {
  if (!currency.value) return

  loading.value = true

  const currencyObject = findCurrencyObjectByAccorCurrencyCode(currency.value)
  if (!currencyObject) return

  await updateCurrencyCookies(currencyObject)
  refreshPOS()
  resetStateCurrency()

  loading.value = false

  closeModal()
}

const orderedGeographicalOptions = ref(
  moveToTop(
    props.geographicalOptions,
    props.geographicalOptions.find((option) => option.value === geographicalArea.value)
  ) as Option[]
)

const orderedCurrencyOptions = ref(
  moveToTop(
    props.currencyOptions,
    props.currencyOptions.find((option) => option.value === currency.value)
  ) as Option[]
)

watch(
  () => geographicalArea.value,
  async () => {
    await nextTick(() => {
      orderedGeographicalOptions.value = moveToTop(
        props.geographicalOptions,
        props.geographicalOptions.find((option) => option.value === geographicalArea.value)
      ) as Option[]
    })
  }
)

watch(
  () => currency.value,
  async () => {
    await nextTick(() => {
      orderedCurrencyOptions.value = moveToTop(
        props.currencyOptions,
        props.currencyOptions.find((option) => option.value === currency.value)
      ) as Option[]
    })
  }
)
</script>

<template>
  <div ref="currency-modal" class="Currency-modal">
    <UiButton
      ref="activator"
      class="Currency-modal__button"
      :text="currentCurrencyLabel || ''"
      :size="UiButtonSize.SMALL"
      @click="toggleModal"
    />

    <UiHeaderModal :activator="activator" :is-open="isModalOpen" @ui-header-modal::close="closeCurrencyModal">
      <div class="Currency-modal__content">
        <button class="sr-only" @click="closeModal">
          {{ $t("ui.molecules.ui_modal.close_button") }}
        </button>

        <h5 class="Currency-modal__title">{{ $t("components.currency_modal.select_currency") }}</h5>

        <form class="Currency-modal__form">
          <UiSelect
            v-model="geographicalArea"
            :label="$t('components.currency_modal.geographical_area')"
            :options="orderedGeographicalOptions"
            required
          />
          <UiSelect
            v-model="currency"
            :default-selected="currency"
            :label="$t('components.currency_modal.currency')"
            :options="orderedCurrencyOptions"
            required
          />
        </form>

        <UiButton
          class="Currency-modal__confirm"
          type="button"
          :is-loading="loading"
          :text="$t('components.currency_modal.confirm')"
          :variation="UiButtonVariation.SECONDARY"
          @click="handleSubmitCurrencyModal"
        />
      </div>
    </UiHeaderModal>
  </div>
</template>

<style lang="scss" scoped>
@use "./CurrencyModal.scss";
</style>
