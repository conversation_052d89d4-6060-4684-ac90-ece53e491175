<script setup lang="ts">
import {
  DividerDirection,
  ModalSize,
  SpecialRates,
  SpecialRatesCodes,
  Transition,
  UiDivider,
  UiImage,
  UiLink,
  UiModal
} from "design-system"
import { useModal, useSearch } from "../../composables"
import { AllHotelRedirectionFixedParams } from "./interface"
import { computed } from "vue"
import { getGlobalConfig } from "../../global/config"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useI18n } from "vue-i18n"
import { useUserPOS } from "../../composables/useUserPOS"

const { dateIn, hotel, lengthOfStay, rooms, specialRate, promoCode } = useSearch()
const { locale } = useI18n()
const { userPOS } = useUserPOS()
const config = getGlobalConfig()

const monthIn = computed(() => ((dateIn.value?.getMonth() || 0) + 1).toString())
const yearIn = computed(() => dateIn.value?.getFullYear().toString())

const params = computed(() => {
  const body: Record<string, string | undefined> = {
    accessibility: rooms.value.some((room) => room.accessibility).toString(),
    code_langue: locale.value,
    currency: userPOS.currency,
    dayIn: dateIn.value?.getDate().toString(),
    destination: hotel.value?.id,
    monthIn: monthIn.value,
    nightNb: lengthOfStay.value.toString(),
    origin: config.siteCode,
    partner_id: config.siteCode,
    preferredCode: undefined as string | undefined,
    roomNumber: rooms.value.length.toString(),
    yearIn: yearIn.value,

    //TODO: add cookie persistency (Not included in CONVERT-174)

    ...AllHotelRedirectionFixedParams
  }

  for (const [index, room] of Object.entries(rooms.value)) {
    const roomBody = {
      [`room[${index}].adultNumber`]: room.adults,
      [`room[${index}].childrenNumber`]: room.children
    }

    for (const [subIndex, childAge] of Object.entries(room.childrenAges)) {
      roomBody[`room[${index}].childrenAge[${subIndex}]`] = childAge
    }

    Object.assign(body, roomBody)
  }

  switch (specialRate.value) {
    case SpecialRates.PROMO_CODE:
      if (promoCode.value && promoCode.value.split(",").length > 1) {
        delete body.preferredCode
      } else {
        body.preferredCode = promoCode.value
      }
      break
    case SpecialRates.AAA_CCA_MEMBER:
      body.preferredCode = SpecialRatesCodes.AAA_CCA_MEMBER
      break
    case SpecialRates.GOVERNMENT: // We do not handle multiple code in the URL for all.com redirection
      delete body.preferredCode
      break
    case SpecialRates.MILITARY_VETERAN:
      body.preferredCode = SpecialRatesCodes.MILITARY_VETERAN
      break
    case SpecialRates.SENIOR_DISCOUNT:
      body.preferredCode = SpecialRatesCodes.SENIOR_DISCOUNT
      break

    default:
      delete body.preferredCode
      break
  }

  return body
})

const allLink = computed(() => {
  const rawUrl = replaceUriParams(config.all.redirection, config.all.hostname, locale.value)
  const url = new URL(rawUrl)

  for (const [key, value] of Object.entries(params.value)) {
    if (!value) continue

    url.searchParams.set(key, value.toString())
  }

  return url.toString()
})

const { isModalOpen, closeModal } = useModal("hotel")
const onModalClosed = () => {
  closeModal()
  window.scrollTo({ behavior: "smooth", top: 0 })
}
</script>

<template>
  <UiModal
    with-overlay
    class="All-hotel-redirect-modal"
    :is-open="isModalOpen"
    :transition="Transition.FADE"
    :modal-size="ModalSize.SMALL"
    mobile-min-height
    @ui-modal::close="onModalClosed"
  >
    <div class="All-hotel-redirect-modal__container">
      <div class="All-hotel-redirect-modal__logos">
        <UiImage
          class="All-hotel-redirect-modal__brand"
          src="/booking/brandLogoBlack.svg"
          :alt="$t('components.all_hotel_redirect_modal.brand_logo', { brand: config.name })"
        />
        <UiImage
          class="All-hotel-redirect-modal__all"
          src="/booking/allLogo.svg"
          :alt="$t('components.all_hotel_redirect_modal.all_logo')"
        />
        <!--TODO: found a way to use relative link for all brands-->
      </div>
      <UiDivider :direction="DividerDirection.HORIZONTAL" max-length="800px" />
      <p class="All-hotel-redirect-modal__infos">{{ $t("components.all_hotel_redirect_modal.infos") }}</p>
      <div class="All-hotel-redirect-modal__actions">
        <UiLink
          class="All-hotel-redirect-modal__link"
          :text="$t('components.all_hotel_redirect_modal.redirect')"
          :href="allLink"
          uppercase
          external
        />
      </div>
    </div>
  </UiModal>
</template>

<style lang="scss" scoped>
@use "./AllHotelRedirectModal.scss";
</style>
