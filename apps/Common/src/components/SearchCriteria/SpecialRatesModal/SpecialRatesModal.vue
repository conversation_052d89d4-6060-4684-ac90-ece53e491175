<script setup lang="ts">
import {
  ActionAlignment,
  SPECIAL_RATES,
  SpecialRates,
  UiButton,
  UiInputStatus,
  UiMessage,
  UiMessageVariation,
  UiModal,
  UiModalHeader,
  UiSearchCriteriaButton,
  UiSearchCriteriaButtonVariant,
  UiSpecialRates
} from "design-system"
import { computed, ref } from "vue"
import { EventNameEnum } from "../../../global/enums"
import { SpecialRatesModalProps } from "./interface"
import { useGA4Event } from "../../../composables"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"
import { useStepRoutes } from "../../../composables/useStepRoutes"
import { validateSpecialRateWithPromoCode } from "../../../helpers/specialRatesValidation"

withDefaults(defineProps<SpecialRatesModalProps>(), {
  isValid: true
})

defineEmits(["SpecialRatesModal::validate"])

const { t } = useI18n()
const { pushGA4Event } = useGA4Event()
const route = useRoute()
const { currentStep } = useStepRoutes()

const isOpen = defineModel<boolean>("is-open")
const iataCode = defineModel<string>("iata-code")
const promoCode = defineModel<string>("promo-code")
const specialRate = defineModel<SpecialRates>("special-rate", {
  default: SpecialRates.NONE
})
const unvalidatedIataCode = defineModel<string>("unvalidated-iata-code")
const unvalidatedSpecialRate = ref<SpecialRates>(SpecialRates.NONE)
const unvalidatedPromoCode = ref<string>()

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const closeModal = () => {
  isOpen.value = false
}

const openModal = () => {
  pushGA4Event(`step${currentStep.value}`, currentStep.value === 1 ? "check availability" : "select a room", {
    eventName: EventNameEnum.booking_form_interact,
    event_data: {
      form_action: "openadvanced"
    }
  })

  unvalidatedSpecialRate.value = specialRate.value
  unvalidatedIataCode.value = iataCode.value
  unvalidatedPromoCode.value = promoCode.value
  isOpen.value = true
}

const handleContinue = () => {
  unvalidatedSpecialRate.value = validateSpecialRateWithPromoCode(
    unvalidatedSpecialRate.value,
    unvalidatedPromoCode.value
  )

  specialRate.value = unvalidatedSpecialRate.value

  iataCode.value = unvalidatedIataCode.value

  promoCode.value = unvalidatedPromoCode.value

  closeModal()
}

const rateLabelFormated = computed(() => {
  if (iataCode.value) {
    return t("ui.organisms.ui_special_rates.rate_items.iata_code_label")
  }

  const rateToFormat = SPECIAL_RATES.find((item) => item.selectedValue === specialRate.value)?.label

  return rateToFormat ? t(rateToFormat) : ""
})
</script>

<template>
  <div class="Special-rates-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.special_rates.dropdown.name')"
      :criteria-value="rateLabelFormated"
      :error="!isValid"
      :is-criteria-value-empty="specialRate === SpecialRates.NONE"
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      has-arrow
      @ui-search-criteria-button::click="openModal"
    />
    <UiModal :is-open="isOpen" :action-alignment="ActionAlignment.FULL">
      <template #header>
        <UiModalHeader
          has-border-bottom
          :title="$t('components.search_criteria.special_rates.section_title')"
          @ui-modal-header::click="closeModal"
        />
      </template>

      <template #default>
        <UiMessage
          v-if="(iataErrorMessage || promoErrorMessage) && displayTopErrorMessage"
          :description="iataErrorMessage ?? (promoErrorMessage as string)"
          :variation="UiMessageVariation.DANGER"
          no-radius
        />
        <UiSpecialRates
          v-model:iata-code="unvalidatedIataCode"
          v-model:rate-selected="unvalidatedSpecialRate"
          v-model:promo-code="unvalidatedPromoCode"
          :iata-error-message="iataErrorMessage"
          :iata-code-status="!!iataErrorMessage ? UiInputStatus.ERROR : undefined"
          :promo-error-message="promoErrorMessage"
          :promo-code-status="!!promoErrorMessage ? UiInputStatus.ERROR : undefined"
          global-iata-error
          @special-rates::iata-blur="$emit('SpecialRatesModal::validate')"
        />
      </template>

      <template #actions>
        <UiButton class="Destination-modal__button" :text="buttonText" @click="handleContinue" />
      </template>
    </UiModal>
  </div>
</template>
