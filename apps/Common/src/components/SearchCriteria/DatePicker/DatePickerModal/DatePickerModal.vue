<script setup lang="ts">
import {
  DateMoment,
  UiButton,
  UiButtonVariation,
  UiDatePickerMobile,
  UiIcon,
  UiMessage,
  UiMessageVariation,
  UiModal,
  UiModalHeader,
  UiSearchCriteriaButton,
  UiSearchCriteriaButtonVariant,
  useDatePicker
} from "design-system"
import { computed, ref, useTemplateRef } from "vue"
import { differenceInCalendarDays, isSameDay } from "date-fns"
import { DatePickerModalProps } from "./interface"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"

defineProps<DatePickerModalProps>()

const emits = defineEmits([
  "DatePickerModal:closeModal",
  "DatePickerModal:isValid",
  "DatePickerModal:change-start-date",
  "DatePickerModal:change-end-date"
])

const dates = defineModel<Date[]>("dates", {
  default: () => []
})
const currentDates = defineModel<Date[]>("current-dates")
const isOpen = defineModel<boolean>("is-open")

const { computedDate } = useDatePicker()
const { t } = useI18n()
const route = useRoute()

const datePicker = useTemplateRef("date-picker")
const defaultDateSelected = ref<DateMoment>(DateMoment.START)

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const dateChanged = computed(() => {
  let changedStart = true
  let changedEnd = true

  if (currentDates.value?.[DateMoment.START] && dates.value?.[DateMoment.START]) {
    changedStart = !isSameDay(currentDates.value[DateMoment.START], dates.value[DateMoment.START])
  }

  if (currentDates.value?.[DateMoment.END] && dates.value?.[DateMoment.END]) {
    changedEnd = !isSameDay(currentDates.value[DateMoment.END], dates.value[DateMoment.END])
  }

  return changedStart || changedEnd
})

const closeModal = (resetDates: boolean = true) => {
  defaultDateSelected.value = DateMoment.START
  isOpen.value = false
  emits("DatePickerModal:closeModal")

  if (resetDates && dateChanged.value) {
    currentDates.value = [...dates.value]
  }
}

const nightsCount = computed(() => {
  if (!currentDates.value?.[DateMoment.START] || !currentDates.value?.[DateMoment.END]) return 0

  return differenceInCalendarDays(currentDates.value?.[DateMoment.END], currentDates.value?.[DateMoment.START])
})

const clearDates = () => {
  currentDates.value = []
  datePicker.value?.clearDates()
}

const handleContinue = () => {
  if (currentDates?.value?.[DateMoment.START] && currentDates?.value[DateMoment.END] && dateChanged.value) {
    dates.value = [...currentDates.value]
  } else if (!currentDates?.value?.[DateMoment.START] || !currentDates?.value[DateMoment.END]) {
    dates.value = []
  }

  closeModal(false)
}

const startDateLabel = computed(() =>
  dates.value?.[DateMoment.START]
    ? computedDate(dates.value[DateMoment.START])
    : t("ui.organisms.ui_date_picker_desktop.example")
)
const endDateLabel = computed(() =>
  dates.value?.[DateMoment.END]
    ? computedDate(dates.value[DateMoment.END])
    : t("ui.organisms.ui_date_picker_desktop.example")
)
</script>

<template>
  <div class="Date-picker-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.date_picker.title')"
      has-arrow
      :is-criteria-value-empty="!dates || !dates.length"
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      :error="!isValid"
      @ui-search-criteria-button::click="isOpen = true"
    >
      <template #criteria-value>
        <UiButton
          class="Date-picker-modal__button"
          :aria-label="$t('components.search_criteria.date_picker.edit_start_date')"
          :variation="UiButtonVariation.PLAIN"
          :text="startDateLabel || ''"
          @click="defaultDateSelected = DateMoment.START"
        />
        <UiIcon class="Date-picker-modal__arrow" name="arrow" />
        <UiButton
          class="Date-picker-modal__button"
          :aria-label="$t('components.search_criteria.date_picker.edit_end_date')"
          :variation="UiButtonVariation.PLAIN"
          :text="endDateLabel || ''"
          @click="defaultDateSelected = DateMoment.END"
        />
      </template>
    </UiSearchCriteriaButton>
    <UiModal :is-open="isOpen">
      <template #header>
        <UiModalHeader
          :title="$t('components.search_criteria.date_picker.choose_your_dates')"
          @ui-modal-header::click="closeModal"
        />
      </template>

      <UiMessage
        v-if="errorMessage"
        :description="errorMessage"
        :variation="UiMessageVariation.DANGER"
        no-radius
        class="my-4"
      />

      <UiDatePickerMobile
        ref="date-picker"
        v-model="currentDates"
        :max-date="maxDate"
        :max-range="maxRange"
        :min-range="minRange"
        :default-date-selected="defaultDateSelected"
        @ui-date-picker-mobile:change-start-date="$emit('DatePickerModal:change-start-date')"
        @ui-date-picker-mobile:change-end-date="$emit('DatePickerModal:change-end-date')"
      />

      <template #actions>
        <div class="Date-picker-modal__action-row">
          <p v-if="nightsCount" class="Date-picker-modal__nights">
            {{ $t("ui.organisms.ui_date_picker_mobile.nights", { count: nightsCount }) }}
          </p>
          <button class="Date-picker-modal__clear" type="button" :disabled="nightsCount === 0" @click="clearDates">
            {{ $t("ui.organisms.ui_date_picker_mobile.clear") }}
          </button>
        </div>
        <UiButton type="button" :disabled="currentDates?.length !== 2" :text="buttonText" @click="handleContinue" />
      </template>
    </UiModal>
  </div>
</template>

<style lang="scss" scoped>
@use "./DatePickerModal.scss";
</style>
