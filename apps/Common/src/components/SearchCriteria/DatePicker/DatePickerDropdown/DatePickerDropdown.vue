<script setup lang="ts">
import { DateMoment, DatePickerVariant, UiDatePickerDesktop } from "design-system"
import { DatePickerDropdownProps } from "./interface"
import { ref } from "vue"

withDefaults(defineProps<DatePickerDropdownProps>(), {
  variant: DatePickerVariant.DEFAULT
})

const emit = defineEmits([
  "DatePickerDropdown::change-start-date",
  "DatePickerDropdown::change-end-date",
  "DatePickerDropdown::clear-dates"
])

const dates = defineModel<Date[]>()
const uiDatePickerDesktop = ref()

const handleDatesChange = (newDates: Date[]) => {
  dates.value = newDates
}

const openDropdownModal = (moment: DateMoment) => {
  uiDatePickerDesktop.value.openDatePickerDropdown(moment)
}

const handleClear = () => {
  dates.value = []
  emit("DatePickerDropdown::clear-dates")
}

defineExpose({ openDropdownModal })
</script>

<template>
  <div class="Date-picker-desktop">
    <UiDatePickerDesktop
      ref="uiDatePickerDesktop"
      :dates="dates"
      :variant="variant"
      :max-date="maxDate"
      :max-range="maxRange"
      :min-range="minRange"
      :error-message="errorMessage"
      :start-date-error="!inputsAreValid?.startDate"
      :end-date-error="!inputsAreValid?.endDate"
      @update:dates="handleDatesChange"
      @ui-date-picker-desktop::clear="handleClear"
      @ui-date-picker-desktop::change-start-date="$emit('DatePickerDropdown::change-start-date', $event)"
      @ui-date-picker-desktop::change-end-date="$emit('DatePickerDropdown::change-end-date', $event)"
    />
  </div>
</template>
