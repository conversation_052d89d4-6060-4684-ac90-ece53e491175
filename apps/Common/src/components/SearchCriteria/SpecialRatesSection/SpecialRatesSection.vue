<script setup lang="ts">
import { SpecialRates, UiInputStatus, UiSection, UiSpecialRates } from "design-system"
import { SpecialRatesSectionProps } from "./interface"

withDefaults(defineProps<SpecialRatesSectionProps>(), {
  isValid: true
})

defineEmits(["SpecialRatesSection::validate"])

const specialRate = defineModel<SpecialRates>("special-rate")
const promoCode = defineModel<string>("promo-code")
const iataCode = defineModel<string>("iata-code")
</script>

<template>
  <div>
    <UiSection class="Special-rates-section" :title="$t('components.search_criteria.special_rates.section_title')">
      <template #content>
        <UiSpecialRates
          v-model:iata-code="iataCode"
          v-model:rate-selected="specialRate as SpecialRates"
          v-model:promo-code="promoCode"
          :iata-error-message="iataErrorMessage"
          :iata-code-status="!!iataErrorMessage ? UiInputStatus.ERROR : undefined"
          :promo-error-message="promoErrorMessage"
          :promo-code-status="!!promoErrorMessage ? UiInputStatus.ERROR : undefined"
          @special-rates::iata-blur="$emit('SpecialRatesSection::validate')"
        />
      </template>
    </UiSection>
  </div>
</template>
<style lang="scss" scoped>
@use "./SpecialRateSection.scss";
</style>
