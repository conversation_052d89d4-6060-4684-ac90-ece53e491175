<script setup lang="ts">
import {
  SPECIAL_RATES,
  SpecialRates,
  UiDropdownModal,
  UiInputStatus,
  UiMessage,
  UiMessageVariation,
  UiSearchCriteriaButton,
  UiSpecialRates
} from "design-system"
import { EventNameEnum } from "../../../global/enums"
import { SpecialRatesDropdownProps } from "./interface"
import { computed } from "vue"
import { useGA4Event } from "../../../composables"
import { useI18n } from "vue-i18n"

withDefaults(defineProps<SpecialRatesDropdownProps>(), {
  isValid: true
})

const { t } = useI18n()
const { pushGA4Event } = useGA4Event()
const emit = defineEmits(["SpecialRatesDropdown::validate", "SpecialRatesDropdown::close"])

const specialRate = defineModel<SpecialRates>("special-rate")
const promoCode = defineModel<string>("promo-code")
const iataCode = defineModel<string>("iata-code")
const criteriaOpen = defineModel<boolean>("is-open", { default: false })

const rateLabelFormated = computed(() => {
  if (iataCode.value) {
    return t("ui.organisms.ui_special_rates.rate_items.iata_code_label")
  }

  const rateToFormat = SPECIAL_RATES.find((item) => item.selectedValue === specialRate.value)?.label
  return rateToFormat ? t(rateToFormat) : ""
})

const openDropdownModal = () => {
  criteriaOpen.value = true
}

const openCriteriaDropdown = () => {
  pushGA4Event(`step2`, "select a room", {
    eventName: EventNameEnum.booking_form_interact,
    event_data: {
      form_action: "openadvanced"
    }
  })

  criteriaOpen.value = !criteriaOpen.value
}

const handleDropdownClose = () => {
  criteriaOpen.value = false
  emit("SpecialRatesDropdown::close")
}

defineExpose({ openDropdownModal })
</script>

<template>
  <div>
    <UiDropdownModal :is-open="criteriaOpen" @ui-dropdown-modal::close="handleDropdownClose">
      <template #activator>
        <UiSearchCriteriaButton
          :criteria-name="$t('components.search_criteria.special_rates.dropdown.name')"
          :criteria-value="rateLabelFormated"
          :is-criteria-value-empty="specialRate === SpecialRates.NONE"
          :is-criteria-dropdown-modal-open="criteriaOpen"
          :error="!isValid"
          @ui-search-criteria-button::click="openCriteriaDropdown"
        />
      </template>
      <template #content>
        <UiMessage
          v-if="(iataErrorMessage || promoErrorMessage) && displayTopErrorMessage"
          :description="iataErrorMessage ?? (promoErrorMessage as string)"
          :variation="UiMessageVariation.DANGER"
          no-radius
        />
        <UiSpecialRates
          v-model:iata-code="iataCode"
          v-model:rate-selected="specialRate as SpecialRates"
          v-model:promo-code="promoCode"
          :iata-code-status="!!iataErrorMessage ? UiInputStatus.ERROR : undefined"
          :promo-code-status="!!promoErrorMessage ? UiInputStatus.ERROR : undefined"
          :promo-error-message="promoErrorMessage"
          :iata-error-message="iataErrorMessage"
          is-dropdown
          is-desktop
          @special-rates::rate-selected="specialRate = $event"
          @special-rates::iata-blur="$emit('SpecialRatesDropdown::validate')"
          @special-rates::iata-updated="iataCode = $event"
          @special-rates::promo-updated="promoCode = $event"
        />
      </template>
    </UiDropdownModal>
  </div>
</template>
