<script setup lang="ts">
import { DefaultRoomDetails, UiRooms, UiSection } from "design-system"
import { useGA4Event, useSearch } from "../../../../composables"
import { EventNameEnum } from "../../../../global/enums"
import { RoomsAndGuestsSectionProps } from "./interface"
import { UnwrapRef } from "vue"
import { useI18n } from "vue-i18n"

defineProps<RoomsAndGuestsSectionProps>()
const { pushGA4Event } = useGA4Event()
defineEmits(["RoomsAndGuestsSection::validate-children-age", "RoomsAndGuestsSection::validate-error-messages"])

const { rooms } = useSearch()

const { t } = useI18n()

const updateRooms = (newRooms: UnwrapRef<typeof rooms>) => {
  rooms.value = newRooms
}

const addRoom = (id: number) => {
  pushGA4Event("step1", "check availability", {
    eventName: EventNameEnum.booking_form_interact,
    event_data: {
      form_action: "add room"
    }
  })

  rooms.value = [
    ...rooms.value,
    {
      id,
      ...DefaultRoomDetails
    }
  ]
}
</script>

<template>
  <UiSection :title="$t('components.search_criteria.rooms_and_guests.section_title')">
    <template #content>
      <UiRooms
        :rooms="rooms"
        :max-rooms="maxRooms"
        :max-pax="maxPax"
        :max-adult="maxAdult"
        :max-child="maxChild"
        :max-child-age="maxChildAge"
        :error-message="t('errors.child_age')"
        :rooms-and-guests-in-error="roomsAndGuestsInError"
        @ui-rooms::update="updateRooms"
        @ui-rooms::add="addRoom"
        @ui-rooms::validate-children-age="$emit('RoomsAndGuestsSection::validate-children-age', $event)"
      />
    </template>
  </UiSection>
</template>
