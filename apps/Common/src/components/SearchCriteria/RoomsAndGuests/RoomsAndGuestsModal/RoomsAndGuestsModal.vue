<script setup lang="ts">
import {
  ActionAlignment,
  DefaultRoomDetails,
  RoomType,
  RoomsDirection,
  UiButton,
  UiModal,
  UiModalHeader,
  UiRoomSelectDefault,
  UiRooms,
  UiSearchCriteriaButton,
  UiSearchCriteriaButtonVariant
} from "design-system"
import { computed, ref } from "vue"
import { EventNameEnum } from "../../../../global/enums"
import { RoomsAndGuestsModalProps } from "./interface"
import { useGA4Event } from "../../../../composables"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"
import { useStepRoutes } from "../../../../composables/useStepRoutes"

defineProps<RoomsAndGuestsModalProps>()

defineEmits(["RoomsAndGuestsModal::validate-children-age"])

const { t } = useI18n()
const { pushGA4Event } = useGA4Event()
const route = useRoute()
const { currentStep } = useStepRoutes()

const rooms = defineModel<RoomType[]>("rooms")

const temporaryRooms = ref<RoomType[]>(
  rooms.value || [
    {
      ...DefaultRoomDetails,
      id: 0
    }
  ]
)
const isOpen = defineModel<boolean>("is-open")

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const temporaryRoomsAndGuestsInError = computed(() => {
  const errorIndexes: number[] = []

  temporaryRooms.value?.forEach((room, index) => {
    if (room.dropdownsInError?.length > 0) {
      errorIndexes.push(index)
    }
  })

  return errorIndexes
})

const criteriaValue = computed(() => {
  const roomsNumber = rooms.value?.length
  const guestsNumbers = rooms.value?.reduce((acc: number, room: RoomType) => acc + room.adults + room.children, 0)

  return `${t("components.search_criteria.rooms_and_guests.button_value_rooms", { count: roomsNumber })} - ${t("components.search_criteria.rooms_and_guests.button_value_guests", { count: guestsNumbers })}`
})

const closeModal = () => {
  isOpen.value = false
}

const openModal = () => {
  isOpen.value = true
  // Reset working value on modal open
  temporaryRooms.value = JSON.parse(JSON.stringify(rooms.value))
}

const addRoom = (id: number) => {
  pushGA4Event(`step${currentStep.value}`, currentStep.value === 1 ? "check availability" : "select a room", {
    eventName: EventNameEnum.booking_form_submit,
    event_data: {
      form_action: "add room"
    }
  })

  temporaryRooms.value?.push({
    ...DefaultRoomDetails,
    id
  })
}

const singleDropdownValidation = (dropdown: { roomIndex: number; dropdownIndex: number }) => {
  if (temporaryRooms.value[dropdown.roomIndex].childrenAges[dropdown.dropdownIndex] === UiRoomSelectDefault) {
    if (temporaryRooms.value[dropdown.roomIndex].dropdownsInError) {
      temporaryRooms.value[dropdown.roomIndex]?.dropdownsInError?.push(dropdown.dropdownIndex)
    } else {
      temporaryRooms.value[dropdown.roomIndex].dropdownsInError = [dropdown.dropdownIndex]
    }
  }

  // if dropdown and not the default value, we have to filter the index out of the array
  else {
    const filteredErrors = temporaryRooms.value[dropdown.roomIndex].dropdownsInError?.filter(
      (item) => item !== dropdown.dropdownIndex
    )
    temporaryRooms.value[dropdown.roomIndex].dropdownsInError = filteredErrors

    // if there's no more dropdowns in error, we can remove the error message from the room
    if (!temporaryRooms.value[dropdown.roomIndex].dropdownsInError?.length) {
      temporaryRooms.value[dropdown.roomIndex].hasErrorMessage = false
    }
  }
}

const allDropdownsValidation = () => {
  temporaryRooms.value?.forEach((room) => {
    const roomDropdownsInError: number[] = []

    room.childrenAges.forEach((age, index) => {
      if (age === UiRoomSelectDefault) {
        roomDropdownsInError.push(index)
      }
    })
    room.dropdownsInError = [...new Set(roomDropdownsInError)]
  })
}

const handleContinue = () => {
  allDropdownsValidation()

  if (temporaryRoomsAndGuestsInError.value.length === 0) {
    rooms.value = temporaryRooms.value

    closeModal()
  }
}

defineExpose({
  openModal
})
</script>

<template>
  <div class="Rooms-and-guests-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.rooms_and_guests.dropdown.name')"
      :criteria-value="criteriaValue"
      :error="!isValid"
      has-arrow
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      @ui-search-criteria-button::click="openModal"
    />
    <UiModal :is-open="isOpen" :action-alignment="ActionAlignment.FULL">
      <template #header>
        <UiModalHeader
          has-border-bottom
          :title="$t('components.search_criteria.rooms_and_guests.section_title')"
          @ui-modal-header::click="closeModal"
        />
      </template>

      <UiRooms
        :direction="RoomsDirection.VERTICAL"
        :rooms="temporaryRooms as RoomType[]"
        :rooms-and-guests-in-error="temporaryRoomsAndGuestsInError"
        :error-message="t('errors.child_age')"
        :max-room="maxRooms"
        :max-pax="maxPax"
        :max-adult="maxAdult"
        :max-child="maxChild"
        :max-child-age="maxChildAge"
        @ui-rooms::update="temporaryRooms = $event"
        @ui-rooms::add="addRoom"
        @ui-rooms::validate-children-age="singleDropdownValidation($event)"
      />

      <template #actions>
        <UiButton :text="buttonText" @click="handleContinue" />
      </template>
    </UiModal>
  </div>
</template>
