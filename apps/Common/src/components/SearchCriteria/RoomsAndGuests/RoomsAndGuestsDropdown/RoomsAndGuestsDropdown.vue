<script setup lang="ts">
import {
  DefaultRoomDetails,
  RoomType,
  RoomsDirection,
  UiDropdownModal,
  UiRooms,
  UiSearchCriteriaButton
} from "design-system"
import { computed, ref } from "vue"
import { EventNameEnum } from "../../../../global/enums"
import { RoomsAndGuestsDropdownProps } from "./interface"
import { useGA4Event } from "../../../../composables"
import { useI18n } from "vue-i18n"

defineProps<RoomsAndGuestsDropdownProps>()

const model = defineModel<RoomType[]>({
  required: true
})

defineEmits(["RoomsAndGuestsDropdown::validate-children-age", "RoomsAndGuestsDropdown::validate-error-messages"])

const { t } = useI18n()
const { pushGA4Event } = useGA4Event()

const addRoom = (id: number) => {
  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.booking_form_interact,
    event_data: {
      form_action: "add room"
    }
  })

  model.value.push({
    ...DefaultRoomDetails,
    id
  })
}

const updateRooms = (rooms: RoomType[]) => {
  model.value = rooms
}

const isOpen = ref(false)

const computedValue = computed(() => {
  const roomsNumber = model.value.length
  const guestsNumbers = model.value.reduce((acc: number, room: RoomType) => acc + room.adults + room.children, 0)

  return `${t("components.search_criteria.rooms_and_guests.button_value_rooms", { count: roomsNumber })} - ${t("components.search_criteria.rooms_and_guests.button_value_guests", { count: guestsNumbers })}`
})

const openModal = () => {
  isOpen.value = true
}

defineExpose({
  openModal
})
</script>

<template>
  <UiDropdownModal :is-open="isOpen" @ui-dropdown-modal::close="isOpen = false">
    <template #activator>
      <UiSearchCriteriaButton
        :criteria-name="$t('components.search_criteria.rooms_and_guests.dropdown.name')"
        :criteria-value="computedValue"
        :error="!!roomsAndGuestsInError?.length"
        :is-criteria-dropdown-modal-open="isOpen"
        @ui-search-criteria-button::click="isOpen = !isOpen"
      />
    </template>
    <template #content>
      <UiRooms
        :direction="RoomsDirection.VERTICAL"
        :max-adult="maxAdult"
        :max-child-age="maxChildAge"
        :max-child="maxChild"
        :max-pax="maxPax"
        :max-rooms="maxRooms"
        :rooms="modelValue"
        :rooms-and-guests-in-error="roomsAndGuestsInError"
        :error-message="t('errors.child_age')"
        @ui-rooms::update="updateRooms($event)"
        @ui-rooms::add="addRoom($event)"
        @ui-rooms::validate-children-age="$emit('RoomsAndGuestsDropdown::validate-children-age', $event)"
        @ui-rooms::remove="$emit('RoomsAndGuestsDropdown::validate-error-messages', $event)"
      />
    </template>
  </UiDropdownModal>
</template>
