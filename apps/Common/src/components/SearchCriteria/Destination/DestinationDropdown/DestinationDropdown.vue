<script setup lang="ts">
import { Destination, DestinationVariant, UiDestinationDesktop } from "design-system"
import type { DestinationDropdownProps } from "./interface"
import { ref } from "vue"

withDefaults(defineProps<DestinationDropdownProps>(), {
  isValid: true,
  variant: DestinationVariant.DEFAULT
})

const hotel = defineModel<Destination>("hotel")
const inputValue = defineModel<string | undefined>("input-filter")
const dropdownIsOpen = defineModel<boolean>("dropdown-is-open")

const destinationDropdown = ref()
</script>

<template>
  <div class="Destination-dropdown">
    <UiDestinationDesktop
      ref="destinationDropdown"
      v-model:destination="hotel"
      v-model:input-value="inputValue"
      v-model:is-open="dropdownIsOpen"
      :error-border="!isValid"
      :error-message="errorMessage"
      :is-loading="isLoading"
      :items="items"
      :variant="variant"
    />
  </div>
</template>
