<script setup lang="ts">
import {
  Destination,
  UiButton,
  UiDestinationMobile,
  UiModal,
  UiModalHeader,
  UiSearchCriteriaButton,
  UiSearchCriteriaButtonVariant
} from "design-system"
import { computed, ref } from "vue"
import { DestinationModalProps } from "./interface"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"

withDefaults(defineProps<DestinationModalProps>(), {
  isValid: true
})

const emit = defineEmits(["DestinationModal::check"])

const route = useRoute()

const hotel = defineModel<Destination | undefined>("hotel")
const inputValue = defineModel<string | undefined>("input-filter")
const isOpen = defineModel<boolean>("is-open")
const selectedHotel = ref<Destination | undefined>()

const { t } = useI18n()

// Returns the selected hotel name based on the store or a default trad
const selectedHotelName = computed(() => {
  return hotel.value?.name ?? t("ui.organisms.ui_destination.select_your_destination")
})

// On search input update, we check the string entered by the user.
// If the string matches an hotel name, we can allow the search validation.
const inputHotelNameIsValid = computed(() => selectedHotel.value?.name === inputValue.value)

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const closeModal = () => {
  isOpen.value = false
}

const openModal = () => {
  // In the case the user changed the input before using the "back to your search button" and reopens the modal,
  // we reload the current hotel.
  selectedHotel.value = hotel.value
  isOpen.value = true
}

const handleContinue = () => {
  hotel.value = selectedHotel.value
  emit("DestinationModal::check")
  closeModal()
}

const handleUpdate = (newHotel: Destination) => {
  selectedHotel.value = newHotel

  if (!hotel.value) {
    handleContinue()
  }
}
</script>

<template>
  <div class="Destination-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.destination.destination')"
      :criteria-value="selectedHotelName"
      :error="!isValid"
      has-arrow
      :is-criteria-value-empty="!hotel"
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      @ui-search-criteria-button::click="openModal"
    />
    <UiModal :is-open="isOpen" @ui-modal::close="closeModal">
      <template #header>
        <UiModalHeader
          :title="$t('ui.organisms.ui_destination.select_your_destination')"
          @ui-modal-header::click="closeModal"
        />
      </template>
      <UiDestinationMobile
        v-model:selected-hotel="selectedHotel"
        v-model:input-value="inputValue"
        :error-message="errorMessage"
        :is-loading="isLoading"
        :items="items"
        :selected-value-id="hotel?.id"
        :selected-value-name="hotel?.name"
        @ui-destination-mobile::update-hotel="handleUpdate"
      />

      <template v-if="hotel" #actions>
        <UiButton
          class="Destination-modal__button"
          :text="buttonText"
          :disabled="!inputHotelNameIsValid"
          @click="handleContinue"
        />
      </template>
    </UiModal>
  </div>
</template>

<style lang="scss" scoped>
@use "./DestinationModal.scss";
</style>
