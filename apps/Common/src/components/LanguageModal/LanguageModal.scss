@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Language-modal {
  display: flex;
  align-items: center;

  &__button {
    display: flex;
    align-items: center;

    :deep(.ads-button) {
      @include text.lbf-text("caption-01-uppercase");
      min-height: unset;
      padding: 0;

      &:focus-visible::after {
        border-color: map.get(colors.$basics, "white");
        border: map.get(boxes.$borders, "interactiveSelected");
      }
    }
  }

  :deep(.ads-input) {
    .ads-input__input {
      height: map.get(spaces.$sizes, "10");
      background: none;

      .ads-select__select {
        height: map.get(spaces.$sizes, "10");
        // we set both instead of py to override the default value
        padding-block-start: map.get(spaces.$sizes, "5");
        padding-block-end: map.get(spaces.$sizes, "5");
      }
    }

    .ads-input__append-inner-icon {
      padding-inline-end: 1.4rem;

      .ads-icon {
        height: 1.8rem;
        width: 1.8rem;
      }
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  &__title {
    @include text.lbf-text("exp-heading-05");
    padding-block-end: 5.6rem;
    text-align: center;

    color: map.get(colors.$neutral, "900");

    @include mq.media(">=small") {
      padding-block-end: map.get(spaces.$sizes, "10");
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      width: 35.9rem;
    }
  }

  &__confirm {
    width: 100%;
    padding-block-start: map.get(spaces.$sizes, "8");

    @include mq.media(">=small") {
      width: auto;

      padding-block-start: map.get(spaces.$sizes, "10");
    }
  }
}
