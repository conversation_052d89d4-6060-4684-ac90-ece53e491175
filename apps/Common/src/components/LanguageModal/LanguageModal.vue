<script setup lang="ts">
import { UiButton, UiButtonSize, UiButtonVariation, UiHeaderModal, UiSelect } from "design-system"
import { nextTick, ref, useTemplateRef, watch } from "vue"
import { useLoader, useModal, usePosCurrencyHandler, usePosLanguageHandler } from "../../composables"
import { LanguageModalProps } from "./interface"
import { Option } from "@accor/ads-components"
import { moveToTop } from "../../helpers"
import { updateLanguageCookies } from "../../services/pos/pos.fetch"
import { useI18n } from "vue-i18n"
import { usePosStore } from "@stores/pos/pos"
import { useUserPOS } from "../../composables/useUserPOS"

const { locale } = useI18n()
const { loading } = useLoader("language")
const { isModalOpen, closeModal, toggleModal } = useModal("language")
const { resetStateCurrency } = usePosCurrencyHandler()
const { countryRegionLabel, handleLanguageChange, resetState } = usePosLanguageHandler()
const { findCountryObjectByAccorCountryCode } = usePosStore()
const activator = useTemplateRef<HTMLButtonElement>("activator")
const { refreshPOS } = useUserPOS()

const emits = defineEmits(["close:languageModal"])
const props = defineProps<LanguageModalProps>()

const geographicalArea = defineModel<string>("geographicalArea")
const country = defineModel<string>("country")

const closeLanguageModal = () => {
  closeModal()
  emits("close:languageModal")
}

async function handleSubmitLanguageModal() {
  loading.value = true

  const country = findCountryObjectByAccorCountryCode(countryRegionLabel.value)

  if (!country) return

  await updateLanguageCookies(country)
  refreshPOS()
  resetState()
  await handleLanguageChange()
  resetStateCurrency()

  loading.value = false

  closeModal()
}

const orderedGeographicalOptions = ref(
  moveToTop(
    props.geographicalOptions,
    props.geographicalOptions.find((option) => option.value === geographicalArea.value)
  ) as Option[]
)

const orderedCountryOptions = ref(
  moveToTop(
    props.countryOptions,
    props.countryOptions.find((option) => option.value === country.value)
  ) as Option[]
)

watch(
  () => geographicalArea.value,
  async () => {
    await nextTick(() => {
      orderedGeographicalOptions.value = moveToTop(
        props.geographicalOptions,
        props.geographicalOptions.find((option) => option.value === geographicalArea.value)
      ) as Option[]
    })
  }
)

watch(
  () => country.value,
  async () => {
    await nextTick(() => {
      orderedCountryOptions.value = moveToTop(
        props.countryOptions,
        props.countryOptions.find((option) => option.value === country.value)
      ) as Option[]
    })
  }
)
</script>

<template>
  <div class="Language-modal">
    <UiButton
      ref="activator"
      class="Language-modal__button"
      :text="locale"
      :size="UiButtonSize.SMALL"
      @click="toggleModal"
    />

    <UiHeaderModal :activator="activator" :is-open="isModalOpen" @ui-header-modal::close="closeLanguageModal">
      <div class="Language-modal__content">
        <button class="sr-only" @click="closeModal">
          {{ $t("ui.molecules.ui_modal.close_button") }}
        </button>

        <h5 class="Language-modal__title">{{ $t("components.language_modal.select_language") }}</h5>

        <form class="Language-modal__form">
          <UiSelect
            v-model="geographicalArea"
            :default-selected="geographicalArea"
            :label="$t('components.language_modal.geographical_area')"
            :options="orderedGeographicalOptions"
            required
          />

          <UiSelect
            v-model="country"
            :default-selected="country"
            :label="$t('components.language_modal.language')"
            :options="orderedCountryOptions"
            required
          />
        </form>

        <UiButton
          class="Language-modal__confirm"
          type="button"
          :is-loading="loading"
          :text="$t('components.language_modal.confirm')"
          :variation="UiButtonVariation.SECONDARY"
          @click="handleSubmitLanguageModal"
        />
      </div>
    </UiHeaderModal>
  </div>
</template>

<style lang="scss" scoped>
@use "./LanguageModal.scss";
</style>
