<script setup lang="ts">
import {
  DateMoment,
  Destination,
  RoomType,
  SpecialRates,
  UiButton,
  UiRoomSelectDefault,
  filterDestinationsMobile
} from "design-system"
import { computed, ref, watch } from "vue"
import {
  datePickerValidation,
  destinationValidation,
  iataSchema,
  useGA4Event,
  useIdentificationPlainText,
  useLoader,
  useSearch,
  useZodValidation
} from "../../../composables"
import DatePickerModal from "../../SearchCriteria/DatePicker/DatePickerModal/DatePickerModal.vue"
import DestinationModal from "../../SearchCriteria/Destination/DestinationModal/DestinationModal.vue"
import { EventNameEnum } from "../../../global/enums"
import RoomsAndGuestsModal from "../../SearchCriteria/RoomsAndGuests/RoomsAndGuestsModal/RoomsAndGuestsModal.vue"
import { SearchEngineMobileProps } from "./interface"
import SpecialRatesModal from "../../SearchCriteria/SpecialRatesModal/SpecialRatesModal.vue"
import { mapApiHotelsToDestinationMobile } from "../../../services/hotels/hotels.mapper"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useSearchValidation } from "../../../composables/useSearchValidation"
import { useStepRoutes } from "../../../composables/useStepRoutes"
import { validateSpecialRateWithPromoCode } from "../../../helpers/specialRatesValidation"
import { watchDebounced } from "@vueuse/core"

withDefaults(defineProps<SearchEngineMobileProps>(), {
  hasCheckRatesButton: true
})

const emit = defineEmits(["SearchEngineMobile::check"])

const { t } = useI18n()
const { refreshIdentification } = useIdentificationPlainText()
const { hotelsList } = useHotelsStore()
const { promoCodeIsValid } = useSearch()
const { loading } = useLoader("search")
const { pushGA4Event } = useGA4Event()
const { currentStep } = useStepRoutes()

// Models
// Destination
const hotelModel = defineModel<Destination | undefined>("hotel")

// Date picker
const datesModel = defineModel<Date[]>("dates")

// Rooms and guests
const roomsModel = defineModel<RoomType[]>("rooms")

// Special rates
const iataCodeModel = defineModel<string>("iata-code")
const promoCodeModel = defineModel<string>("promo-code")
const specialRateModel = defineModel<SpecialRates>("special-rate", {
  default: SpecialRates.NONE
})

// Refs
// Destination
const destinationModalIsOpen = ref(false)
const destinationErrorMessage = ref<string | undefined>()
const destinationInput = ref<string>(hotelModel.value?.name || "")

// Date picker
const datePickerModalIsOpen = ref(false)
const datePickerModalIsValid = ref(true)
const datePickerErrorMessage = ref<string | undefined>()
const currentDatesModel = ref([...(datesModel.value || [])])

// Rooms and guests
const roomsAndGuestsInError = computed(() => {
  const errorIndexes: number[] = []
  roomsModel.value?.forEach((room, index) => {
    if (room.hasErrorMessage) {
      errorIndexes.push(index)
    }
  })
  return errorIndexes
})

const roomsAndGuestsModalIsOpen = ref(false)

// Special rates
const displaySpecialRatesTopErrorMessage = ref(false)
const specialRatesModalIsOpen = ref(false)
const specialRatesModalIsValid = ref(true)

const iataCodeErrorMessage = ref<string | undefined>()
const promoCodeErrorMessage = ref<string>()

const unvalidatedIataCode = ref<string>()

const mappedHotels = computed(() => mapApiHotelsToDestinationMobile(hotelsList))

const destinationModalIsValid = computed(() => {
  return !destinationErrorMessage.value
})

// Removes the destinations that don't match the input value
const filteredDestinations = computed(() => {
  return filterDestinationsMobile(mappedHotels.value, destinationInput)
})

// Removes the destinations that don't match the input value
// and returns the full list of destinations if there's no destinations left after the filter
const filteredDestinationsFallback = computed(() => {
  return filteredDestinations.value.length === 0 ? mappedHotels.value : filteredDestinations.value
})

const roomsAndGuestsValidation = () => {
  roomsAndGuestsErrorMessageValidation(roomsModel.value as RoomType[])
}

const destinationErrorMessageValidation = (allowEmptyString?: boolean) => {
  destinationErrorMessage.value = destinationValidation(
    destinationInput.value,
    filteredDestinations.value,
    allowEmptyString
  )
}

const datePickerErrorMessageValidation = (allowEmptyDates?: boolean) => {
  datePickerErrorMessage.value = datePickerValidation(
    currentDatesModel.value[DateMoment.START]?.toString(),
    currentDatesModel.value[DateMoment.END]?.toString(),
    allowEmptyDates
  )

  if (!datePickerErrorMessage.value) {
    datePickerModalIsValid.value = true
  }
}

const validateDatePickerModal = () => {
  datePickerErrorMessageValidation()
  if (datePickerErrorMessage.value) {
    datePickerModalIsValid.value = false
  }
}

const roomsAndGuestsErrorMessageValidation = (rooms: RoomType[]) => {
  rooms?.forEach((room) => {
    const roomDropdownsInError: number[] = []
    let dropdownHasError = false

    room.childrenAges.forEach((age, index) => {
      if (age === UiRoomSelectDefault) {
        room.hasErrorMessage = true
        dropdownHasError = true
        roomDropdownsInError.push(index)
      }
    })

    room.dropdownsInError = [...new Set(roomDropdownsInError)]
    room.hasErrorMessage = dropdownHasError
  })
}

const specialRatesErrorMessageValidation = (ignoreTopErrorMessage?: boolean) => {
  iataCodeErrorMessage.value = undefined
  promoCodeErrorMessage.value = undefined
  displaySpecialRatesTopErrorMessage.value = false

  const validatedSchema = useZodValidation(unvalidatedIataCode.value, iataSchema)

  if (!validatedSchema.success) {
    const error = validatedSchema.error.format()
    iataCodeErrorMessage.value = error._errors[0]

    if (!ignoreTopErrorMessage) {
      displaySpecialRatesTopErrorMessage.value = true
    }
  }

  if (!promoCodeIsValid.value) {
    if (!ignoreTopErrorMessage) {
      displaySpecialRatesTopErrorMessage.value = true
    }
    promoCodeErrorMessage.value = t("errors.invalid_promo_code")
  }

  if (validatedSchema.success && promoCodeIsValid.value) {
    specialRatesModalIsValid.value = true
  }
}

const validateSpecialRatesModal = () => {
  specialRatesErrorMessageValidation()
  specialRatesModalIsValid.value = !iataCodeErrorMessage.value && !promoCodeErrorMessage.value
}

const handleSubmit = async () => {
  await refreshIdentification()

  destinationErrorMessageValidation()
  validateDatePickerModal()
  roomsAndGuestsValidation()
  validateSpecialRatesModal()

  const { errorFields, firstError, errorFieldsString } = useSearchValidation({
    datePickerIsValid: datePickerModalIsValid.value,
    destinationIsValid: destinationModalIsValid.value,
    iataCodeError: iataCodeErrorMessage.value,
    promoCodeError: promoCodeErrorMessage.value,
    roomsAndGuestsInError: roomsAndGuestsInError.value.length > 0
  })

  if (errorFields.value.length > 0) {
    pushGA4Event(`step${currentStep.value}`, currentStep.value === 1 ? "check availability" : "select a room", {
      eventName: EventNameEnum.booking_form_interact,
      event_data: {
        error_field: errorFieldsString.value,
        // TODO: Wait po return, for the moment same to legacy
        error_type: "invalid value",
        form_action: "error"
      }
    })

    if (firstError.value === "destination") {
      destinationModalIsOpen.value = true
    } else if (firstError.value === "datePicker") {
      datePickerModalIsOpen.value = true
    } else if (roomsAndGuestsInError.value.length > 0) {
      roomsAndGuestsModalIsOpen.value = true
    } else if (firstError.value === "iataCode" || firstError.value === "promoCode") {
      specialRatesModalIsOpen.value = true
    }

    return
  }

  // Everything is valid, we can submit
  else {
    specialRateModel.value = validateSpecialRateWithPromoCode(specialRateModel.value, promoCodeModel.value)

    pushGA4Event(`step${currentStep.value}`, currentStep.value === 1 ? "check availability" : "select a room", {
      eventName: EventNameEnum.booking_form_submit
    })

    emit("SearchEngineMobile::check")
  }

  loading.value = false
}

watchDebounced(
  destinationInput,
  () => {
    destinationErrorMessageValidation(true)
  },
  { debounce: 300, maxWait: 300 }
)

watch(
  () => currentDatesModel.value,
  () => {
    datePickerErrorMessageValidation(true)
  }
)

defineExpose({ handleSubmit })
</script>

<template>
  <form class="Search-engine-mobile" @submit.prevent="handleSubmit">
    <DestinationModal
      v-model:is-open="destinationModalIsOpen"
      v-model:hotel="hotelModel"
      v-model:input-filter="destinationInput"
      :error-message="destinationErrorMessage"
      :is-loading="isLoading"
      :is-valid="destinationModalIsValid"
      :items="filteredDestinationsFallback"
      @destination-modal::check="validateDestinationModal"
    />

    <DatePickerModal
      v-model:is-open="datePickerModalIsOpen"
      v-model:dates="datesModel"
      v-model:current-dates="currentDatesModel"
      :error-message="datePickerErrorMessage"
      :is-valid="datePickerModalIsValid"
      :max-date="maxDate"
      :max-range="maxRange"
      :min-range="minRange"
    />

    <RoomsAndGuestsModal
      v-model:is-open="roomsAndGuestsModalIsOpen"
      v-model:rooms="roomsModel"
      v-model:rooms-and-guests-in-error="roomsAndGuestsInError"
      :is-valid="!roomsAndGuestsInError.length"
      :max-room="maxRoom"
      :max-pax="maxPax"
      :max-adult="maxAdult"
      :max-child="maxChild"
      :max-child-age="maxChildAge"
    />

    <SpecialRatesModal
      v-model:is-open="specialRatesModalIsOpen"
      v-model:iata-code="iataCodeModel"
      v-model:unvalidated-iata-code="unvalidatedIataCode"
      v-model:special-rate="specialRateModel"
      v-model:promo-code="promoCodeModel"
      :display-top-error-message="displaySpecialRatesTopErrorMessage || !promoCodeIsValid"
      :iata-error-message="iataCodeErrorMessage"
      :promo-error-message="promoCodeErrorMessage"
      :is-valid="specialRatesModalIsValid"
      @special-rates-modal::validate="specialRatesErrorMessageValidation(true)"
    />

    <UiButton
      v-if="hasCheckRatesButton"
      :text="$t('components.search_engine.check_rates')"
      :is-loading="loading"
      class="Search-engine-mobile__button"
      @click="handleSubmit"
    />
  </form>
</template>

<style lang="scss" scoped>
@use "./SearchEngineMobile.scss";
</style>
