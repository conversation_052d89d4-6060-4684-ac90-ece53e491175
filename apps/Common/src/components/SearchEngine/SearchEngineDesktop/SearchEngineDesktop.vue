<script setup lang="ts">
import {
  DateMoment,
  SpecialRates,
  UiButton,
  UiButtonVariation,
  UiDivider,
  UiRoomSelectDefault,
  filterDestinationsDesktop,
  scrollToElement
} from "design-system"
import { computed, ref } from "vue"
import {
  datePickerInputsValidation,
  datePickerValidation,
  destinationValidation,
  iataSchema,
  useGA4Event,
  useIdentificationPlainText,
  useLoader,
  useSearch,
  useSearchConstraints,
  useSearchEngineSectionValidation,
  useZodValidation
} from "../../../composables"
import DatePickerDropdown from "../../SearchCriteria/DatePicker/DatePickerDropdown/DatePickerDropdown.vue"
import DestinationDropdown from "../../SearchCriteria/Destination/DestinationDropdown/DestinationDropdown.vue"
import { EventNameEnum } from "../../../global/enums"
import RoomsAndGuestsDropdown from "../../SearchCriteria/RoomsAndGuests/RoomsAndGuestsDropdown/RoomsAndGuestsDropdown.vue"
import { SearchEngineDesktopProps } from "./interface"
import SpecialRatesDropdown from "../../SearchCriteria/SpecialRatesDropdown/SpecialRatesDropdown.vue"
import { mapApiHotelsToDestinationDesktop } from "../../../services/hotels/hotels.mapper"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useSearchValidation } from "../../../composables/useSearchValidation"
import { validateSpecialRateWithPromoCode } from "../../../helpers/specialRatesValidation"
import { watchDebounced } from "@vueuse/core"

defineProps<SearchEngineDesktopProps>()
const emit = defineEmits(["SearchEngineDesktop::check"])

const { t } = useI18n()
const { hotelsList } = useHotelsStore()
const { hotel, dates, rooms, specialRate, promoCode, iataCode, promoCodeIsValid } = useSearch()
const { generateFixedDates } = useSearchEngineSectionValidation()
const { refreshIdentification } = useIdentificationPlainText()
const { pushGA4Event } = useGA4Event()
const { loading } = useLoader("search")

const localDates = ref(dates.value)
const localRooms = ref(rooms.value)

// Refs to components
const datePickerDropdown = ref()
const searchEngineDesktop = ref()
const roomsAndGuestsDropdown = ref()

// Destination
const localHotel = ref(hotel.value)
const destinationDropdownIsOpen = ref(false)
const destinationDropdownIsValid = ref(true)
const destinationErrorMessage = ref<string | undefined>()
const destinationInput = ref<string>(hotel.value?.name || "")

// Date picker
const datePickerErrorMessage = ref<string | undefined>()
const datePickerInputsAreValid = ref({ endDate: true, startDate: true })
const localStartDate = ref<string>()
const localEndDate = ref<string>()

// Rooms and guests
const roomsAndGuestsInError = computed(() => {
  const errorIndexes: number[] = []
  localRooms.value?.forEach((room, index) => {
    if (room.hasErrorMessage) {
      errorIndexes.push(index)
    }
  })
  return errorIndexes
})

// Special rates
const displaySpecialRatesTopErrorMessage = ref(false)
const specialRatesDropdownIsOpen = ref(false)
const specialRatesDropdownIsValid = ref(true)

const iataCodeErrorMessage = ref<string | undefined>()
const promoCodeErrorMessage = ref<string>()

const localSpecialRate = ref<SpecialRates>(specialRate.value)
const localPromoCode = ref(promoCode.value)
const localIataCode = ref(iataCode.value)

const scrollOffset = -30 // the px value offset on the scrollTo

const mappedHotels = computed(() => mapApiHotelsToDestinationDesktop(hotelsList))
const hotelId = computed(() => localHotel.value?.id)
const { calendarConstraints, occupancyConstraints } = useSearchConstraints(hotelId)

// Removes the destinations that don't match the input value
const filteredDestinations = computed(() => {
  return filterDestinationsDesktop(mappedHotels.value, destinationInput)
})

// Removes the destinations that don't match the input value
// and returns the full list of destinations if there's no destinations left after the filter
const filteredDestinationsFallback = computed(() => {
  return filteredDestinations.value.length === 0 ? mappedHotels.value : filteredDestinations.value
})

// Destination validations
const destinationErrorMessageValidation = (allowEmptyString?: boolean) => {
  destinationErrorMessage.value = destinationValidation(
    destinationInput.value,
    filteredDestinations.value,
    allowEmptyString
  )

  // the red border around the input isn't displayed for the "empty input" case
  if (localHotel.value || !destinationInput.value) {
    destinationDropdownIsValid.value = true
  }
}

const validateDestinationDropdown = () => {
  destinationErrorMessageValidation()
  destinationDropdownIsValid.value = !!localHotel.value
}

// Date picker validation
const datePickerDatesValidation = (datesObject?: { endDate?: Date; startDate?: Date }) => {
  const { endDate, startDate } = generateFixedDates(localDates.value, datesObject)
  datePickerErrorMessageValidation({ endDate: endDate, startDate: startDate }, true)
}

const datePickerErrorMessageValidation = (
  datesObject: { startDate?: string; endDate?: string },
  allowEmptyDates?: boolean
) => {
  // Stores startDate and endDate separately because the v-model is an array
  // and doesn't care about the date position for the inputs validation.
  localStartDate.value = datesObject.startDate
  localEndDate.value = datesObject.endDate

  // Conditional reset of inputs validity
  if (datesObject?.startDate && datesObject?.startDate !== "" && datesObject?.startDate !== "-") {
    datePickerInputsAreValid.value.startDate = true
  }
  if (datesObject?.endDate && datesObject?.endDate !== "" && datesObject?.endDate !== "-") {
    datePickerInputsAreValid.value.endDate = true
  }

  datePickerErrorMessage.value = datePickerValidation(
    localStartDate.value || "",
    localEndDate.value || "",
    allowEmptyDates
  )
}

const validateDatePicker = () => {
  datePickerErrorMessageValidation({
    endDate: localEndDate.value || "",
    startDate: localStartDate.value || ""
  })
  datePickerInputsAreValid.value = datePickerInputsValidation(localStartDate.value, localEndDate.value)
}

const roomsAndGuestsRoomsValidation = (onlyRemoveErrors?: boolean) => {
  localRooms.value.forEach((room) => {
    if (room.dropdownsInError?.length && !onlyRemoveErrors) {
      room.hasErrorMessage = true
    } else if (room.dropdownsInError?.length === 0) {
      room.hasErrorMessage = false
    }
  })
}

const roomsAndGuestsSingleDropdownValidation = (dropdown: { roomIndex: number; dropdownIndex: number }) => {
  if (localRooms.value[dropdown.roomIndex].childrenAges[dropdown.dropdownIndex] === UiRoomSelectDefault) {
    if (localRooms.value[dropdown.roomIndex].dropdownsInError) {
      localRooms.value[dropdown.roomIndex]?.dropdownsInError?.push(dropdown.dropdownIndex)
    } else {
      localRooms.value[dropdown.roomIndex].dropdownsInError = [dropdown.dropdownIndex]
    }
  }

  // if dropdown and not the default value, we have to filter the index out of the array
  else {
    const filteredErrors = localRooms.value[dropdown.roomIndex].dropdownsInError?.filter(
      (item) => item !== dropdown.dropdownIndex
    )
    localRooms.value[dropdown.roomIndex].dropdownsInError = filteredErrors

    // At this point, there might be no more errors for the room,
    // so we revalidate the rooms to remove the error message if applicable
    roomsAndGuestsRoomsValidation()
  }
}
const roomsAndGuestsAllDropdownsValidation = () => {
  localRooms.value.forEach((room) => {
    const roomDropdownsInError: number[] = []

    room.childrenAges.forEach((age, index) => {
      if (age === UiRoomSelectDefault) {
        room.hasErrorMessage = true
        roomDropdownsInError.push(index)
      }
    })

    room.dropdownsInError = [...new Set(roomDropdownsInError)]
  })
}

const roomsAndGuestsErrorMessageValidation = () => {
  roomsAndGuestsAllDropdownsValidation()
  roomsAndGuestsRoomsValidation()
}

// Special rates validation
const specialRatesErrorMessageValidation = (ignoreTopErrorMessage?: boolean) => {
  iataCodeErrorMessage.value = undefined
  promoCodeErrorMessage.value = undefined
  displaySpecialRatesTopErrorMessage.value = false

  const validatedSchema = useZodValidation(localIataCode.value, iataSchema)

  if (!validatedSchema.success) {
    const error = validatedSchema.error.format()
    iataCodeErrorMessage.value = error._errors[0]

    if (!ignoreTopErrorMessage) {
      displaySpecialRatesTopErrorMessage.value = true
    }

    return
  }

  specialRatesDropdownIsValid.value = true
}

const validateSpecialRatesDropdown = () => {
  specialRatesErrorMessageValidation()
  specialRatesDropdownIsValid.value = !iataCodeErrorMessage.value
}

const handleSpecialRatesDropdownClose = () => {
  localSpecialRate.value = validateSpecialRateWithPromoCode(localSpecialRate.value, localPromoCode.value)
}

const validatePromoCodeDropdown = async () => {
  await refreshIdentification()

  if (!promoCodeIsValid.value) {
    promoCodeErrorMessage.value = t("errors.invalid_promo_code")
    specialRatesDropdownIsValid.value = false
    specialRatesDropdownIsOpen.value = true
  } else {
    promoCodeErrorMessage.value = undefined
    specialRatesDropdownIsValid.value = true
  }
}

const handleSubmit = async () => {
  // Validation for all the required fields
  validateDestinationDropdown()
  validateDatePicker()
  roomsAndGuestsErrorMessageValidation()
  validateSpecialRatesDropdown()

  const { errorFields, firstError, errorFieldsString } = useSearchValidation({
    datePickerIsValid: datePickerInputsAreValid.value?.endDate && datePickerInputsAreValid.value?.startDate,
    destinationIsValid: destinationDropdownIsValid.value,
    iataCodeError: iataCodeErrorMessage.value,
    promoCodeError: promoCodeErrorMessage.value,
    roomsAndGuestsInError: roomsAndGuestsInError.value.length > 0
  })

  if (errorFields.value.length > 0) {
    pushGA4Event("step2", "select a room", {
      eventName: EventNameEnum.booking_form_interact,
      event_data: {
        error_field: errorFieldsString.value,
        // TODO: Wait po return, for the moment same to legacy
        error_type: "invalid value",
        form_action: "error"
      }
    })

    if (firstError.value === "destination") {
      scrollToElement(searchEngineDesktop.value, undefined, scrollOffset)

      destinationDropdownIsOpen.value = true
    } else if (firstError.value === "datePicker") {
      scrollToElement(searchEngineDesktop.value, undefined, scrollOffset)

      const moment = datePickerInputsAreValid.value?.startDate ? DateMoment.END : DateMoment.START

      datePickerDropdown.value.openDropdownModal(moment)
    } else if (firstError.value === "roomsAndGuests") {
      scrollToElement(searchEngineDesktop.value, undefined, scrollOffset)

      roomsAndGuestsDropdown.value.openModal()
    } else if (firstError.value === "iataCode" || firstError.value === "promoCode") {
      scrollToElement(searchEngineDesktop.value, undefined, scrollOffset)

      specialRatesDropdownIsOpen.value = true
    }

    return
  }

  // We don't have any error so we can check the rates
  else {
    loading.value = true
    const validatedSpecialRate = validateSpecialRateWithPromoCode(localSpecialRate.value, localPromoCode.value)

    // Data update
    hotel.value = localHotel.value
    dates.value = localDates.value
    rooms.value = localRooms.value
    specialRate.value = validatedSpecialRate
    promoCode.value = localPromoCode.value
    iataCode.value = localIataCode.value

    // Check if the new promo code is valid after update with local value
    if (validatedSpecialRate === SpecialRates.PROMO_CODE) {
      await validatePromoCodeDropdown()

      if (!specialRatesDropdownIsValid.value) {
        scrollToElement(searchEngineDesktop.value, undefined, scrollOffset)
        loading.value = false
        return
      }
    } else if (validatedSpecialRate !== SpecialRates.NONE) {
      await refreshIdentification()
    }

    pushGA4Event("step2", "select a room", {
      eventName: EventNameEnum.booking_form_submit
    })

    emit("SearchEngineDesktop::check")
  }
}

watchDebounced(
  destinationInput,
  () => {
    destinationErrorMessageValidation(true)
  },
  { debounce: 300, maxWait: 300 }
)
</script>

<template>
  <form ref="searchEngineDesktop" class="Search-engine-desktop">
    <DestinationDropdown
      v-model:hotel="localHotel"
      v-model:input-filter="destinationInput"
      v-model:dropdown-is-open="destinationDropdownIsOpen"
      :error-message="destinationErrorMessage"
      :is-valid="destinationDropdownIsValid"
      :items="filteredDestinationsFallback"
    />

    <UiDivider length="5.6rem" />

    <DatePickerDropdown
      ref="datePickerDropdown"
      v-model="localDates"
      class="Search-engine-desktop__date-picker"
      :error-message="datePickerErrorMessage"
      :inputs-are-valid="datePickerInputsAreValid"
      :max-date="calendarConstraints?.maxDate"
      :min-range="calendarConstraints?.minLengthOfStay"
      :max-range="calendarConstraints?.maxLengthOfStay"
      @date-picker-dropdown::clear-dates="datePickerDatesValidation"
      @date-picker-dropdown::change-start-date="datePickerDatesValidation"
      @date-picker-dropdown::change-end-date="datePickerDatesValidation"
    />

    <UiDivider length="5.6rem" />

    <RoomsAndGuestsDropdown
      ref="roomsAndGuestsDropdown"
      v-model="localRooms"
      class="Search-engine-desktop__rooms-and-guests"
      :max-adult="occupancyConstraints?.maxAdult"
      :max-child="occupancyConstraints?.maxChild"
      :max-child-age="occupancyConstraints?.maxChildAge"
      :max-pax="occupancyConstraints?.maxPax"
      :max-rooms="occupancyConstraints?.maxRoom"
      :rooms-and-guests-in-error="roomsAndGuestsInError"
      @rooms-and-guests-dropdown::validate-children-age="roomsAndGuestsSingleDropdownValidation($event)"
    />

    <UiDivider length="5.6rem" />

    <SpecialRatesDropdown
      v-model:special-rate="localSpecialRate"
      v-model:promo-code="localPromoCode"
      v-model:iata-code="localIataCode"
      v-model:is-open="specialRatesDropdownIsOpen"
      class="Search-engine-desktop__special-rates"
      :display-top-error-message="displaySpecialRatesTopErrorMessage || !promoCodeIsValid"
      :iata-error-message="iataCodeErrorMessage"
      :promo-error-message="promoCodeErrorMessage"
      :is-valid="specialRatesDropdownIsValid"
      @special-rates-dropdown::validate="specialRatesErrorMessageValidation(true)"
      @special-rates-dropdown::close="handleSpecialRatesDropdownClose"
    />

    <UiButton
      class="Search-engine-desktop__update"
      :text="$t('components.search_engine.update_search')"
      :variation="UiButtonVariation.TERTIARY"
      :is-loading="loading"
      @click="handleSubmit"
    />
  </form>
</template>

<style lang="scss" scoped>
@use "./SearchEngineDesktop.scss";
</style>
