@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Search-engine-section {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "10");

  &__content {
    display: flex;
    gap: map.get(spaces.$sizes, "7");
  }

  &__button {
    align-self: center;
  }

  // Destination modal alignment, specific to this version of the search engine
  :deep(.Dropdown-modal__content) {
    top: calc(100% + map.get(spaces.$sizes, "4"));
  }
}
