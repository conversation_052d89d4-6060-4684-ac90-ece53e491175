<script setup lang="ts">
import { computed } from "vue"
import { getGlobalConfig } from "../../global/config"

const config = getGlobalConfig()

const legalMail = computed(() => config.app.legalEmail)
</script>

<template>
  <div class="Legal-text caption-01 color-customs-text-light">
    <i18n-t keypath="components.legal_text.legal.first_paragraph" tag="p">
      <template #link>
        <a :href="`mailto:${legalMail}`">{{ legalMail }}</a>
      </template>
    </i18n-t>

    <p class="Legal-text__first-paragraph">{{ $t("components.legal_text.legal.second_paragraph") }}</p>
    <p>{{ $t("components.legal_text.legal.third_paragraph") }}</p>
  </div>
</template>

<style lang="scss" scoped>
@use "./LegalText.scss";
</style>
