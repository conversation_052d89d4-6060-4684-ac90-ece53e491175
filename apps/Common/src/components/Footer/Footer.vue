<script setup lang="ts">
import { UiFooter, type Ui<PERSON>ooter<PERSON><PERSON> } from "design-system"
import { computed } from "vue"
import { getGlobalConfig } from "../../global/config"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useI18n } from "vue-i18n"

const config = getGlobalConfig()
const { locale, t } = useI18n()

const moreNumbersUri = computed(() => {
  return replaceUriParams(config.legacy.endpoints.moreNumbers, "", locale.value)
})

const termsAndConditionsUri = computed(() => {
  return replaceUriParams(config.legacy.endpoints.termsAndConditions, "", locale.value)
})

const privacyPolicyUri = computed(() => {
  return replaceUriParams(config.all.privacyPolicy, config.all.hostname, locale.value)
})

const helpContactUri = computed(() => {
  return replaceUriParams(config.all.helpAndContact, "", locale.value)
})

const sitemapUri = computed(() => {
  return replaceUriParams(config.legacy.endpoints.sitemap, "", locale.value)
})

const accessibilityUri = computed(() => {
  return replaceUriParams(config.legacy.endpoints.webAccessibility, "", locale.value)
})

const homepageUri = computed(() => {
  return replaceUriParams(config.legacy.homePage, "", locale.value)
})

const footerProps = computed<UiFooterProps>(() => ({
  copyright: t("components.footer.copyright"),
  currentYear: new Date().getFullYear(),
  helpSection: {
    link: {
      href: moreNumbersUri.value,
      target: "_blank",
      text: t("components.footer.help_section.link")
    },
    mainContent: t("components.footer.help_section.main_content"),
    secondaryContent: t("components.footer.help_section.secondary_content"),
    title: t("components.footer.help_section.title")
  },
  links: [
    {
      href: termsAndConditionsUri.value,
      target: "_blank",
      text: t("components.footer.links.terms_conditions")
    },
    {
      href: privacyPolicyUri.value,
      target: "_blank",
      text: t("components.footer.links.privacy_policy")
    },
    {
      href: helpContactUri.value,
      target: "_blank",
      text: t("components.footer.links.do_not_sell")
    },
    {
      onClick: (e: Event) => {
        e.preventDefault()
        window.Optanon.ToggleInfoDisplay()
      },
      text: t("components.footer.links.cookies_preferences")
    },
    {
      href: sitemapUri.value,
      target: "_blank",
      text: t("components.footer.links.sitemap")
    },
    {
      href: accessibilityUri.value,
      target: "_blank",
      text: t("components.footer.links.accessibility")
    }
  ],
  logo: config.logoInvert,
  logoLink: homepageUri.value
}))
</script>

<template>
  <UiFooter v-bind="footerProps" />
</template>
