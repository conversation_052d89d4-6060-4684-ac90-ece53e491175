<script setup lang="ts">
import {
  ContextErrorKey,
  ContextFormContextKey,
  FormBuilderData,
  FormBuilderProps,
  // FormBuilderSection as FormBuilderSectionType,
  ValidationError
} from "./interface"
import { FormConfig, useForm } from "../../composables/useForm"
import { computed, nextTick, provide, reactive, ref, watch } from "vue"
import FormBuilderSection from "./FormBuilderSection.vue"
import { getGlobalConfig } from "../../global/config"
// import { useBasket } from "../../composables"

type Emits = {
  (event: "form:validate", data: FormBuilderData): void
  (event: "form:error", errors: Record<string, ValidationError>): void
}

const props = defineProps<FormBuilderProps>()
const emits = defineEmits<Emits>()

// const { updateBasketSummary } = useBasket()

const formConfig = computed<FormConfig>(() => ({
  basketId: props.basketId,
  countryMarket: props.countryMarket,
  currency: props.currency
}))

const { formContext, sections, initialForm } = useForm(formConfig)

const form = reactive<FormBuilderData>({})
const errors = ref<Record<string, ValidationError>>()
provide(ContextErrorKey, errors)
provide(ContextFormContextKey, formContext)
const brandConfiguration = getGlobalConfig()

const completedSections = ref<string[]>([])
const sectionsLocked = ref<string[]>([])
const openedSections = reactive<Record<string, boolean>>({})

const modelValue = computed({
  get() {
    return form
  },
  set(value) {
    Object.assign(form, value)
  }
})

watch(
  initialForm,
  (newInitialForm, oldInitialForm) => {
    if (brandConfiguration?.formFieldStructure.lockFurtherSteps) {
      for (let i = 1; i < sections.value.length; i++) {
        sectionsLocked.value?.push(sections.value[i].structure.key)
      }
    }

    // Only execute this for the first instanciation once the endpoint call is resolved
    if (oldInitialForm || !newInitialForm) return

    Object.assign(form, newInitialForm)
  },
  {
    once: true
  }
)

const validate = async () => {
  const validationErrors: Record<string, ValidationError> = {}
  const validationForm: FormBuilderData = {}
  let validationSuccess = true

  for (const section of sections.value) {
    const { data, error, success } = await validateSection(section.structure.key)
    validationSuccess = validationSuccess && success

    if (error) {
      for (const fieldError of error.errors) {
        const field = fieldError.path.join(".")

        validationErrors[field] = {
          error: error,
          field,
          message: fieldError.message
        }
      }
    }

    if (success) {
      Object.assign(validationForm, data)
    }
  }

  errors.value = validationErrors

  if (validationSuccess) {
    emits("form:validate", validationForm)
  } else {
    emits("form:error", errors.value)
  }
}

const validateSection = async (sectionKey: string) => {
  const section = sections.value.find((sec) => sec.structure.key === sectionKey)

  const { data, error, success } = section!.schema.safeParse(form)

  if (success) {
    // await applySectionActions(section!, data)

    if (!completedSections.value.includes(sectionKey)) completedSections.value.push(sectionKey)

    openedSections[sectionKey] = false

    const sectionIndex = sections.value.findIndex((sec) => sec.structure.key === sectionKey)
    const nextSection = sections.value[sectionIndex + 1]

    if (nextSection) {
      sectionsLocked.value = sectionsLocked.value.filter((sectionId) => sectionId !== nextSection.structure.key)
    }

    // Scroll behavior
    nextTick(() => {
      const sectionElement = nextSection ? document.getElementById(nextSection.structure.key) : undefined

      if (sectionElement) {
        const position = sectionElement.getBoundingClientRect().top
        const offset = position + window.pageYOffset - 16 // Small offset
        window.scrollTo({ behavior: "smooth", top: offset })
      }
    })
  }

  return { data, error, success }
}

/**
 * @todo
 * This is disabled for now since the figma aren't supporting all the required fields
 * Basket isn't updated as such, and the basket -> order transformation can't occur
 * This is commented to allow to work a bit more on the form
 */
// const applySectionActions = async (section: FormBuilderSectionType, data: unknown) => {
//   if (!Array.isArray(section.structure.actions)) return

//   for (const action of section.structure.actions) {
//     const payload = typeof action.transform === "function" ? await action.transform(data) : data

//     switch (action.type) {
//       case "update-basket-summary":
//         await updateBasketSummary(payload)
//         break
//     }
//   }
// }
</script>

<template>
  <form @submit.prevent="validate">
    <FormBuilderSection
      v-for="section of sections"
      :key="section.structure.key"
      v-model="modelValue"
      v-model:is-open="openedSections[section.structure.key]"
      :disabled="sectionsLocked.includes(section.structure.key)"
      :is-complete="completedSections.includes(section.structure.key)"
      :can-be-edited="completedSections.includes(section.structure.key)"
      :section="section"
      @submit="validateSection"
    />
  </form>
</template>
