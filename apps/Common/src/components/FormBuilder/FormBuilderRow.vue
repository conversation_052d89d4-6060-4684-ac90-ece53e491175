<script setup lang="ts" generic="T">
import { FormBuilderData, FormBuilderRowProps } from "./interface"
import FormFieldAccordion from "./fields/FormFieldAccordion.vue"
import FormFieldFields from "./fields/FormFieldFields.vue"
import FormFieldHelper from "./fields/FormFieldHelper.vue"
import FormFieldText from "./fields/FormFieldText.vue"
import { computed } from "vue"

const props = defineProps<FormBuilderRowProps>()

const value = defineModel<FormBuilderData>()

const component = computed(() => {
  switch (props.field.type) {
    case "text":
      return FormFieldText

    case "fields":
      return FormFieldFields

    case "helper":
      return FormFieldHelper

    case "accordion":
      return FormFieldAccordion

    // To do
    // case "loyalty":
    //   return FormFieldLoyalty

    default:
      return null
  }
})
</script>

<template>
  <component :is="component" v-model="value" :field="field" />
</template>
