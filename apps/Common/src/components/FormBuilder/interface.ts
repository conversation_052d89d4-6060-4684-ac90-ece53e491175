import { FormStructureField, FormStructureSection } from "@shared/types/FormStructure"
import { <PERSON>hem<PERSON>, ZodError } from "zod"

export const ContextFormContextKey = "form-context" as const
export const ContextErrorKey = "form-errors" as const

export interface FormBuilderProps {
  basketId: string
  currency: string
  countryMarket: string
}

export interface FormBuilderSectionProps {
  section: FormBuilderSection
  disabled: boolean
  isComplete: boolean
  canBeEdited: boolean
}

export type FormBuilderSection = {
  structure: FormStructureSection
  schema: Schema
}

export interface FormBuilderRowProps {
  field: FormStructureField
}

export interface ValidationError {
  field: string
  message: string
  error: ZodError
}

export interface FormContext {
  isRussianLawContext: boolean
}

export type FormBuilderData = {
  [key: string]: string | boolean
}
