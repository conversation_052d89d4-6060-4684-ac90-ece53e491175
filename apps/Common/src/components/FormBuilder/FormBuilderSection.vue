<script setup lang="ts">
import { FormBuilderData, FormBuilderSectionProps } from "./interface"
import { UiButton, UiButtonSize, UiDetailSummary } from "design-system"
import FormBuilderRow from "./FormBuilderRow.vue"
import { computed } from "vue"
import { useLoader } from "../../composables"

type Emits = {
  (event: "submit", key: string): void
}

const props = defineProps<FormBuilderSectionProps>()
defineEmits<Emits>()

const structure = computed(() => props.section.structure)

const value = defineModel<FormBuilderData>()
const isOpen = defineModel<boolean>("is-open")
const { loading } = useLoader(props.section.structure.key)
</script>

<template>
  <UiDetailSummary
    :id="structure.key"
    v-model:is-open="isOpen"
    class="Form-builder-section"
    :title="structure.title"
    :subtitle="structure.subtitle"
    :disabled="disabled"
    :is-complete="isComplete"
    :can-be-edited="isComplete && canBeEdited"
  >
    <template #content>
      <div class="Form-builder-section__content">
        <FormBuilderRow
          v-for="field of structure.fields"
          :key="field.key"
          v-model="value"
          class="Form-builder-section__row"
          :field="field"
        />
      </div>
      <div class="Form-builder-section__action">
        <UiButton
          :text="$t('form.get_acquainted_submit')"
          :size="UiButtonSize.SMALL"
          :is-loading="loading"
          @click="$emit('submit', section.structure.key)"
        />
      </div>
    </template>
  </UiDetailSummary>
</template>

<style lang="scss" scoped>
@use "./FormBuilder.scss";
</style>
