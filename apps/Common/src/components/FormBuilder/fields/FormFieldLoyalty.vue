<script setup lang="ts">
import { FormBuilderRowProps } from "../interface"
import { UiEnrollmentBlock } from "design-system"
import { computed } from "vue"
import { replaceUriParams } from "../../../helpers/uriHelper"
import { useBasket } from "../../../composables"
import { useI18n } from "vue-i18n"
import { useOidcUri } from "../../../composables/useOidcUri"

defineProps<FormBuilderRowProps>()

const { enrollmentChoice } = useBasket()
const { locale } = useI18n()
const { signInUri } = useOidcUri()

const termsAndConditionLink = computed(() =>
  replaceUriParams(import.meta.env.VITE_APP_ALL_TERMS_AND_CONDITIONS_URL, "", locale.value)
)
</script>

<template>
  <UiEnrollmentBlock
    v-bind="field.enrollment"
    v-model="enrollmentChoice"
    :terms-and-conditions-link="termsAndConditionLink"
    :sign-in-url="signInUri"
  />
</template>
