<script setup lang="ts">
import { FormBuilderData, FormBuilderRowProps } from "../interface"
import { UiAccordionGroup, UiAccordionGroupVariation } from "design-system"
import FormBuilderRow from "../FormBuilderRow.vue"
import { computed } from "vue"

const props = defineProps<FormBuilderRowProps>()

const value = defineModel<FormBuilderData>()

const accordionItems = computed(() => [
  {
    id: 1,
    name: props.field.key,
    title: props.field.label as string
  }
])
</script>

<template>
  <div class="Form-field-accordion">
    <UiAccordionGroup :variation="UiAccordionGroupVariation.THIN" :items="accordionItems">
      <template #[`content-${field.key}`]>
        <div class="Form-builder-section__content">
          <FormBuilderRow
            v-for="field of field.accordionField"
            :key="field.key"
            v-model="value"
            class="Form-builder-section__row"
            :field="field"
          />
        </div>
      </template>
    </UiAccordionGroup>
  </div>
</template>
