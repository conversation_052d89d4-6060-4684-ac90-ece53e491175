<script setup lang="ts">
import { FormBuilderRowProps } from "../interface"

defineProps<FormBuilderRowProps>()
</script>

<template>
  <div class="Form-field-helper">
    <h3 v-if="field.label" class="body-02">
      {{ field.label }}
      <span v-if="field.optional">{{ $t("form.optional") }}</span>
    </h3>
    <p v-if="field.description" class="Form-field-helper__description caption-01">{{ field.description }}</p>
  </div>
</template>
