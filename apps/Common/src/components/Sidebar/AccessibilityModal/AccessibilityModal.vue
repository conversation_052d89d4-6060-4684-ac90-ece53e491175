<script setup lang="ts">
import {
  ActionAlignment,
  UiAccessibilityModalContent,
  UiAccessibilityModalContentProps,
  UiLink,
  UiLinkVariant,
  UiModal
} from "design-system"
import { ref } from "vue"

const accessibilityModalProps = defineProps<UiAccessibilityModalContentProps>()

const isOpen = ref<boolean>(false)
</script>
<template>
  <div class="Accessibility-modal">
    <UiLink
      type="button"
      :text="$t('components.sidebar.accessible_rooms_button_text')"
      class="caption-01-underline"
      :variant="UiLinkVariant.NEUTRAL"
      @click="isOpen = true"
    />
    <UiModal
      v-if="isOpen"
      :action-alignment="ActionAlignment.FULL"
      no-header-shadow
      with-overlay
      @ui-modal::close="isOpen = false"
    >
      <UiAccessibilityModalContent v-bind="accessibilityModalProps" />
    </UiModal>
  </div>
</template>
