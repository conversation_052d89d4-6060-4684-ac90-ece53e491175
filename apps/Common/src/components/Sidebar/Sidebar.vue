<script setup lang="ts">
import {
  DateMoment,
  RoomType,
  UiBookingSummary,
  UiBookingSummaryProps,
  UiBookingSummaryStep,
  UiCancellationPolicy,
  UiCancellationPolicyVariant,
  UiComplementaryInformation,
  UiPricingDetails,
  UiSidebarAccordion,
  UiSidebarAccordionProps,
  mockStoreEnhanceStep,
  mockStoreSearchStep,
  mockStoreStayStep
} from "design-system"
import { computed, watch } from "vue"
import {
  useAccessibilitiesFacilities,
  useCurrentHotel,
  useCurrentRoom,
  useCurrentWindowSize,
  useLoader,
  useOffersAndAccommodations,
  usePricingCondition,
  useSearch,
  useSearchEngineSectionValidation
} from "../../composables"
import AccessibilityModal from "./AccessibilityModal/AccessibilityModal.vue"
import { BlocInteractionEnum } from "../../global/enums"
import { getGlobalConfig } from "../../global/config"
import { getHotelUrlById } from "../../services/hotels/hotels.mapper"
import { pricingConditionMapper } from "../../services/offer/offer.mapper"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useBasket } from "../../composables/useBasket"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"
import { useUserPOS } from "../../composables/useUserPOS"
import { useUserStore } from "../../stores/user/user"

const { accessibilities, loadAccessibilities } = useAccessibilitiesFacilities()
const { hotel: currentHotel } = useCurrentHotel()
const { dates, hotel, rooms, lengthOfStay } = useSearch()
const { isDesktop } = useCurrentWindowSize()
const { basket, selectedOffers } = useBasket()
const { userPOS } = useUserPOS()
const { locale } = useI18n()
const { hasAccessibility, offers, getRoomDetails } = useOffersAndAccommodations()
const { loading } = useLoader("search")
const { cancellationPolicies, getPricingConditionById } = usePricingCondition()
const route = useRoute()
const { currentRoom } = useCurrentRoom()
const user = useUserStore()

const { handleSearchSubmit } = useSearchEngineSectionValidation()
const config = getGlobalConfig()

const displayPricingDetails = computed(
  () =>
    // TODO hide pricing details block for now, due to inconsistency between LBF and Legacy (cf https://accor-it.atlassian.net/browse/CONVERT-840)
    // [UiBookingSummaryStep.SEARCH, UiBookingSummaryStep.STAY].includes(route.name as UiBookingSummaryStep) &&
    // !!basket.value &&
    // pricingDetails.value &&
    // rooms.value.length > 1
    false
)

const pricingDetails = computed(() => {
  if (!basket.value) return null

  //TODO replace hard coded value
  return {
    hotelCurrencyPrices: {
      currency: basket.value.hotelCurrency,
      hotelAmount: "$4071",
      onlineAmount: "$0",
      roomPrice: "$1767",
      taxAndFeesPrice: "$170",
      totalPrice: basket.value.pricing.formattedTotalAmountHotelCurrency
    },
    numberOfNight: lengthOfStay.value,
    userCurrencyPrices: {
      currency: basket.value.currency,
      hotelAmount: "4071",
      onlineAmount: "0",
      totalPrice: basket.value.pricing.formattedTotalAmount
    }
  }
})

const shouldDisplayAccessibilityComponent = computed(() => {
  return route.name !== UiBookingSummaryStep.SEARCH && hasAccessibility.value && offers.value.length > 0
})

// TODO: Change mockStoreData with real data
const mockStoreData = computed<UiBookingSummaryProps>(() => {
  switch (route.name) {
    case UiBookingSummaryStep.ENHANCE:
      return mockStoreEnhanceStep
    case UiBookingSummaryStep.SEARCH:
      return mockStoreSearchStep
    case UiBookingSummaryStep.STAY:
      return mockStoreStayStep
    default:
      return mockStoreSearchStep
  }
})

const adultsCount = computed(() => {
  let count = 0
  rooms.value.forEach((room) => {
    count += room.adults
  })

  return count as number
})

const childrenCount = computed(() => {
  let count = 0
  rooms.value.forEach((room) => {
    count += room.children
  })

  return count as number
})

const formattedRooms = computed<RoomType[]>(() => [
  ...rooms.value.map((room) => {
    const productCode = room.productCode

    const roomDetails = getRoomDetails(productCode)
    const rateTitle = selectedOffers.value?.find((offer) => offer.id === room.rateId)?.rate.label as string

    return {
      ...room,
      offerDetails: {
        ...room.offerDetails,
        rateTitle: rateTitle === "NONE" ? "" : rateTitle,
        roomBedding: roomDetails?.bedding,
        roomMedias: roomDetails?.images,
        roomTitle: roomDetails?.title
      }
    }
  })
])

const hotelUrl = computed(() => {
  const hotelId = currentHotel.value?.id
  if (!hotelId) return ""

  const hotelPath = getHotelUrlById(hotelId)
  if (!hotelPath) return ""

  return `https://www.fairmont.com/${locale.value}${hotelPath}`
})

const bookingSummaryData = computed<UiBookingSummaryProps & UiSidebarAccordionProps>(() => {
  return {
    adults: adultsCount.value,
    checkInHour: currentHotel.value?.checkInHour,
    checkOutHour: currentHotel.value?.checkOutHour,
    children: childrenCount.value,
    currency: userPOS.currency,
    currentRoomId: currentRoom.value.id,
    dateIn: dates.value[DateMoment.START],
    dateOut: dates.value[DateMoment.END],
    images: currentHotel.value?.medias,
    price: basket.value?.pricing.formattedTotalAmount || "",
    rooms: formattedRooms.value,
    step: mockStoreData.value.step,
    title: currentHotel.value?.name,
    url: hotelUrl.value
  }
})

const moreNumbersUri = computed(() => {
  return replaceUriParams(config.legacy.endpoints.moreNumbers, "", locale.value)
})

const bestPriceGuarantee = computed(() => {
  return replaceUriParams(config.all.bestPriceConditions, config.all.hostname, locale.value)
})

const help = computed(() => {
  return replaceUriParams(config.all.help, "", locale.value)
})

watch(
  () => hotel.value?.id,
  (hotelId) => {
    if (hotelId) {
      loadAccessibilities(hotelId)
    }
  }
)

const loadRateModalDetails = async (roomId: number) => {
  const roomIndex = rooms.value.findIndex((room) => room.id === roomId)
  const room = rooms.value[roomIndex]
  if (!room?.productCode || !room.rateId) return

  const pricingDetails = await getPricingConditionById(room.rateId)

  if (!pricingDetails) return

  rooms.value[roomIndex].offerDetails = {
    ...rooms.value[roomIndex].offerDetails,
    rateModalContent: pricingConditionMapper(pricingDetails, user.isLogged, hotel.value?.expediaCompliant || false)
  }
}

const loadRoomModalDetails = async (roomId: number) => {
  const roomIndex = rooms.value.findIndex((room) => room.id === roomId)
  const room = rooms.value[roomIndex]
  if (!room?.productCode || !room.rateId) return

  const roomDetails = getRoomDetails(room.productCode)
  if (!roomDetails) return

  rooms.value[roomIndex].offerDetails = {
    ...rooms.value[roomIndex].offerDetails,
    roomModalContent: roomDetails
  }
}
</script>

<template>
  <aside class="Sidebar">
    <UiSidebarAccordion
      v-if="!isDesktop && route.name !== UiBookingSummaryStep.SEARCH && basket?.id"
      :currency="bookingSummaryData.currency"
      :price="bookingSummaryData.price"
    >
      <UiBookingSummary
        v-if="hotel?.id && rooms.length > 0 && currentHotel?.medias?.length"
        v-bind="bookingSummaryData"
        :loading="loading"
        @ui-booking-summary::load-rate-modal="loadRateModalDetails"
        @ui-booking-summary::load-room-modal="loadRoomModalDetails"
      />

      <UiPricingDetails
        v-if="displayPricingDetails && pricingDetails"
        v-bind="pricingDetails"
        :show-room-details="false"
        :show-pay-online="false"
        :show-pay-at-the-hotel="false"
      />

      <UiCancellationPolicy
        v-if="cancellationPolicies && route.name !== UiBookingSummaryStep.SEARCH"
        v-bind="cancellationPolicies"
        :variant="UiCancellationPolicyVariant.SIDE_BAR"
      />
    </UiSidebarAccordion>

    <template v-else-if="isDesktop">
      <UiBookingSummary
        v-if="
          hotel?.id &&
          rooms.length > 0 &&
          bookingSummaryData.dateIn &&
          bookingSummaryData.dateOut &&
          currentHotel?.medias?.length
        "
        v-bind="bookingSummaryData"
        :loading="loading"
        @ui-booking-summary::load-rate-modal="loadRateModalDetails"
        @ui-booking-summary::load-room-modal="loadRoomModalDetails"
        @ui-booking-summary::validate="handleSearchSubmit(BlocInteractionEnum.RIGHT)"
      />

      <UiPricingDetails
        v-if="displayPricingDetails && pricingDetails"
        v-bind="pricingDetails"
        :show-room-details="false"
        :show-pay-online="false"
        :show-pay-at-the-hotel="false"
      />

      <UiCancellationPolicy
        v-if="cancellationPolicies && route.name !== UiBookingSummaryStep.SEARCH"
        v-bind="cancellationPolicies"
      />
    </template>

    <UiComplementaryInformation
      v-if="shouldDisplayAccessibilityComponent"
      :title="$t('components.sidebar.accessible_rooms_title')"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.accessible_rooms_text") }}</p>
        <AccessibilityModal v-bind="accessibilities" />
      </template>
    </UiComplementaryInformation>

    <UiComplementaryInformation
      :title="$t('components.sidebar.best_price_title')"
      :top-link-text="$t('components.sidebar.best_price_top_link_text')"
      :top-link-href="bestPriceGuarantee"
      top-link-target="_blank"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.best_price_text") }}</p>
      </template>
    </UiComplementaryInformation>

    <UiComplementaryInformation
      :title="$t('components.sidebar.need_help_title')"
      :top-link-text="$t('components.sidebar.need_help_top_link_text')"
      :top-link-href="moreNumbersUri"
      top-link-target="_blank"
      :bottom-link-text="$t('components.sidebar.need_help_bottom_link_text')"
      :bottom-link-href="help"
      bottom-link-target="_blank"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.need_help_text_1") }}</p>
        <p class="body-02">{{ $t("components.sidebar.need_help_text_2") }}</p>
      </template>
    </UiComplementaryInformation>
  </aside>
</template>
<style lang="scss" scoped>
@use "./Sidebar.scss";
</style>
