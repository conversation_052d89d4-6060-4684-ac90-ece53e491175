@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Header {
  position: relative;
  padding-block: map.get(spaces.$sizes, "5");
  background-color: map.get(colors.$basics, "black");

  @include mq.media(">=small") {
    padding: 1.6rem 0;
  }

  @include mq.media(">=medium") {
    padding: 2rem 0;
  }

  &__brand {
    display: flex;
    align-items: center;
    grid-column: 1 / 4;

    .Link {
      position: relative;

      :deep(.ads-link) {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    @include mq.media(">=small") {
      grid-column: 1 / 3;
    }

    @include mq.media(">=large") {
      justify-content: center;
      grid-column: 6 / 8;
    }
  }

  &__brand-logo {
    height: 3.6rem;
  }

  &__right {
    display: flex;
    justify-content: end;
    grid-column: 4 / -1;
    gap: map.get(spaces.$sizes, "7");
    color: map.get(colors.$basics, "white");

    @include mq.media(">=small") {
      grid-column: 3 / -1;
    }

    @include mq.media(">=medium") {
      grid-column: 8 / -1;
    }
  }

  &__button {
    display: flex;
    align-items: center;

    :deep(.ads-button) {
      @include text.lbf-text("caption-01-uppercase");
      min-height: unset;
      padding: 0;

      &:focus-visible::after {
        border-color: map.get(colors.$basics, "white");
        border: map.get(boxes.$borders, "interactiveSelected");
      }
    }
  }

  :deep(.Dropdown-account) {
    display: flex;
    flex-direction: row;
    align-items: center;

    & > .Button > .ads-button {
      text-transform: unset;
    }
  }

  :deep(.Dropdown-account__activator .ads-button--size-small) {
    min-height: unset;
    padding: 0;
  }

  :deep(.ads-button--primary) {
    &:hover,
    &:focus {
      opacity: 0.8;
      background-color: unset !important; // ads overwrite
    }
  }
}
