<script setup lang="ts">
import { UiDropdownAccount, UiLink, UiLinkVariant, UiLoadingDots } from "design-system"
import { useModal, usePosCurrencyHandler, usePosLanguageHandler, useUserPOS } from "../../composables"
import CurrencyModal from "../CurrencyModal/CurrencyModal.vue"
import { HeaderProps } from "./interface"
import LanguageModal from "../LanguageModal/LanguageModal.vue"
import LogoutModal from "../LogoutModal/LogoutModal.vue"
import { LoyaltyAccountUriFragments } from "./constants"
import { computed } from "vue"
import { getGlobalConfig } from "../../global/config"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useI18n } from "vue-i18n"
import { useOidcUri } from "../../composables/useOidcUri"
import { useUserStore } from "@stores/user/user"

const {
  countriesGeographicalArea,
  countriesGeographicalAreaOptions,
  countryRegionLabel,
  countryRegionOptions,
  resetState
} = usePosLanguageHandler()
const {
  currencyCode,
  currencyCodeOptions,
  currenciesGeographicalArea,
  currenciesGeographicalAreaOptions,
  resetStateCurrency
} = usePosCurrencyHandler()
const { userPOS } = useUserPOS()
const { loggedUser } = useUserStore()
const { locale } = useI18n()
const { signInUri, signUpUri, uriInfos, logOut } = useOidcUri()
const { openModal } = useModal("logout")
const config = getGlobalConfig()

defineProps<HeaderProps>()

const myBookingsUrl = config.myBookingsUrl.replace("{locale}", locale.value)

const loyaltyAccountUri = computed(() => {
  const url = new URL(
    `${replaceUriParams(config.all.account, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.LOYALTY_ACCOUNT_URI_FRAGMENT}`,
    config.all.hostname
  )
  return url.href
})

const pointStatementUri = computed(() => {
  const url = new URL(
    `${replaceUriParams(config.all.account, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.POINT_STATEMENT_URI_FRAGMENT}`,
    config.all.hostname
  )
  return url.href
})

const reservationUri = computed(() => {
  const url = new URL(
    `${replaceUriParams(config.all.account, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.RESERVATION_URI_FRAGMENT}`,
    config.all.hostname
  )
  return url.href
})

function handleLogout() {
  logOut()
}

function handleLogoutModal() {
  openModal()
}

function handleSignInClicked() {
  window.location.href = signInUri.value
}

function handleSignUpClicked() {
  window.location.href = signUpUri.value
}
</script>
<template>
  <header class="Header container">
    <div class="grid">
      <div class="Header__brand">
        <UiLink :href="logoLink" :variant="UiLinkVariant.NEUTRAL" text="">
          <template #preprend-content>
            <span class="sr-only">{{ $t("global.go_to_homepage") }}</span>
            <img class="Header__brand-logo" :src="logo" :alt="$t('components.header.brand_logo')" />
          </template>
        </UiLink>
      </div>

      <div class="Header__right">
        <UiDropdownAccount
          :my-bookings-url="myBookingsUrl"
          :user-logged-infos="loggedUser"
          :your-loyalty-account-uri="loyaltyAccountUri"
          :your-point-statement-uri="pointStatementUri"
          :your-reservation-uri="reservationUri"
          @ui-dropdown-account::sign-in-clicked="handleSignInClicked"
          @ui-dropdown-account::sign-up-clicked="handleSignUpClicked"
          @ui-dropdown-account::log-out-clicked="handleLogoutModal"
        />

        <LogoutModal @logout-modal::logout="handleLogout" />

        <UiLoadingDots
          v-if="
            !userPOS.countryMarket ||
            !userPOS.currency ||
            !countriesGeographicalAreaOptions.length ||
            !countryRegionOptions.length ||
            !currencyCodeOptions.length ||
            !currenciesGeographicalAreaOptions.length
          "
          color="white"
        />

        <template v-else>
          <LanguageModal
            v-model:geographical-area="countriesGeographicalArea"
            v-model:country="countryRegionLabel"
            :geographical-options="countriesGeographicalAreaOptions"
            :country-options="countryRegionOptions"
            @close:language-modal="resetState"
          />

          <CurrencyModal
            v-model:geographical-area="currenciesGeographicalArea"
            v-model:currency="currencyCode"
            :geographical-options="currenciesGeographicalAreaOptions"
            :currency-options="currencyCodeOptions"
            @close:currency-modal="resetStateCurrency"
          />
        </template>
      </div>
    </div>
  </header>
</template>

<style lang="scss" scoped>
@use "./Header.scss";
</style>
