<script setup lang="ts">
import {
  BestOffersError,
  useCurrentRoom,
  useCurrentWindowSize,
  useGA4Event,
  useOffersAndAccommodations,
  useSearch,
  useUserPOS
} from "../../../composables"
import {
  DateMoment,
  RoomType,
  UiGallery,
  UiRoomCard,
  UiRoomDetailsModal,
  UiRoomDetailsModalContentProps
} from "design-system"
import { ErrorTypes, useHTTPError } from "../../../composables/useHTTPError"
import { calculateNights, truncateApiTitle } from "@shared/utils"
import { onMounted, ref, watch } from "vue"
import { AxiosError } from "axios"
import { EventNameEnum } from "../../../global/enums"
import { Offer } from "../../../services/offer/offer.types"
import RateCard from "../RateCard/RateCard.vue"
import { RateCardProps } from "../RateCard/interface"
import { RoomCardProps } from "../RoomCard/interface"
import { accommodationMapper } from "../../../services/accommodation/accommodation.mapper"
import { getSelectedRoomRateCards } from "../../../services/offer/offer.fetch"
import { rateCardMapper } from "../../../services/offer/offer.mapper"
import { useUserStore } from "@/stores/user/user"

type Emits = {
  (
    event: "choose-offer",
    payload: { apiOffer: Offer; classCode: string; rateId: string; productCode: string; rateCode: string }
  ): void
}

const props = defineProps<RoomCardProps>()
defineEmits<Emits>()

const { isMobile, isDesktop } = useCurrentWindowSize()
const { getOfferAccommodationById, offers } = useOffersAndAccommodations()
const { error: rateError, generateError } = useHTTPError("rate")
const { pushGA4Event } = useGA4Event()
const { currentRoom } = useCurrentRoom()
const { hotel, dates } = useSearch()
const { isLogged } = useUserStore()
const { userPOS } = useUserPOS()

const modelValue = defineModel<RoomType>({ required: true })

const isLoading = ref(false)
const roomCardRef = ref<HTMLElement | null>(null)
const modalIsOpen = ref(false)
const cardIsOpen = ref(false)
const rateCards = ref<RateCardProps[]>([])
const offersList = ref<Offer[]>([])
const loadingRateCards = ref(false)
const roomCardModalContent = ref<UiRoomDetailsModalContentProps>()
const isGalleryOpen = ref(false)
const displayLoading = ref(false)

const handleRoomCard = async () => {
  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: `select room - ${truncateApiTitle(props.roomCardContent.title)}`,
      bloc_name: "room bloc"
    }
  })

  if (rateCards.value.length === 0) {
    displayLoading.value = true

    if (!props.preFetchRate || !loadingRateCards.value) {
      displayLoading.value = true
      await fetchRate()

      if (rateCards.value.length) {
        scrollToRoomCard()
        cardIsOpen.value = true
      }
    }
  } else {
    cardIsOpen.value = true
    scrollToRoomCard()
  }
}

const fetchRate = async (displayError: boolean = true) => {
  loadingRateCards.value = true

  try {
    const rates = await getSelectedRoomRateCards(props.roomCardContent.productCode, currentRoom.value)

    offersList.value = rates.offers

    const dateIn = dates.value[DateMoment.START]
    const dateOut = dates.value[DateMoment.END]
    const nbNights = calculateNights(dateIn, dateOut)

    rateCards.value = rateCardMapper(
      offersList.value.filter((offer) => !offer.updatedRemaining || offer.updatedRemaining > 0),
      !!hotel.value?.expediaCompliant,
      isLogged,
      nbNights > 1,
      userPOS.countryMarket === "US"
    )

    rateError.value = false
  } catch (error) {
    const apiError = error as AxiosError<BestOffersError>
    const code = apiError?.response?.data?.code
    const status = apiError?.response?.status

    if (displayError) {
      if (status === 400 && ["SELECTED_OFFER_UNAVAILABLE", "NO_OFFER_AVAILABLE"].includes(code || "")) {
        rateError.value = generateError(ErrorTypes.BEST_OFFERS_SELECTED_OFFER_UNAVAILABLE)
      } else {
        rateError.value = generateError(ErrorTypes.GENERIC)
      }
    }
  } finally {
    loadingRateCards.value = false
    displayLoading.value = false
  }
}

const scrollToRoomCard = () => {
  if (isDesktop.value) {
    roomCardRef.value?.scrollIntoView({ behavior: "smooth", block: "start" })
  }
}

const handleGalleryClicked = () => {
  isGalleryOpen.value = true

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: "open gallery",
      bloc_name: "room bloc"
    }
  })
}

const openModal = async () => {
  isLoading.value = true
  modalIsOpen.value = true

  if (!roomCardModalContent.value) {
    const offer = offers.value.find((o) => o.id === props.offerId)
    if (!offer) return

    const accommotation = await getOfferAccommodationById(offer)
    roomCardModalContent.value = accommodationMapper(offer.product, accommotation)
  }

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: "room details",
      bloc_name: "room bloc"
    }
  })

  isLoading.value = false
}

onMounted(async () => {
  if (props.preFetchRate) {
    await fetchRate(false)
  }
})

watch(
  () => props.roomCardContent.isOpen,
  (isOpen: boolean) => {
    if (isOpen) {
      handleRoomCard()
    }
  },
  { immediate: true }
)

watch(
  () => props.preFetchRate,
  (preFetchRate: boolean) => {
    if (preFetchRate && !rateCards.value.length) {
      fetchRate()
    }
  }
)

watch(
  () => displayLoading.value,
  (displayLoading: boolean) => {
    if (!displayLoading && rateCards.value.length > 0) {
      cardIsOpen.value = true
      scrollToRoomCard()
    }
  }
)

watch(isMobile, (newVal) => {
  if (newVal) {
    cardIsOpen.value = false
  }
})
</script>

<template>
  <div ref="roomCardRef" class="Room-card">
    <UiRoomCard
      v-bind="roomCardContent"
      :is-open="cardIsOpen"
      :is-loading-rate-cards="loadingRateCards && displayLoading"
      :eager-loading="eagerLoading"
      @ui-room-card-details-button::click="openModal"
      @ui-room-card-select-button::click="handleRoomCard"
      @ui-room-card-close-button::click="cardIsOpen = false"
      @ui-room-card-gallery-button::click="handleGalleryClicked"
    >
      <template #rate-cards>
        <div class="Room-card__rate-cards-title exp-heading-04">
          {{ $t("components.stay_view.room_card.rate_title") }}
        </div>

        <RateCard
          v-for="(rateCard, index) in rateCards"
          :key="rateCard.content.rateCardId"
          :class="{ 'Rate-card__container--first': index === 0 }"
          :class-code="classCode || ''"
          :content="rateCard.content"
          :api-offer="rateCard.apiOffer"
          :rate-code="rateCard.apiOffer.rate.id"
          :room="modelValue"
          :product-code="roomCardContent.productCode"
          :rate-id="rateCard.rateId"
          :eager-loading="eagerLoading"
          :loading-rate-id="loadingRateId"
          @choose-offer="$emit('choose-offer', $event)"
        />
      </template>
    </UiRoomCard>

    <UiGallery
      v-if="roomCardContent.gallery.medias"
      :is-open="isGalleryOpen"
      :images="roomCardContent.gallery.medias"
      :title="$t('components.stay_view.room_card.gallery')"
      @ui-gallery::close="isGalleryOpen = false"
    />

    <UiRoomDetailsModal
      :is-loading="isLoading"
      :is-open="modalIsOpen"
      :room-details-modal-content="roomCardModalContent"
      @room-details-modal::close="modalIsOpen = false"
    />
  </div>
</template>

<style lang="scss" scoped>
@use "./RoomCard.scss";
</style>
