import { UiRoomCardProps } from "design-system"

export interface RoomCardProps {
  /**
   * Current clicked rate id (used for loading)
   */
  loadingRateId?: string
  /**
   * Unique identifier of the offer
   */
  offerId: string
  /**
   * Unique identifier of the rate
   */
  rateCode: string
  /**
   * Id of the room class
   */
  classCode?: string
  /**
   * Properties for the room card, including details like amenities, gallery, image, pricing, and title.
   */
  roomCardContent: UiRoomCardProps
  /**
   * Wether the image loading policy should be eager
   */
  eagerLoading?: boolean
  /**
   * Wether the rate should be pre-fetched
   */
  preFetchRate?: boolean
}
