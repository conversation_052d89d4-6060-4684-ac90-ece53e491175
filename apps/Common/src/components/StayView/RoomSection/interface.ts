import { MatchedPair } from "../../../services/offer/offer.interface"

export interface RoomSectionProps {
  /**
   * Current clicked rate id (used for loading)
   */
  loadingRateId?: string
  /**
   * List of mapped offer/accommodation pairs associated with this section.
   */
  pairs: MatchedPair[]
  /**
   * Displayable title for this room section.
   */
  title: string
  /**
   * Class code of the section
   */
  classCode?: string
  /**
   * Whether to show a divider below this section.
   */
  hasDivider: boolean
  /**
   * Wether the first room card image loading policy should be eager
   */
  eagerLoading?: boolean
}
