<script setup lang="ts">
import { DividerDirection, UiButtonType, UiDivider, UiIcon, UiLink, UiLinkVariant } from "design-system"
import { computed, watch } from "vue"
import { useGA4Event, useSearch } from "../../../composables"
import { EventNameEnum } from "../../../global/enums"
import { Offer } from "../../../services/offer/offer.types"
import RoomCard from "../RoomCard/RoomCard.vue"
import { RoomSectionProps } from "./interface"
import { useI18n } from "vue-i18n"

type Emits = {
  (
    event: "choose-offer",
    payload: { apiOffer: Offer; classCode?: string; rateId: string; productCode: string; rateCode: string }
  ): void
}

const props = defineProps<RoomSectionProps>()
defineEmits<Emits>()

const { pushGA4Event } = useGA4Event()
const { rooms } = useSearch()
const { t } = useI18n()

const isExpanded = defineModel<boolean>("expanded")

function toggleExpand() {
  isExpanded.value = !isExpanded.value

  if (!isExpanded.value) {
    pushGA4Event("step2", "select a room", {
      eventName: EventNameEnum.bloc_interact,
      event_data: {
        bloc_interaction: "see other type of rooms",
        bloc_name: "room bloc"
      }
    })
  }
}

const chevronIcon = computed(() => (isExpanded.value ? "chevronUp" : "chevronDown"))

const buttonLabel = computed(() => {
  return isExpanded.value
    ? t("components.stay_view.room_section.reduce_label")
    : t("components.stay_view.room_section.other_room_label", { count: props.pairs.length - 1 })
})

const currentRoom = computed({
  get() {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateId)
    return rooms.value[currentIndex]
  },
  set(newValue) {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateId)
    rooms.value[currentIndex] = newValue
  }
})

watch(
  currentRoom,
  (_newCurrentRoom, oldCurrentRoom) => {
    if (oldCurrentRoom) return
  },
  {
    once: true
  }
)
</script>

<template>
  <div class="Room-section">
    <div class="Room-section__content">
      <div class="Room-section__title-content">
        <h2 class="Room-section__title">
          {{ title }}

          <UiIcon class="Room-section__ellipsis" name="ellipsis" />

          <span class="Room-section__subtitle">
            {{ $t("components.stay_view.room_section.subtitle", { count: pairs.length }) }}
          </span>
        </h2>
      </div>

      <RoomCard
        v-if="pairs.length > 0"
        v-model="currentRoom"
        :offer-id="pairs[0].offerMapped.id"
        :rate-code="pairs[0].offerMapped.rateId"
        :class-code="classCode"
        :room-card-content="pairs[0].offerMapped.roomCard"
        :eager-loading="eagerLoading"
        :loading-rate-id="loadingRateId"
        :pre-fetch-rate="true"
        @choose-offer="$emit('choose-offer', $event)"
      />

      <ul v-show="isExpanded" class="Room-section__room-card-content">
        <li v-for="roomCard in pairs.slice(1)" :key="roomCard.offerMapped.id">
          <RoomCard
            v-model="currentRoom"
            :offer-id="roomCard.offerMapped.id"
            :rate-code="roomCard.offerMapped.rateId"
            :room-card-content="roomCard.offerMapped.roomCard"
            :loading-rate-id="loadingRateId"
            :pre-fetch-rate="isExpanded"
            @choose-offer="$emit('choose-offer', $event)"
          />
        </li>
      </ul>

      <UiLink
        v-if="pairs.length > 1"
        class="Room-section__button"
        :text="buttonLabel"
        :variant="UiLinkVariant.NEUTRAL"
        :type="UiButtonType.BUTTON"
        uppercase
        @click="toggleExpand"
      >
        <template #append-content>
          <UiIcon class="Room-section__button--icon" :name="chevronIcon" />
        </template>
      </UiLink>
    </div>

    <UiDivider v-if="hasDivider" :direction="DividerDirection.HORIZONTAL" />
  </div>
</template>

<style lang="scss" scoped>
@use "./RoomSection.scss";
</style>
