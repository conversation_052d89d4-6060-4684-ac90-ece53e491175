import { RoomType, UiRateCardProps } from "design-system"
import { Offer } from "../../../services/offer/offer.types"

export interface RateCardProps {
  /**
   * Properties for the rate card, including pricing, discounts, and additional offers.
   */
  content: UiRateCardProps
  /**
   * The offer object from the API
   */
  apiOffer: Offer
  /**
   * Identifier of the rate
   */
  rateId: string
  /**
   * The product code of the room
   */
  productCode: string
  /**
   * The rate code of the rate
   */
  rateCode: string
  /**
   * The room type of the room
   */
  room?: RoomType
  /**
   * The class code of the room section
   */
  classCode?: string
  /**
   * Current clicked rate id (used for loading)
   */
  loadingRateId?: string
}
