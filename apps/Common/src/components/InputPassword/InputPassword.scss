@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Input-password {
  &__criterias {
    @include text.lbf-text("caption-01");

    .Icon {
      width: 1.8rem;
      height: 1.8rem;
      color: map.get(colors.$neutral, "200");
    }
  }

  &__additional-title {
    color: map.get(colors.$caviarBlack, "700");
    margin-block-end: map.get(spaces.$sizes, "4");
  }

  &__criterias-list {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }

  &__criteria {
    display: flex;
    align-items: center;
    color: map.get(colors.$caviarBlack, "700");
    gap: map.get(spaces.$sizes, "4");

    &--success {
      .Icon {
        color: map.get(colors.$green, "500");
      }
    }

    &--error {
      .Icon {
        color: map.get(colors.$red, "500");
      }
    }
  }
}
