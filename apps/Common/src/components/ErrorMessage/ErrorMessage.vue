<script setup lang="ts">
import { CustomError, errorMapping } from "../../composables/useHTTPErrors"
import { UiMessage, UiMessageVariation } from "design-system"
import { computed } from "vue"
import { getGlobalConfig } from "../../global/config"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useI18n } from "vue-i18n"

const props = defineProps<{ error: CustomError }>()
const { t, locale } = useI18n()
const config = getGlobalConfig()

const displayedError = computed(() => {
  return errorMapping[props.error.code]
})

const computeDestination = (to: string) => {
  if (to === "homepage") {
    return replaceUriParams(config.legacy.homePage, "", locale.value)
  }

  if (to === "login") {
    return replaceUriParams(config.oidc.url, window.location.origin, locale.value)
  }

  return ""
}

const links = computed(() =>
  (displayedError.value.links ?? [])
    .map((link) => ({
      href: computeDestination(link.to),
      target: link.target ?? "_self",
      text: t(link.text)
    }))
    .filter((link) => link.href)
)
</script>

<template>
  <UiMessage
    class="ErrorMessage__container"
    :title="$te(displayedError.message) ? $t(displayedError.message, error.params) : $t('errors.generic.title')"
    :description="
      $te(displayedError.description) ? $t(displayedError.description, error.params) : $t('errors.generic.description')
    "
    :variation="UiMessageVariation.DANGER"
    :links="links"
  />
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/spaces";

.ErrorMessage__container {
  margin-top: map.get(spaces.$sizes, "11");
  margin-bottom: map.get(spaces.$sizes, "11");
}
</style>
