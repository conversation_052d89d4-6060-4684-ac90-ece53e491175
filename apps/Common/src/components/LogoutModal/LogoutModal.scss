@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-utilities/mq";

.Logout-modal {
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: map.get(spaces.$sizes, "7");
    padding: 0 map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      padding: 0 map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "7");
    }
  }

  &__text {
    text-align: center;
    color: map.get(colors.$basics, "black");
  }

  &__buttons {
    display: flex;
    gap: map.get(spaces.$sizes, "7");
    margin-bottom: map.get(spaces.$sizes, "8");

    @include mq.media(">=small") {
      margin-bottom: map.get(spaces.$sizes, "9");
    }

    :deep(button span) {
      padding-inline: map.get(spaces.$sizes, "3");
    }
  }
}
