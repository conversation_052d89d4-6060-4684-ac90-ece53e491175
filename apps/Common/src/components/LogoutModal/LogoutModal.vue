<script setup lang="ts">
import { ModalSize, Transition, UiButton, UiButtonVariation, UiModal } from "design-system"
import { useModal } from "../../composables"

const emit = defineEmits(["LogoutModal::logout"])

const { isModalOpen, closeModal } = useModal("logout")

const handleLogOut = () => {
  emit("LogoutModal::logout")
  closeModal()
}
</script>

<template>
  <UiModal
    with-overlay
    class="Logout-modal"
    :is-open="isModalOpen"
    :transition="Transition.FADE"
    :modal-size="ModalSize.SMALL"
    mobile-min-height
    @ui-modal::close="closeModal"
  >
    <div class="Logout-modal__container">
      <p class="Logout-modal__text body-01-strong">
        {{ $t("components.logout_modal.are_you_sure") }}
      </p>
      <div class="Logout-modal__buttons">
        <UiButton :text="$t('global.go_back')" :variation="UiButtonVariation.TERTIARY" @click="closeModal" />
        <UiButton :text="$t('global.log_out')" :variation="UiButtonVariation.SECONDARY" @click="handleLogOut" />
      </div>
    </div>
  </UiModal>
</template>

<style lang="scss" scoped>
@use "./LogoutModal.scss";
</style>
