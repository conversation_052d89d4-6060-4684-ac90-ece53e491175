<script setup lang="ts">
import { PageTitleProps } from "./interface"
import { useCurrentWindowSize } from "../../composables"

defineProps<PageTitleProps>()

const { isDesktop } = useCurrentWindowSize()
</script>

<template>
  <h1 class="page-title">
    <span>
      <span :class="!isDesktop ? 'exp-heading-04-alt' : 'exp-heading-03-alt'">
        {{ left }}
      </span>
      <span :class="!isDesktop ? 'exp-heading-04' : 'exp-heading-03'">
        {{ middle }}
      </span>
      <span :class="!isDesktop ? 'exp-heading-04-alt' : 'exp-heading-03-alt'">
        {{ right }}
      </span>
    </span>

    <span v-if="down" :class="!isDesktop ? 'exp-heading-04-alt' : 'exp-heading-03-alt'">
      {{ down }}
    </span>
  </h1>
</template>

<style lang="scss" scoped>
@use "./PageTitle.scss";
</style>
