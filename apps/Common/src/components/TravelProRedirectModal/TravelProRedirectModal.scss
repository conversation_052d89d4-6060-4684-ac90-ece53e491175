@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Travel-pro-redirect-modal {
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "10");
    gap: map.get(spaces.$sizes, "8");

    @include mq.media(">=small") {
      gap: map.get(spaces.$sizes, "10");
      padding: 0 map.get(spaces.$sizes, "7") 7.2rem;
    }
  }

  &__logos {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    max-width: 80rem;
    gap: map.get(spaces.$sizes, "8");
    padding-block-start: map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      padding-top: map.get(spaces.$sizes, "7");
    }
  }

  &__brand {
    width: 13rem;
    height: 4rem;

    @include mq.media(">=small") {
      width: 18.3rem;
      height: 5.6rem;
    }
  }

  &__travel {
    width: 14.9rem;
    height: 3.2rem;

    @include mq.media(">=small") {
      width: 19.6rem;
      height: 4.2rem;
    }
  }

  &__all {
    width: 8rem;
    height: 4rem;

    @include mq.media(">=small") {
      width: 11.1rem;
      height: 5.6rem;
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
  }

  &__infos {
    @include text.lbf-text("exp-subheading-02");
    text-align: center;
    color: map.get(colors.$caviarBlack, "700");
  }

  &__link {
    width: 100%;
    min-height: map.get(spaces.$sizes, "10");
    padding: map.get(spaces.$sizes, "5") map.get(spaces.$sizes, "7");
    border: 0;
    border-radius: 2px;
    background-color: map.get(colors.$caviarBlack, "800");

    :deep(.ads-link) {
      @include text.lbf-text("body-01-uppercase");
      text-decoration: none;
      color: map.get(colors.$basics, "white");
    }

    &:active {
      &:not(:disabled) {
        background-color: map.get(colors.$caviarBlack, "600");
      }
    }

    @include mq.media(">medium") {
      &:hover {
        &:not(:disabled, :active) {
          background-color: map.get(colors.$caviarBlack, "700");
          color: map.get(colors.$white, "90");
        }
      }
    }
  }
}
