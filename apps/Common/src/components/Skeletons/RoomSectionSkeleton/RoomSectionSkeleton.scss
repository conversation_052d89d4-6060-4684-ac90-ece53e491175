@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-utilities/mq";

.Room-section-skeleton {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "7");

  @include mq.media(">=small") {
    gap: map.get(spaces.$sizes, "8");
  }

  &__more {
    align-self: center;
    @include mq.media("<small") {
      margin-bottom: map.get(spaces.$sizes, "5");
    }

    @include mq.media(">=large") {
      margin-bottom: map.get(spaces.$sizes, "6");
    }
  }

  &__divider {
    display: none;

    @include mq.media(">=small") {
      display: block;
    }
  }
}
