<script setup lang="ts">
import { Divide<PERSON><PERSON>ire<PERSON>, UiDivider, UiRoomCardSkeleton, UiSkeleton } from "design-system"
import RoomSectionTitleSkeleton from "../RoomSectionTitleSkeleton/RoomSectionTitleSkeleton.vue"

const skeletons = 3
</script>
<template>
  <div class="Room-section-skeleton">
    <template v-for="index in skeletons" :key="index">
      <RoomSectionTitleSkeleton class="Room-section-skeleton__title" />

      <UiRoomCardSkeleton />

      <UiSkeleton width="24.5rem" height="2.2rem" border-radius=".3rem" class="Room-section-skeleton__more" />

      <UiDivider
        v-if="index !== skeletons"
        class="Room-section-skeleton__divider"
        :direction="DividerDirection.HORIZONTAL"
      />
    </template>
  </div>
</template>

<style lang="scss" scoped>
@use "./RoomSectionSkeleton.scss";
</style>
