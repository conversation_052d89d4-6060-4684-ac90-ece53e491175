<script setup lang="ts">
import { UiSkeleton } from "design-system"
</script>
<template>
  <div class="Room-section-title-skeleton">
    <UiSkeleton width="80%" height="2rem" border-radius=".3rem" class="Room-section-title-skeleton__main-title py-2" />
    <UiSkeleton width="30%" height="1.6rem" border-radius=".3rem" class="Room-section-title-skeleton__sub-title" />
  </div>
</template>

<style lang="scss" scoped>
@use "./RoomSectionTitleSkeleton.scss";
</style>
