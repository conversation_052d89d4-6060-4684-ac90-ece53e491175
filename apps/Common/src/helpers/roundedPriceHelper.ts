export const roundedPriceHelper = (amount: number, locale: string, currency: string): string => {
  const roundedAmount = Math.ceil(amount)

  if (locale) {
    const formattedPrice = new Intl.NumberFormat(locale, {
      currency,
      maximumFractionDigits: 0,
      minimumFractionDigits: 0,
      style: "currency"
    }).format(roundedAmount)

    if (formattedPrice.includes(currency)) {
      return formattedPrice.replace(currency, "").trim()
    }

    return formattedPrice
  }

  return roundedAmount.toString()
}
