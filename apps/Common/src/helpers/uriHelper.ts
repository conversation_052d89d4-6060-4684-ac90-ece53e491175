import { getGlobalConfig } from "../global/config"

export function replaceUriParams(uri: string, origin: string, locale: string) {
  const config = getGlobalConfig()
  if (uri?.includes("{BASE_URI}")) {
    uri = uri.replace("{BASE_URI}", origin)
  }

  if (uri?.includes("{locale}")) {
    uri = uri.replace("{locale}", locale)
  }

  if (uri?.includes("{siteCode}")) {
    uri = uri.replace("{siteCode}", config.siteCode)
  }

  return uri
}

export function encodeState(stateValues: {
  redirectUrl: string
  baseUrl: string
  userLang: string
  cookieDomain: string
}): string {
  return btoa(JSON.stringify(stateValues))
}

export function buildOidcUri(
  uri: string,
  origin: string,
  ui: string,
  logo: string,
  locale: string,
  clientId: string,
  params: string,
  register: boolean = false,
  state: { redirectUrl: string; baseUrl: string; userLang: string; cookieDomain: string }
) {
  const pingUri = new URL(uri)
  const brandUi = ui.replace("{OIDC_LOGO_PATH}", logo)
  const oidcParam = replaceUriParams(params, origin, locale)
    .replace("{BRAND_UI}", brandUi)
    .replace("{LOGIN_CLIENT_ID}", clientId)

  pingUri.search = oidcParam

  if (register) {
    pingUri.searchParams.set("accorregister", "true")
  }

  if (state) {
    const encodedState = encodeState(state)
    pingUri.searchParams.set("state", encodedState)
  }

  return pingUri.toString()
}
