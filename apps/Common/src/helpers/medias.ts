import { ApiMediaFormat } from "../services/hotels/hotels.types"
import { MediaFormat } from "../services/offer/offer.types"

export interface FormattedRoomDetailModalMedia {
  format: string
  path: string
}

export const sortMediasByResolution = (medias: ApiMediaFormat[] | MediaFormat[] | FormattedRoomDetailModalMedia[]) => {
  return medias.sort((formatA, formatB) => {
    const widthA = Number(formatA.format.split("x")[0])
    const widthB = Number(formatB.format.split("x")[0])

    return widthA > widthB ? -1 : 1
  })
}
