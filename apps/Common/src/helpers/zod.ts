import { InputPasswordError } from "../components/InputPassword/enum"
import { z } from "zod"

export const passwordSchema = z.coerce
  .string()
  .min(8, InputPasswordError.TOO_SHORT_OR_LONG)
  .max(20, InputPasswordError.TOO_SHORT_OR_LONG)
  .regex(/[a-z]/g, InputPasswordError.NO_LOWERCASE)
  .regex(/[A-Z]/g, InputPasswordError.NO_UPPERCASE)
  .regex(/[0-9!@#$%^&*(),.?":{}|<>_\-+=[\]\\;/`~'€£¥]/g, InputPasswordError.NO_NUMBER_OR_SYMBOL)
