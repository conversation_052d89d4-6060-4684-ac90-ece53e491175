import { ApiIdentification, ApiUserResponse } from "./users.types"
import { SPECIAL_RATES_MAPPING, SpecialRates } from "design-system"
import { Semaphores, useApiSemaphore } from "../../composables/useApiSemaphore"
import { useApi<PERSON>anager, useSearch } from "../../composables"
import { AccorApiEndpoints } from "../../global/consts"
import { Identification } from "../../composables/useIdentificationPlainText"
import { User } from "@stores/user/interface"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"

import { useUserStore } from "@stores/user/user"

export async function getUserIdentification(currentIdentification: Identification): Promise<Identification> {
  const { apiManager } = useApiManager()
  const { specialRate, promoCode } = useSearch()
  const { loggedUser } = useUserStore()
  const cookies = useCookies()

  const identificationData = {
    identification: currentIdentification
  }

  const identificationId = currentIdentification.identificationId

  let offers: { code: string; type: "PROMOTIONAL" }[] = []

  if (specialRate.value === SpecialRates.PROMO_CODE && promoCode.value) {
    offers = promoCode.value.split(",").map((code) => ({ code, type: "PROMOTIONAL" }))
  } else {
    const value = SPECIAL_RATES_MAPPING[specialRate.value]
    if (value) {
      const values = Array.isArray(value) ? value : [value]
      offers = values.map((value) => ({
        code: value,
        type: "PROMOTIONAL"
      }))
    }
  }

  const merchantId = cookies.get("MerchantId")
  const sourceId = cookies.get("SourceId")
  const accorref = cookies.get("accorref")

  identificationData.identification = {
    ...identificationData.identification,
    ...(merchantId || sourceId
      ? {
          affiliation: {
            merchantId,
            sourceId
          }
        }
      : {}),
    ...(offers && offers.length > 0
      ? {
          offerPreference: {
            offer: offers,
            regularOfferAllowed: true
          }
        }
      : {}),
    booking: null,
    cardNumber: loggedUser.cardNumber,
    cardType: loggedUser.loyaltyCardType,
    identificationToken: null, //not needed on payload
    referer: accorref
  }

  // Remove null or undefined keys at top level
  const identificationCleanData = Object.fromEntries(
    Object.entries(identificationData.identification).filter(([, value]) => value !== null && value !== undefined)
  )

  const identificationPayload = {
    identification: identificationCleanData
  }

  if (identificationId !== null) {
    const updateIdentification = `${AccorApiEndpoints.identification}/${identificationId}`
    const { data: response, headers } = await apiManager.put<ApiIdentification<Identification>>(
      updateIdentification,
      identificationPayload
    )
    return { ...response.identification, identificationToken: headers["identification-token"] }
  } else {
    const { data: response, headers } = await apiManager.post<ApiIdentification<Identification>>(
      `${AccorApiEndpoints.identification}/plaintext`,
      identificationPayload
    )
    return { ...response.identification, identificationToken: headers["identification-token"] }
  }
}

export async function getUserDetails(): Promise<void> {
  const { setSemaphore } = useApiSemaphore()
  const { apiManager } = useApiManager()
  const { updateInitialUser } = useUserStore()

  const endPoint = AccorApiEndpoints.contactMe
  const fields = [
    "id",
    "contactMediums.contactMedium",
    "individual.individualName",
    "individual.individualContracts",
    "individual.isLoyaltyMember",
    "loyalty.balances.nbPoints",
    "loyalty.balances.pointsEarnedOnTierUpdate",
    "loyalty.balances.nightsSpentOnTierUpdate",
    "loyalty.loyaltyCards.card.cardNumber",
    "loyalty.loyaltyCards.card.cardProduct.cardCodeTARS",
    "loyalty.loyaltyCards.card.cardProduct.productTier",
    "loyalty.loyaltyCards.card.isLastActiveCard",
    "loyalty.memberInfo.fullEnrolmentDate"
  ]

  const filter = "loyalty.loyaltyCards.card:isLastActiveCard=true"

  const params = {
    fields: fields.join(","),
    filter: filter
  }
  try {
    setSemaphore(Semaphores.userDetails, true)

    const response = await apiManager.getWithAccessToken<ApiUserResponse>(endPoint, { params })

    const lastActiveCard = response.loyalty.loyaltyCards?.card.find((card) => card.isLastActiveCard)

    const primaryEmailContactMedium = response.contactMediums.contactMedium.find(
      (contact) => contact.emailContact?.isPrimary
    )

    const user: User = {
      cardNumber: lastActiveCard?.cardNumber || null,
      city: response.contactMediums.contactMedium[0].postalAddress.city || "",
      countryCode: response.contactMediums.contactMedium[0].postalAddress.countryCode || "",
      email: primaryEmailContactMedium?.emailContact.email || "",
      firstName: response.individual.individualName.firstName,
      id: response.id,
      isLoyaltyMember: response.individual.isLoyaltyMember,
      lastName: response.individual.individualName.lastName,
      loyaltyCardType: lastActiveCard?.cardProduct.cardCodeTARS || null,
      nightValue: response.loyalty.balances?.nightsSpentOnTierUpdate,
      rewardPointsValue: response.loyalty.balances?.nbPoints,
      statusName: lastActiveCard?.cardProduct.productTier || "",
      statusPointsValue: response.loyalty.balances?.pointsEarnedOnTierUpdate,
      statusSince: response.loyalty.memberInfo?.fullEnrolmentDate
    }

    updateInitialUser(user)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error fetching user details:", error)
  } finally {
    setSemaphore(Semaphores.userDetails, false)
  }
}
