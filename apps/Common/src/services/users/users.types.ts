export interface ApiIdentification<T> {
  identification: T
  message: string
  success: boolean
}

export interface ApiUserResponse {
  id: string
  contactMediums: {
    contactMedium: ContactMedium[]
  }
  individual: {
    individualName: {
      lastName: string
      firstName: string
    }
    isLoyaltyMember: boolean
  }
  loyalty: {
    balances?: {
      nightsSpentOnTierUpdate: number
      pointsEarnedOnTierUpdate: number
      nbPoints: number
    }
    memberInfo?: {
      fullEnrolmentDate: string
    }
    loyaltyCards?: {
      card: Card[]
    }
  }
}

interface Card {
  cardNumber: string
  cardProduct: {
    cardCodeTARS: string
    productTier: string
  }
  isLastActiveCard: boolean
}

interface ContactMedium {
  emailContact: {
    email: string
    isPrimary: boolean
  }
  postalAddress: {
    city: string
    countryCode: string
  }
}
