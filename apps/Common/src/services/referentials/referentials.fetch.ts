import { Country, ReferentialList, ReferentialListKey } from "./referentials.type"
import { AccorApiEndpoints } from "../../global/consts"
import { cachedApi } from "../cache"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchCountries(): Promise<Country[]> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.get<Country[]>(AccorApiEndpoints.countries)

    return response
  }, AccorApiEndpoints.countries)
}

export async function fetchLists<T extends ReferentialListKey = ReferentialListKey>(
  expands: T[]
): Promise<Pick<ReferentialList, T>> {
  return cachedApi(
    async () => {
      const { apiManager } = useApiManager()

      const { data: response } = await apiManager.get<Pick<ReferentialList, T>>(AccorApiEndpoints.referentialList, {
        params: { expand: expands }
      })

      return response
    },
    AccorApiEndpoints.referentialList,
    { keyParts: expands }
  )
}
