export interface Country {
  alpha2Code: string
  geoCode: string
  name: string
  callingCodes: string[]
  nationality: {
    name: string
    isoCode: string
    geoCode: string
  }
  idCardRequired: boolean
  states: {
    code: string
    codeGeo: string
    name: string
  }[]
}

export type ReferentialList = {
  calendar: ReferentialListCalendar
  "room-occupancy": ReferentialListOccupancy
}

export type ReferentialListKey = keyof ReferentialList

export type ReferentialListCalendar = {
  maxDate: number
  maxLengthOfStay: number
  minLengthOfStay: number
}

export type ReferentialListOccupancy = {
  maxRoom: number
  maxPax: number
  maxAdult: number
  maxChild: number
  maxChildAge: number
}
