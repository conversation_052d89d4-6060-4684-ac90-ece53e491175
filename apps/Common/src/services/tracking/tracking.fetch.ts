import { AccorApiEndpoints } from "../../global/consts"
import { ApiCommerceTracking } from "./tracking.types"
import { getGlobalConfig } from "../../global/config"
import { useApiManager } from "../../composables/useApiManager"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"

function getQueryParamInsensitive(paramName: string): string | undefined {
  const searchParams = new URLSearchParams(window.location.search)

  for (const [queryKey, queryValue] of searchParams.entries()) {
    if (queryKey.toLowerCase() === paramName.toLowerCase()) {
      return queryValue
    }
  }
  return undefined
}

export async function getCommerceTracking(appDomain: string) {
  const { apiManager } = useApiManager()
  const cookies = useCookies()

  const qpSourceId = getQueryParamInsensitive("sourceId")
  const qpMerchantId = getQueryParamInsensitive("merchantId")

  const sourceId = qpSourceId || cookies.get("SourceId")
  const merchantId = qpMerchantId || cookies.get("MerchantId")
  const affcookie = cookies.get("affcookie")
  const config = getGlobalConfig()

  const body = {
    affcookie,
    currentMerchantId: merchantId,
    currentSourceId: sourceId,
    siteCode: config.siteCode
  }

  const { data: response } = await apiManager.post<ApiCommerceTracking>(AccorApiEndpoints.commerceTracking, {
    body
  })

  if (response.outputBestVisit) {
    cookies.set("MerchantId", response.outputBestVisit.merchantId, { domain: appDomain, path: "/" })
    cookies.set("SourceId", response.outputBestVisit.sourceId, { domain: appDomain, path: "/" })
  }

  return response
}
