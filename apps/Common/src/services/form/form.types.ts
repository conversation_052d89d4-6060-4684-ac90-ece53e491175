export interface ApiBasketSummary {
  operationWarnings: ApiOperationWarning[]
  basket: ApiBasketSummaryBasket
  displayFields: {
    [field: string]: ApiBasketDisplayField
  }
  isRussianLawContext: boolean
  referentials: {
    countries: ApiBasketSummaryReferentialCountry[]
    nationalitiesWithConsent: string[]
    newsletterOptions: { code: string; label: string }[]
    purposesOfStay: { code: string; label: string }[]
    titles: { code: string; label: string; shortLabel: string }[]
  }
}

export interface ApiOperationWarning {
  code: string
  message: string
}

export interface ApiBasketSummaryBasket {
  currency: string
  currencySymbol: string
  hotel: ApiBasketSummaryHotel
  hotelCurrency: string
  hotelCurrencySymbol: string
  id: string
  itemTotalAmountMode: string
  items: ApiBasketSummaryBasketItem[]
  pricing: {
    formattedTotalAmount: string
    formattedTotalAmountHotelCurrency: string
    totalAmount: number
    totalAmountHotelCurrency: number
  }
  request: {
    adults: number
    children: number
    dateIn: string
    dateOut: string
    formattedDateIn: string
    formattedDateOut: string
    lengthOfStay: {
      unit: string
      value: number
    }
  }
  simplifiedCanpol: {
    code: string
    label: string
  }
}

export interface ApiBasketSummaryHotel {
  brand: { code: string; label: string }
  checkInHour: string
  checkoutOutHour: string
  formattedCheckInHour: string
  formattedCheckOutHour: string
  id: string
  localRating: number
  media: {
    category: string
    formats: {
      format: string
      lastUpdate: string
      url: string
    }[]
    type: string
  }
  name: string
}

export interface ApiBasketSummaryBasketItem {
  adults: number
  beneficiary: unknown // todo - to type
  children: number
  childrenAges: number[]
  detail: ApiBasketSummaryBasketItemDetail
  formattedTotalAmount: string
  id: string
  label: string
  offerId: string
  totalAmount: number
}

export interface ApiBasketSummaryBasketItemDetail {
  categories: { code: string; description: string }[]
  fees: {
    amount: number
    emphasizedBreakdown: { amount: number; formattedAmount: string; label: string }[]
    formattedAmount: string
    label: string
  }
  formattedMainProductAmount: string
  formattedTotalOptionsAmount: string
  guarantiedPreferences: unknown[] // todo - to type
  mainProductAmount: number
  mainProductId: string
  mainProductLabel: string
  nonGuarantiedPreferences: unknown[] // todo - to type
  options: [] // todo - to type
  rateId: string
  rateLabel: string
  taxes: { amount: number; formattedAmount: string; label: string }
  totalOptionsAmount: number
  totalOptionsAmountLabel: string
}

export interface ApiBasketDisplayField {
  emptyErrorMessage: string
  initialValue: string
  invalidErrorMessage: string
  isInitiallyDisplayed: boolean
  isMandatory: boolean
  isReadOnly: boolean
  label: string
  maxLength?: number
  validationRule?: string
}

interface ApiBasketSummaryReferentialCountry {
  code: string
  label: string
  nationalityLabel: string
  phonePrefix: { code: string; label: string }
  state: { code: string; label: string }[]
}
