import { ApiBasketSummary } from "./form.types"
import { getGlobalConfig } from "../../global/config"
import { useApiManager } from "../../composables"

export async function getBasketSummary(basketId: string, currency: string, countryMarket: string) {
  const { apiManager } = useApiManager()
  const config = getGlobalConfig()

  const url = new URL(
    `${config.apiUrl}basket/webdirectchannels/v1/baskets/${basketId}/summary`, //TODO : remove this hardcoded path
    window.location.origin
  )
  url.searchParams.set("currency", currency)
  url.searchParams.set("countryMarket", countryMarket)

  const { data: response } = await apiManager.get<ApiBasketSummary>(url.toString())

  return response
}
