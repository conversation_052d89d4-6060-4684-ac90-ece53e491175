import { BrandFormSectionRow, FormField } from "@shared/types/FormStructure"
import { ApiBasketSummary } from "./form.types"
import { FormFieldKey } from "../../../../../shared/constants/form"
import { ZodSchema } from "zod"

type Constraint = (schema: ZodSchema, basketSummary: ApiBasketSummary) => ZodSchema
type FieldConstraints = Record<FormFieldKey, Constraint>

export const fieldConstraints: Partial<FieldConstraints> = {
  country(schema, basketSummary) {
    const values = basketSummary.referentials.countries.map((country) => country.code)
    return schema.refine((value) => {
      if (schema.isOptional() && !value) return true

      return values.includes(value)
    })
  },
  mainBeneficiaryTitle(schema, basketSummary) {
    const values = basketSummary.referentials.titles.map((title) => title.code)
    return schema.refine((value) => {
      if (schema.isOptional() && !value) return true

      return values.includes(value)
    })
  },
  phoneCountryCode(schema, basketSummary) {
    const values = basketSummary.referentials.countries.map((country) => country.code)
    return schema.refine((value) => {
      if (schema.isOptional() && !value) return true

      return values.includes(value)
    })
  },
  purposeOfStay(schema, basketSummary) {
    const values = basketSummary.referentials.purposesOfStay.map((purpose) => purpose.code)
    return schema.refine((value) => {
      if (schema.isOptional() && !value) return true

      return values.includes(value)
    })
  }
}

type Customization = (formField: FormField, brandField: BrandFormSectionRow, basketSummary: ApiBasketSummary) => void
type FieldCustomization = Record<FormFieldKey, Customization>

export const fieldCustomizations: Partial<FieldCustomization> = {
  country(formField, brandField, basketSummary) {
    formField.options = {
      inputType: "select",
      options: basketSummary.referentials.countries.map((country) => ({ label: country.label, value: country.code }))
    }
  },
  mainBeneficiaryTitle(formField, brandField, basketSummary) {
    formField.options = {
      inputType: "select",
      options: basketSummary.referentials.titles.map((title) => ({ label: title.label, value: title.code }))
    }
  },
  messageToHotel(formField) {
    formField.options = {
      inputType: "text"
    }
  },
  nationality(formField, brandField, basketSummary) {
    formField.options = {
      inputType: "select",
      options: basketSummary.referentials.countries.map((country) => ({ label: country.label, value: country.code }))
    }
  },
  phoneCountryCode(formField, brandField, basketSummary) {
    formField.options = {
      inputType: "select",
      options: basketSummary.referentials.countries.map((country) => ({ label: country.label, value: country.code }))
    }
  },
  purposeOfStay(formField, brandField, basketSummary) {
    formField.options = {
      inputType: "radios",
      options: basketSummary.referentials.purposesOfStay.map((pos) => ({ label: pos.label, value: pos.code }))
    }
  }
}
