import { ApiBasketDisplayField, ApiBasketSummary } from "./form.types"
import { BrandFormSection, FormField, FormStructureField } from "@shared/types/FormStructure"
import { Schema, ZodString, ZodType, z } from "zod"
import { fieldConstraints, fieldCustomizations } from "./form.constraints"
import { FormFieldKey } from "../../../../../shared/constants/form"

export function buildSectionFieldFromBrandSection(
  brandFormSection: BrandFormSection,
  basketSummary: ApiBasketSummary
): FormStructureField[] {
  const fields: FormStructureField[] = []

  for (let i = 0; i < brandFormSection.fields.length; i++) {
    const brandField = brandFormSection.fields[i]

    const fieldRow: FormStructureField = {
      key: `${brandFormSection.key}-${i}`,
      type: brandField.type
    }

    switch (brandField.type) {
      case "text":
        fieldRow.label = brandField.title
        fieldRow.description = brandField.subtitle
        fieldRow.optional = brandField.optional
        break

      case "helper":
        fieldRow.label = brandField.label
        fieldRow.description = brandField.description
        break

      case "fields": {
        fieldRow.fields = getBasketSummaryField(brandField.keys, basketSummary).map(({ key, ...displayField }) => {
          const field: FormField = {
            displayField,
            key
          }

          if (key in fieldCustomizations) {
            fieldCustomizations[key as FormFieldKey]!(field, brandField, basketSummary)
          }

          return field
        })

        fieldRow.description = brandField.description

        break
      }

      case "accordion":
        fieldRow.label = brandField.title
        fieldRow.description = brandField.description
        fieldRow.accordionField = buildSectionFieldFromBrandSection(
          brandField as unknown as BrandFormSection,
          basketSummary
        )
        break
    }

    fields.push(fieldRow)
  }

  return fields
}

export function buildSectionSchemaFromBrandSection(
  brandFormSection: BrandFormSection,
  basketSummary: ApiBasketSummary
): Schema {
  let schema = z.object({})

  for (const brandField of brandFormSection.fields) {
    if (brandField.type !== "fields") continue

    const basketSummaryFields = getBasketSummaryField(brandField.keys, basketSummary)

    for (const field of basketSummaryFields) {
      let fieldSchema: ZodType = z.string({
        invalid_type_error: field.invalidErrorMessage,
        required_error: field.emptyErrorMessage
      })

      if (field.maxLength) {
        fieldSchema = (fieldSchema as ZodString).max(field.maxLength, field.invalidErrorMessage)
      }

      if (field.validationRule) {
        fieldSchema = (fieldSchema as ZodString).regex(new RegExp(field.validationRule), field.invalidErrorMessage)
      }
      if (!field.isMandatory) {
        fieldSchema = fieldSchema.optional()
      }

      if (field.isReadOnly) {
        fieldSchema = fieldSchema.readonly()
      }

      if (field.initialValue) {
        fieldSchema = fieldSchema.default(field.initialValue)
      }

      if (field.key in fieldConstraints) {
        fieldSchema = fieldConstraints[field.key as FormFieldKey]!(fieldSchema, basketSummary)
      }

      schema = schema.extend({ [field.key]: fieldSchema })
    }
  }

  if (basketSummary.isRussianLawContext) {
    schema = schema.extend({ russianLawContext: z.boolean() })
  }

  return schema
}

/**
 * Filters [basketSummary] fields with the field defined in the brand configuration
 */
function getBasketSummaryField(keys: string[], basketSummary: ApiBasketSummary) {
  return keys.reduce(
    (acc, key) => {
      const displayField = basketSummary.displayFields[key]

      if (!displayField || !displayField?.isInitiallyDisplayed) return acc

      acc.push({ ...displayField, key })

      return acc
    },
    [] as (ApiBasketDisplayField & { key: string })[]
  )
}
