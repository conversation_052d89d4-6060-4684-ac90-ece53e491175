import { AccorApiEndpoints } from "../../global/consts"
import { ApiGlobalFacilities } from "./facilities.types"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchGeneralInformationFacilities(hotelId: string): Promise<ApiGlobalFacilities> {
  const { apiManager } = useApiManager()

  const facilitiesEndpoint = AccorApiEndpoints.facilities(hotelId)

  const { data: response } = await apiManager.get<ApiGlobalFacilities>(facilitiesEndpoint)

  return response
}
