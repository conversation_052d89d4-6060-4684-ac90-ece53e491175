export enum KeyFeaturesCode {
  ACCESSIBILITY = "ACCESSIBILITY",
  BATHROOM_FACILITIES = "BATHROOM_FACILITIES",
  BEDDING = "BEDDING",
  MAX_PAX = "MAX_PAX",
  SURFACE_AREA = "SURFACE_AREA",
  VIEWS = "VIEWS"
}

export enum KeyFeaturesIcon {
  BED = "bedDouble",
  OCCUPANT = "occupant",
  SIZE = "size",
  VIEW = "view",
  WHEELCHAIR = "wheelchair"
}

export const KeyFeaturesOrder = [
  KeyFeaturesCode.SURFACE_AREA,
  KeyFeaturesCode.BEDDING,
  KeyFeaturesCode.MAX_PAX,
  KeyFeaturesCode.VIEWS,
  KeyFeaturesCode.ACCESSIBILITY
]
