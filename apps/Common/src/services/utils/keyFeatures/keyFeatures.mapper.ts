import { AmenitiesColor, Icon, KeyFeatureType, UiAmenityProps } from "design-system"
import { KeyFeaturesCode, KeyFeaturesIcon } from "./keyFeatures.enums"
import { Offer, OfferProduct } from "../../offer/offer.types"
import { i18n } from "../../../i18n"

const { global } = i18n

// Room card
export function roomCardKeyFeaturesMapper(
  bedding: Offer["product"]["bedding"],
  keyFeatures: Offer["product"]["keyFeatures"]
): UiAmenityProps[] {
  const featureAmenities = keyFeatures.reduce<UiAmenityProps[]>((acc, feature) => {
    const result = mapCodeToIconAndAccessibilityLabel(feature.code)

    if (result !== null) {
      acc.push({
        accessibilityIconLabel: result.accessibilityIconLabel,
        code: feature.code,
        iconColor: AmenitiesColor.CAVIAR_BLACK_700,
        iconName: result.iconName,
        label: feature.label,
        labelColor: AmenitiesColor.CAVIAR_BLACK_700
      })
    }

    return acc
  }, [])

  const beddingAmenity = getBeddingRoomCardKeyFeature(bedding)

  if (beddingAmenity !== null) {
    return [...featureAmenities, beddingAmenity]
  }

  return featureAmenities
}

export function getBeddingRoomCardKeyFeature(bedding: OfferProduct["bedding"]): UiAmenityProps | null {
  const formattedLabel = formatBeddingDetails(bedding)
  if (!formattedLabel) {
    return null
  }
  return {
    accessibilityIconLabel: global.t("components.stay_view.room_card.key_features.bed"),
    code: KeyFeaturesCode.BEDDING,
    iconColor: AmenitiesColor.CAVIAR_BLACK_700,
    iconName: KeyFeaturesIcon.BED,
    label: formattedLabel,
    labelColor: AmenitiesColor.CAVIAR_BLACK_700
  }
}

function mapCodeToIconAndAccessibilityLabel(code: string): { accessibilityIconLabel: string; iconName: Icon } | null {
  switch (code) {
    case KeyFeaturesCode.ACCESSIBILITY:
      return {
        accessibilityIconLabel: global.t("components.stay_view.room_card.key_features.accessibility"),
        iconName: KeyFeaturesIcon.WHEELCHAIR
      }

    case KeyFeaturesCode.MAX_PAX:
      return {
        accessibilityIconLabel: global.t("components.stay_view.room_card.key_features.occupant"),
        iconName: KeyFeaturesIcon.OCCUPANT
      }

    case KeyFeaturesCode.SURFACE_AREA:
      return {
        accessibilityIconLabel: global.t("components.stay_view.room_card.key_features.size"),
        iconName: KeyFeaturesIcon.SIZE
      }

    case KeyFeaturesCode.VIEWS:
      return {
        accessibilityIconLabel: global.t("components.stay_view.room_card.key_features.view"),
        iconName: KeyFeaturesIcon.VIEW
      }

    default:
      return null
  }
}

// Room Details Modal
export function roomDetailsModalKeyFeaturesMapper(
  bedding: Offer["product"]["bedding"],
  keyFeatures: Offer["product"]["keyFeatures"]
): KeyFeatureType[] {
  const keyFeatureDescription = keyFeatures.map((feature) => ({
    code: feature.code,
    description: global.t(`components.stay_view.room_details_modal.key_features.${feature.code.toLocaleLowerCase()}`),
    label: feature.label
  }))

  const formattedLabel = formatBeddingDetails(bedding)
  let beddingDescription: KeyFeatureType | null = null

  if (formattedLabel) {
    beddingDescription = {
      code: KeyFeaturesCode.BEDDING,
      description: global.t("components.stay_view.room_details_modal.key_features.bed"),
      label: formattedLabel
    }
  }

  if (beddingDescription) {
    return [...keyFeatureDescription, beddingDescription]
  }

  return keyFeatureDescription
}

function formatBeddingDetails(bedding: OfferProduct["bedding"]): string {
  return bedding?.details.map((detail) => `${detail.quantity} ${detail.label}`).join(", ")
}
