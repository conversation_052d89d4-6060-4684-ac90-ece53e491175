import { Categories, PricingType, UiPricingSectionProps } from "design-system"
import { CountryMarket } from "../../../services/pos/pos.enums"
import { Offer } from "../../offer/offer.types"
import { PricingCategory } from "../../../services/offer/offer.enums"
import { i18n } from "../../../i18n"
import { roundedPriceHelper } from "../../../helpers/roundedPriceHelper"
import { useUserPOS } from "../../../composables/useUserPOS"
const { global } = i18n

function mapCategories(categories: string[]): Categories[] {
  return categories.map((categoryStr) => Categories[categoryStr as keyof typeof Categories] || Categories.STANDARD)
}

function determineAggregationType(stayLength: number, countryMarket: string): string {
  const isMultiNights = stayLength > 1
  const isUS = countryMarket === CountryMarket.US

  const shouldUseTotalStay = !isMultiNights || (isMultiNights && isUS)

  return shouldUseTotalStay
    ? global.t("components.stay_view.room_card.price_section.per_stay")
    : global.t("components.stay_view.room_card.price_section.per_night")
}

function calculateFormattedAmounts(
  pricing: Offer["pricing"],
  stayLength: number,
  countryMarket: string
): {
  main: { amount: number; formatted: string; currency: string }
  alternative: { amount: number; formatted: string; currency: string }
} {
  const isMultiNights = stayLength > 1
  const isUS = countryMarket === CountryMarket.US

  const shouldUseTotalStay = !isMultiNights || (isMultiNights && isUS)

  const mainFormattedAmount = shouldUseTotalStay ? pricing.main : pricing.main.average
  const alternativeAmount = shouldUseTotalStay ? pricing.alternative : pricing.alternative?.average

  return {
    alternative: {
      amount: alternativeAmount?.amount || 0,
      currency: pricing.currency,
      formatted: alternativeAmount?.formattedAmount || "0"
    },
    main: {
      amount: mainFormattedAmount.amount,
      currency: pricing.currency,
      formatted: mainFormattedAmount.formattedAmount
    }
  }
}

export function offerPricingMapper(
  pricing: Offer["pricing"],
  stayLength: number,
  countryMarket: string,
  isLogged: boolean
): UiPricingSectionProps {
  const { userPOS } = useUserPOS()
  const formattedAggregationType = determineAggregationType(stayLength, countryMarket)

  const { main: mainFormattedAmount, alternative: alternativeAmount } = calculateFormattedAmounts(
    pricing,
    stayLength,
    countryMarket
  )

  const hasMemberRate = pricing.main.categories.includes(PricingCategory.MEMBER_RATE)

  const displayedMainAmount = hasMemberRate && !isLogged ? alternativeAmount : mainFormattedAmount

  const mainPricing: PricingType = {
    categories: mapCategories(pricing.main.categories),
    formattedAmount: roundedPriceHelper(displayedMainAmount.amount, userPOS.fullLocale, displayedMainAmount.currency)
  }

  return {
    currency: pricing.currency,
    formattedAggregationType,
    formattedTaxType: pricing.formattedTaxType,
    isLogged,
    mainPricing
  }
}
