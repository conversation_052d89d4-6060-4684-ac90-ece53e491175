import { Amenities, Facilities, FacilitiesItems } from "../../hotels/hotels.types"
import { Amenity, Facility, FacilityItem } from "design-system"
import { i18n } from "../../../i18n"

const { global } = i18n

export function mapAmenities(data?: Amenities[]): Amenity[] {
  if (!data) {
    return []
  }

  return data.map((amenityData, index) => ({
    id: index,
    items: mapFacilities(amenityData.facilities),
    name: amenityData.name,
    title: amenityData.label
  }))
}

function mapFacilities(facilitiesData: Facilities[]): Facility[] {
  return facilitiesData.map((facilityData) => ({
    items: mapItems(facilityData.items),
    label: facilityData.label
  }))
}

function mapItems(itemsData: FacilitiesItems[]): FacilityItem[] {
  return itemsData.map((itemData, index) => ({
    id: index,
    subtitle: itemData.paying ? global.t("components.stay_view.room_details_modal.aditional_payment_label") : undefined,
    title: itemData.name
  }))
}
