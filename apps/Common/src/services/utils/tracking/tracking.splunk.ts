import SplunkOtelWeb from "@splunk/otel-web"

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function initSplunk(splunkConf: any) {
  SplunkOtelWeb.init({
    applicationName: splunkConf.appName,
    cookieDomain: splunkConf.domain,
    deploymentEnvironment: splunkConf.env,
    globalAttributes: {
      critical_biz: splunkConf.critical,
      itdepartment: splunkConf.itDepartment,
      itdomain: splunkConf.itDomain,
      leanix_id: splunkConf.leanixId,
      pci: splunkConf.pci,
      snow_id: splunkConf.snowId
    },
    realm: splunkConf.realm,
    rumAccessToken: splunkConf.token
  })
}
