import { Currency } from "../../global/enums"
import { LengthOfStayUnit } from "../../services/offer/offer.enums"
import { MediaFormat } from "../../services/offer/offer.types"
import { TotalAmountMode } from "./basket.enums"

export interface Basket {
  canpol: string
  currency: string
  currencySymbol: string
  id: string
  items: BasketItem[]
  pricing: {
    formattedTotalAmount: string
    formattedTotalAmountHotelCurrency: string
    totalAmount: number
    totalAmountHotelCurrency: number
  }
  hotelCurrency: string
  hotel: BasketHotel
}

export interface BasketHotel {
  brand: {
    code: string
    label: string
  }
  checkInHour: string
  checkOutHour: string
  formattedCheckInHour: string
  formattedCheckOutHour: string
  id: string
  localRating: number
  media: {
    category: string
    formats: MediaFormat[]
    type: string
  }
  name: string
}

export interface PostBasketApiResponse {
  operationWarnings: unknown[]
  basket: ApiBasket
  modificationReport: unknown[]
}

export interface GetBasketApiResponse {
  operationWarnings: unknown[]
  basket: ApiBasket
}

export interface ApiBasket {
  currency: Currency
  currencySymbol: string
  hotel: {
    brand: {
      code: string
      label: string
    }
    checkInHour: string
    checkOutHour: string
    formattedCheckInHour: string
    formattedCheckOutHour: string
    id: string
    localRating: number
    media: {
      category: string
      formats: MediaFormat[]
      type: string
    }
    name: string
  }
  hotelCurrency: Currency
  hotelCurrencySymbol: string
  id: string
  itemTotalAmountMode: TotalAmountMode
  items: BasketItem[]
  pricing: {
    formattedTotalAmount: string
    formattedTotalAmountHotelCurrency: string
    totalAmount: number
    totalAmountHotelCurrency: number
  }
  request: BasketRequest
  simplifiedCanpol: {
    code: number
    label: string
  }
}

export interface BasketItem {
  adults: number
  beneficiary: object
  children: number
  childrenAges: number[]
  detail: {
    categories: [
      {
        code: string
        description: string
      }
    ]
    fees: {
      amount: number
      emphasizedBreakdown: [
        {
          amount: number
          formattedAmount: string
          label: string
        }
      ]
      formattedAmount: string
      label: string
    }
    formattedMainProductAmount: string
    formattedTotalOptionsAmount: string
    guarantiedPreferences: unknown[]
    mainProductAmount: number
    mainProductId: string
    mainProductLabel: string
    nonGuarantiedPreferences: unknown[]
    options: unknown[]
    rateId: string
    rateLabel: string
    taxes: {
      amount: number
      formattedAmount: string
      label: string
    }
    totalOptionsAmount: number
    totalOptionsAmountLabel: string
  }
  formattedTotalAmount: string
  id: string
  label: string
  offerId: string
  totalAmount: number
}

export interface BasketRequest {
  adults: number
  children: number
  dateIn: string
  dateOut: string
  formattedDateIn: string
  formattedDateOut: string
  lengthOfStay: {
    unit: LengthOfStayUnit
    value: number
  }
}
