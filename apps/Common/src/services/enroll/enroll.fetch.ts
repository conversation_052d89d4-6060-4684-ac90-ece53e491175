import { ApiEligibility, ApiLoyaltyEnroll, LoyaltyEnrollBody } from "./enroll.types"
import { AccorApiEndpoints } from "@/global/consts"
import { GenerateCode } from "./enroll.enums"
import { getGlobalConfig } from "@/global/config"
import { useApiManager } from "@/composables"

export const getEllegibility = async (email: string, language: string): Promise<ApiEligibility> => {
  const { apiManager } = useApiManager()

  const body = {
    email: email,
    generateCode: GenerateCode.EXISTING,
    lang: language
  }

  const { data: response } = await apiManager.post<ApiEligibility>(AccorApiEndpoints.eligibility, body)

  return response
}

export const getLoyalityEnroll = async (loyaltyEnroll: LoyaltyEnrollBody): Promise<ApiLoyaltyEnroll> => {
  const { apiManager } = useApiManager()
  const config = getGlobalConfig()

  const body = {
    ...loyaltyEnroll,
    clientId: config.apiHeaders.clientId,
    language: "en",
    loyalty: {
      origin: config.siteCode
    }
  }

  const { data: response } = await apiManager.post<ApiLoyaltyEnroll>(AccorApiEndpoints.loyaltyEnroll, body)

  return response
}
