import { Civility, ContactType } from "./enroll.enums"

export interface ApiEligibility {
  codeSent: boolean
  contactType: ContactType
}

export interface ApiLoyaltyEnroll {
  pmid: string
  hasPassword: boolean
  mustUpdatePassword: boolean
}

export interface LoyaltyEnrollBody {
  firstName: string
  lastName: string
  civility?: Civility
  password: string
  language: string
  validationCode?: string
  clientId: string
  externalId?: string
  email: string
  phone?: {
    prefix?: string
    number?: string
  }
  postalAddress?: {
    address1?: string
    address2?: string
    companyName?: string
    extensionAddress?: string
    zipCode?: string
    city?: string
    countryCode: string
    stateCode?: string
  }
  loyalty?: {
    promoCode?: string
    origin?: string
    channel?: string
  }
  social?: {
    id?: string
    flowId?: string
  }
  optins?: string[]
}
