import { ApiHotel, ApiHotelMedia, HotelsApiResponse } from "./hotels.types"
import { AccorApiEndpoints } from "../../global/consts"
import { Region } from "../../global/enums"
import { cachedApi } from "../cache"
import { getGlobalConfig } from "../../global/config"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchHotelsByRegion(region: Region): Promise<HotelsApiResponse> {
  const config = getGlobalConfig()
  return cachedApi(
    async () => {
      const { apiManager } = useApiManager()

      const params = {
        brand: config.brandCode,
        enlarge: false,
        q: region
      }

      const { data: response } = await apiManager.get<HotelsApiResponse>(AccorApiEndpoints.hotels, {
        params
      })

      return response
    },
    AccorApiEndpoints.hotels,
    { keyParts: [config.brandCode, region] }
  )
}

export async function fetchHotel(hotelId: string): Promise<ApiHotel> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.get<ApiHotel>(AccorApiEndpoints.hotel(hotelId))

    return response
  }, AccorApiEndpoints.hotel(hotelId))
}

export async function fetchHotelsByIds(hotelsIds: string[]): Promise<HotelsApiResponse> {
  const formattedIds = hotelsIds.join()

  return cachedApi(
    async () => {
      const { apiManager } = useApiManager()

      const params = {
        enlarge: false,
        id: formattedIds
      }

      const { data: response } = await apiManager.get<HotelsApiResponse>(AccorApiEndpoints.hotels, {
        params
      })

      return response
    },
    AccorApiEndpoints.hotels,
    { keyParts: [formattedIds] }
  )
}

export async function getMedias(hotelId: string): Promise<ApiHotelMedia[]> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const params = {
      category: "HOTEL",
      type: "IMAGE"
    }

    const mediasEndpoint = AccorApiEndpoints.medias(hotelId)

    const { data: response } = await apiManager.get<ApiHotelMedia[]>(mediasEndpoint, { params })

    return response
  }, AccorApiEndpoints.medias(hotelId))
}
