import { HotelsApiResponse } from "../hotels.types"
import { Region } from "@/global/enums"
import hotels from "./hotels.json"
import hotelsAfrica from "./hotels.africa.json"
import hotelsAsia from "./hotels.asia.json"
import hotelsEurope from "./hotels.europe.json"
import hotelsNorthAmerica from "./hotels.north-america.json"

import { vi } from "vitest"

export const fetchHotelsByRegion = vi.fn(async (region: Region): Promise<HotelsApiResponse> => {
  switch (region) {
    case Region.AFRICA:
      return hotelsAfrica as HotelsApiResponse
    case Region.ASIA:
      return hotelsAsia as HotelsApiResponse
    case Region.EUROPE:
      return hotelsEurope as HotelsApiResponse
    case Region.NORTH_AMERICA:
      return hotelsNorthAmerica as HotelsApiResponse
    case Region.SOUTH_AMERICA:
      throw new Error("Mocked error on South America hotels")
  }
})

export const fetchHotelsByIds = vi.fn(async (ids: string[]): Promise<HotelsApiResponse> => {
  return {
    ...hotels,
    results: hotels.results.filter((result) => ids.includes(result.hotel.id)) as HotelsApiResponse["results"]
  } as HotelsApiResponse
})
