export interface PosCountriesGroupedByContinent {
  continentCode: string
  continentLabel: string
  countryList: Country[]
}

export interface PosCurrenciesGroupedByContinent {
  continentCode: string
  continentLabel: string
  currencyList: Currency[]
}

export interface PosCountryLanguageAuto {
  accorCountryCode: string
  accorCountryLabel: string
  continent: string
  currencyCode: string
  currencyLabel: string
  currencySymbol: string
  isoCountry: string[]
  languageCode: string
  languageLabel: string
  pointOfSaleCode: string
}

export interface Country {
  accorCountryCode: string
  accorCountryLabel: string
  accorCountryLanguageLabel: string
  homepageUrl: string
  isoCountryCode: string[]
  languageCode: string
  languageLabel: string
  pointOfSale: string
  orderId: number
}

export interface CountryInfos {
  accorCountryCode: string
  accorCountryLabel: string
  continent: string
  currencyCode: string
  currencyLabel: string
  currencySymbol: string
  isoCountry: string[]
  languageCode: string
  languageLabel: string
  pointOfSaleCode: string
  redirection: {
    httpCode: number
    url: string
  }
}

export interface Currency {
  accorCountryCode?: string[]
  currencyCode: string
  currencyLabel: string
  currencySymbol?: string
  isoCountryCode?: string[]
}
