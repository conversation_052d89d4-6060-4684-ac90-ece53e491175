import {
  Country,
  CountryInfos,
  Currency,
  PosCountriesGroupedByContinent,
  PosCountryLanguageAuto,
  PosCurrenciesGroupedByContinent
} from "./pos.type"
import { ErrorTypes, useHTTPError } from "../../composables/useHTTPError"
import { AccorApiEndpoints } from "../../global/consts"
import { cachedApi } from "../cache"
import { getGlobalConfig } from "../../global/config"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchPosCountriesGroupedByContinent(): Promise<PosCountriesGroupedByContinent[]> {
  const config = getGlobalConfig()
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.get<PosCountriesGroupedByContinent[]>(
      AccorApiEndpoints.posCountriesGroupedByContinent(config.siteCode)
    )

    return response
  }, AccorApiEndpoints.posCountriesGroupedByContinent(config.siteCode))
}

export async function fetchPosCurrenciesGroupedByContinent(): Promise<PosCurrenciesGroupedByContinent[]> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.get<PosCurrenciesGroupedByContinent[]>(
      AccorApiEndpoints.posCurrenciesGroupedByContinent
    )

    return response
  }, AccorApiEndpoints.posCurrenciesGroupedByContinent)
}

export async function fetchPosCountryLanguageAuto(
  isoCountryCode: string,
  languageCode: string,
  currencyCode?: string
): Promise<void> {
  const { apiManager } = useApiManager()
  const config = getGlobalConfig()
  const { error: posError, generateError } = useHTTPError("pos")
  try {
    await apiManager.get<PosCountryLanguageAuto>(
      AccorApiEndpoints.posCountryLanguageAuto(config.siteCode, isoCountryCode, languageCode, currencyCode)
    )
  } catch {
    posError.value = generateError(ErrorTypes.GENERIC)
  }
}

export async function updateCurrencyCookies(currency: Currency): Promise<void> {
  const { apiManager } = useApiManager()
  const { error: posError, generateError } = useHTTPError("pos")

  try {
    const endPoint = AccorApiEndpoints.posCurrencyManual(currency.currencyCode)

    await apiManager.get<Currency>(endPoint)
  } catch {
    posError.value = generateError(ErrorTypes.GENERIC)
  }
}

export async function updateLanguageCookies(country: Country): Promise<void> {
  const { apiManager } = useApiManager()
  const { error: posError, generateError } = useHTTPError("pos")
  const config = getGlobalConfig()

  try {
    const endPoint = AccorApiEndpoints.posCountryLanguageManual(
      config.siteCode,
      country.accorCountryCode,
      country.languageCode
    )

    await apiManager.get<CountryInfos>(endPoint)
  } catch {
    posError.value = generateError(ErrorTypes.GENERIC)
  }
}
