import type { AppCache, AppCacheSetOptions } from "./interfaces"
import { i18n } from "../../i18n"
import localStorageCache from "./localStorageCache"

type CachedApiOptions = {
  keyParts?: string[]
  cacheSetOptions?: AppCacheSetOptions
  storage?: AppCache
}

export async function cachedApi<T>(
  apiCall: () => Promise<T>,
  endpoint: string,
  options?: CachedApiOptions
): Promise<T> {
  const storage = options?.storage ?? localStorageCache
  const otherKeyParts = options?.keyParts || []
  const cacheSetOption = options?.cacheSetOptions || { ttl: 24 * 60 }

  const key = storage.buildKey(endpoint, i18n.global.locale.value, ...otherKeyParts)
  const cachedItem = storage.get<T>(key)

  if (cachedItem) return cachedItem

  const response = await apiCall()

  storage.set(key, response, cacheSetOption)

  return response
}
