import { AppCache, AppCacheSetOptions } from "./interfaces"

const minuteInMs = 60 * 1000

type StorageItem<T> = {
  expiresAt: number
  value: T
}

export default <AppCache>{
  buildKey(url: string, locale: string, ...otherDiscrimants: string[]): string {
    return otherDiscrimants.reduce((acc, discriminant) => `${acc}::${discriminant}`, `cache-lsc::${locale}::${url}`)
  },

  delete(key: string): void {
    try {
      localStorage.removeItem(key)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      /* empty */
    }
  },

  get<T>(key: string): T | undefined {
    try {
      const item = localStorage.getItem(key)

      if (!item) return undefined

      const now = Date.now()
      const parsedItem = JSON.parse(item) as StorageItem<T>

      if (parsedItem.expiresAt < now) {
        this.delete(key)
        return undefined
      }

      return parsedItem.value
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      return undefined
    }
  },

  purgeAll(): void {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)

      if (!key || !key.startsWith("cache-lsc::")) continue

      this.delete(key)
    }
  },

  set<T>(key: string, value: T, options: AppCacheSetOptions = { ttl: 24 * 60 }): T {
    const item: StorageItem<T> = {
      expiresAt: Date.now() + options.ttl * minuteInMs,
      value
    }

    try {
      localStorage.setItem(key, JSON.stringify(item))
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      /* empty */
    }

    return value
  }
}
