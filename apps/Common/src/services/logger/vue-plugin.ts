import { Plugin } from "vue"
import { logger } from "./logger"

export const vueLogger: Plugin = {
  install(app) {
    if (import.meta.env.DEV) return

    app.config.errorHandler = (error, instance, info) => {
      const componentName = instance?.$options?.__name ?? "N/A"
      const message = error instanceof Error ? `${info} | ${error.message}` : info

      logger.error({ error, info, instance }, `[ERROR][VUE][${componentName}] ${message}`)
    }

    app.config.warnHandler = (message, instance, trace) => {
      const componentName = instance?.$options?.__name ?? "N/A"

      logger.warn({ instance, message, trace }, `[WARN][VUE][${componentName}] ${message}`)
    }
  }
}
