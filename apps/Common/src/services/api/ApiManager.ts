import { Semaphores, useApiSemaphore } from "../../composables/useApiSemaphore"
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"
import { AccorApiEndpoints } from "../../global/consts"
import { i18n } from "../../i18n"
import { logger } from "../logger"
import qs from "qs"
import { useIdentificationPlainText } from "../../composables/useIdentificationPlainText"
import { useOidcUri } from "../../composables/useOidcUri"

export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

export function mapApiResponse<T, K>(response: ApiResponse<T>, extractor: (data: T) => K): K {
  return extractor(response.data)
}

const { waitUntil } = useApiSemaphore()

class ApiManager {
  private readonly apiClient: AxiosInstance

  constructor(baseURL: string, headers: Record<string, string> = {}) {
    this.apiClient = axios.create({
      baseURL,
      headers,
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: "comma" }),
      withCredentials: true
    })

    this.apiClient.interceptors.request.use(async function (config) {
      const { identification } = useIdentificationPlainText()

      const whitelistURLs = [AccorApiEndpoints.contactMe, AccorApiEndpoints.commerceTracking]

      // The first mandatory calls
      if (config.url && whitelistURLs.includes(config.url)) return config

      await waitUntil(Semaphores.userDetails)
      await waitUntil(Semaphores.cookieAttribution)

      // Identification rely on cookieAttribution, it comes after cookieAttribution and must await it
      if (config.url && config.url.includes(AccorApiEndpoints.identification)) {
        return config
      }

      await waitUntil(Semaphores.identification)

      Object.assign(config.headers, {
        ...config.headers,
        "identification-token": identification.identificationToken || undefined
      })

      // Release all other calls
      return config
    })

    // Enable log if not in dev
    if (!import.meta.env.DEV) {
      this.apiClient.interceptors.response.use(
        (response) => {
          logger.info(`[SUCCESS][NETWORK][${response.config.method?.toUpperCase()}] ${response.config.url}`)
          return response
        },
        (error) => {
          try {
            const axiosError = error as AxiosError
            logger.error(
              { error: axiosError },
              `[ERROR][NETWORK][${axiosError.config?.method?.toUpperCase()}] ${axiosError.name} - ${axiosError.message}`
            )
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (e) {
            // silent error
          }
          throw error
        }
      )
    }
  }

  async get<T>(url: string | URL, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers
    }

    return await this.apiClient.get<T>(url.toString(), { ...config, headers })
  }

  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers
    }

    return await this.apiClient.post<T>(url, data, { ...config, headers })
  }

  async put<T>(url: string, data: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers
    }

    return await this.apiClient.put<T>(url, data, { ...config, headers })
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers
    }

    return await this.apiClient.delete<T>(url, { ...config, headers })
  }

  async getWithAccessToken<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const { refreshUri } = useOidcUri()

    try {
      const response = await this.apiClient.get<T>(url, config)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.response?.status === 401) {
        // Try to refresh expired access token
        await axios.get(refreshUri.value)

        const retriedResponse = await this.apiClient.get<T>(url, config)

        return retriedResponse.data
      }

      throw axiosError
    }
  }
}

export default ApiManager
