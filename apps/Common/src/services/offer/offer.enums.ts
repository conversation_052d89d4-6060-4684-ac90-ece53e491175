export enum AvailabilityStatus {
  AVAILABLE = "AVAILABLE",
  UNAVAILABLE = "UNAVAILABLE"
}

export enum AggregationPricingType {
  AVERAGE_PER_NIGHT = "AVERAGE_PER_NIGHT",
  TOTAL_STAY = "TOTAL_STAY"
}

export enum CancellationPolicy {
  FREE_CANCELLATION = "FREE_CANCELLATION",
  NO_CANCELLATION = "NO_CANCELLATION",
  RESTRICTED_CANCELLATION = "RESTRICTED_CANCELLATION"
}

export enum CodePolicy {
  CANPOL = "CANPOL",
  CINPOL = "CINPOL",
  COUPOL = "COUPOL",
  GUAPOL = "GUAPOL"
}

export enum Deduction {
  MEMBER_RATE = "MEMBER_RATE",
  PREFERRED = "PREFERRED",
  CORPORATE = "CORPORATE"
}

export enum LengthOfStayUnit {
  DAY = "DAY",
  NIGHT = "NIGHT"
}

export enum MealPlan {
  EUROPEAN_PLAN = "EUROPEAN_PLAN",
  FULL_BOARD = "FULL_BOARD"
}

export enum OfferCategoryCode {
  MEMBER_RATE = "MEMBER_RATE",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  LOYALTY_ALL_PRIVILEGED = "LOYALTY_ALL_PRIVILEGED",
  PREFERRED = "PREFERRED"
}

export enum OfferType {
  ROOM = "ROOM",
  PACKAGE = "PACKAGE"
}

export enum PolicyGuarantee {
  NO_PREPAY = "NO_PREPAY"
}

export enum PricingCategory {
  CORPORATE_NEGOTIATED_RATE = "CORPORATE_NEGOTIATED_RATE",
  LOYALTY_ALL_PLUS_IBIS = "LOYALTY_ALL_PLUS_IBIS",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  AGENCY = "AGENCY",
  PREMIUM_AVAILABILITY = "PREMIUM_AVAILABILITY",
  FAMILY = "FAMILY",
  MEMBER_RATE = "MEMBER_RATE"
}

export enum TaxType {
  ALL_TAXES_AND_FEES_INCLUDED = "ALL_TAXES_AND_FEES_INCLUDED",
  TAXES_EXCLUDED = "TAXES_EXCLUDED"
}

export enum RoomClassCode {
  APT = "APT",
  APV = "APV",
  BEA = "BEA",
  BEL = "BEL",
  CLU = "CLU",
  CON = "CON",
  DHV = "DHV",
  DLF = "DLF",
  DLL = "DLL",
  DLN = "DLN",
  DLS = "DLS",
  DLT = "DLT",
  DLV = "DLV",
  DLX = "DLX",
  DLXB = "DLXB",
  DMV = "DMV",
  DPV = "DPV",
  DPX = "DPX",
  DSV = "DSV",
  DUN = "DUN",
  DVR = "DVR",
  DVS = "DVS",
  DXX = "DXX",
  EST = "EST",
  EXC = "EXC",
  EXE = "EXE",
  EXS = "EXS",
  FAM = "FAM",
  FAR = "FAR",
  FAS = "FAS",
  FCO = "FCO",
  FG = "FG",
  FGS = "FGS",
  FHF = "FHF",
  FLL = "FLL",
  FLX = "FLX",
  FME = "FME",
  FMF = "FMF",
  FMS = "FMS",
  FMT = "FMT",
  FMV = "FMV",
  FPR = "FPR",
  FSU = "FSU",
  FVS = "FVS",
  GBS = "GBS",
  GDN = "GDN",
  GGS = "GGS",
  GLD = "GLD",
  GLJ = "GLJ",
  GLL = "GLL",
  GLN = "GLN",
  GLS = "GLS",
  GLV = "GLV",
  GRN = "GRN",
  GSG = "GSG",
  GSO = "GSO",
  GSS = "GSS",
  GST = "GST",
  HIG = "HIG",
  HNV = "HNV",
  HPR = "HPR",
  HTL = "HTL",
  JDX = "JDX",
  JNC = "JNC",
  JNL = "JNL",
  JNR = "JNR",
  JNRR = "JNRR",
  JNS = "JNS",
  JSG = "JSG",
  JSU = "JSU",
  KIL = "KIL",
  LDX = "LDX",
  LGS = "LGS",
  LUX = "LUX",
  LUXR = "LUXR",
  MAN = "MAN",
  MDS = "MDS",
  MLB = "MLB",
  MOD = "MOD",
  MTN = "MTN",
  OBS = "OBS",
  OCV = "OCV",
  OST = "OST",
  OTHER = "NA",
  PEN = "PEN",
  PLV = "PLV",
  PNT = "PNT",
  POV = "POV",
  PRE = "PRE",
  PRM = "PRM",
  PRS = "PRS",
  PRSR = "PRSR",
  PRV = "PRV",
  PS = "PS",
  PST = "PST",
  RDV = "RDV",
  RED = "RED",
  RES = "RES",
  ROY = "ROY",
  RTE = "RTE",
  SAL = "SAL",
  SAS = "SAS",
  SDL = "SDL",
  SGO = "SGO",
  SGS = "SGS",
  SI2 = "SI2",
  SIG = "SIG",
  SOF = "SOF",
  SPS = "SPS",
  SPV = "SPV",
  SSD = "SSD",
  SSE = "SSE",
  SST = "SST",
  STA = "STA",
  STB = "STB",
  STC = "STC",
  STE = "STE",
  STE1 = "STE1",
  STE1G = "STE1G",
  STE2 = "STE2",
  STE2G = "STE2G",
  STER = "STER",
  STF = "STF",
  STG = "STG",
  STH = "STH",
  STI = "STI",
  STJ = "STJ",
  STK = "STK",
  STL = "STL",
  STM = "STM",
  STS = "STS",
  STV = "STV",
  SUI = "SUI",
  SUP = "SUP",
  SUT = "SUT",
  V2B = "V2B",
  V3B = "V3B",
  VIL = "VIL",
  WHA = "WHA",
  XVS = "XVS"
}
