import { AggregationPricingType, LengthOfStayUnit } from "./offer.enums"
import { ApiBestOffers, ApiPricingCondition, RoomRateApiResponse } from "./offer.types"
import { DateMoment, RoomType } from "design-system"
import { AccommodationWrapper } from "../hotels/hotels.types"
import { AccorApiEndpoints } from "../../global/consts"
import { calculateNights } from "@shared/utils"
import { useApiManager } from "../../composables/useApiManager"
import { useSearch } from "../../composables"
import { useUserPOS } from "../../composables/useUserPOS"

export async function fetchOffers(
  hotelId: string,
  dateInValue: string,
  nbNights: number,
  nbAdults: number,
  childrenAges: number[],
  currency: string,
  hasAccessibility: boolean,
  country: string
): Promise<Pick<ApiBestOffers, "availability" | "bestOffers" | "groupBy">> {
  const { apiManager } = useApiManager()

  const offersBody = {
    adults: nbAdults,
    childrenAges,
    countryMarket: country,
    currency,
    dateIn: dateInValue,
    filter: hasAccessibility ? "PMR" : undefined,
    groupBy: "ROOM_CLASS",
    includeAverageAmount: true,
    "lengthOfStay.unit": LengthOfStayUnit.NIGHT,
    "lengthOfStay.value": nbNights,
    pricingContext: AggregationPricingType.TOTAL_STAY
  }
  const { data: response } = await apiManager.get<ApiBestOffers>(AccorApiEndpoints.offers(hotelId), {
    params: offersBody
  })

  return { availability: response.availability, bestOffers: response.bestOffers, groupBy: response.groupBy }
}

export async function getSelectedRoomRateCards(
  productCode: string,
  currentRoom: RoomType
): Promise<RoomRateApiResponse> {
  const { apiManager } = useApiManager()
  const { dates, rooms, hotel } = useSearch()
  const { userPOS } = useUserPOS()
  const dateIn = dates.value[DateMoment.START]
  const dateOut = dates.value[DateMoment.END]

  if (!hotel.value || !dateOut || !dateIn || !rooms.value.length) {
    throw new Error("Missing required parameters for fetching room offers")
  }

  const nbNights = calculateNights(dateIn, dateOut)
  const adults = currentRoom.adults
  const childrenAges = currentRoom.childrenAges

  let data: {
    adults: number
    childrenAges: number[]
    countryMarket: string
    currency: string
    dateIn: string
    includeAverageAmount: boolean
    lengthOfStay: {
      unit: LengthOfStayUnit
      value: number
    }
    pricingContext: AggregationPricingType
    productCodes: string[]
    selection?: Array<{
      adults: number
      childrenAges: number[]
      offerId: string
      productCode: string
    }>
  } = {
    adults: adults,
    childrenAges: childrenAges,
    countryMarket: userPOS.countryMarket,
    currency: userPOS.currency,
    dateIn: dateIn.toISOString().split("T")[0],
    includeAverageAmount: true,
    lengthOfStay: {
      unit: LengthOfStayUnit.NIGHT,
      value: nbNights
    },
    pricingContext: AggregationPricingType.TOTAL_STAY,
    productCodes: [productCode]
  }

  const selection = rooms.value.filter((room) => room.rateId)

  if (selection.length > 0) {
    data = {
      ...data,
      selection: [
        ...selection.map((room) => ({
          adults: room.adults,
          childrenAges: room.childrenAges,
          offerId: room.rateId!,
          productCode: productCode
        }))
      ]
    }
  }
  const productOffersEndpoint = AccorApiEndpoints.productOffer(hotel.value.id)

  const { data: response } = await apiManager.post<RoomRateApiResponse>(productOffersEndpoint, data)

  return response
}

export async function getPricingCondition(offerId: string): Promise<ApiPricingCondition> {
  const { apiManager } = useApiManager()
  const { userPOS } = useUserPOS()

  const params = {
    countryMarket: userPOS.countryMarket,
    currency: userPOS.currency,
    includeAverageAmount: true,
    offerId: offerId,
    pricingContext: AggregationPricingType.TOTAL_STAY
  }

  const { data: response } = await apiManager.get<ApiPricingCondition>(AccorApiEndpoints.pricing, {
    params
  })

  return response
}

export async function getAccommodations(hotelId: string, offersId: string): Promise<AccommodationWrapper> {
  const { apiManager } = useApiManager()

  const params = {
    id: offersId
  }

  const accommodationsEndpoint = AccorApiEndpoints.accommodations.replace("{hotelId}", hotelId)

  const { data: response } = await apiManager.get<AccommodationWrapper>(accommodationsEndpoint, { params })

  return response
}
