import {
  AmenitiesColor,
  Icon,
  PoliciesType,
  RoomCancellationPolicy,
  UiAmenityProps,
  UiCancellationPolicyProps,
  rateCardPriceDisplay
} from "design-system"
import {
  ApiBestOffers,
  ApiPricingCondition,
  Offer,
  OfferMappedType,
  PerUnit,
  Policy,
  TaxesAndFees
} from "./offer.types"
import { CodePolicy, PricingCategory, RoomClassCode } from "./offer.enums"
import { KeyFeaturesCode, KeyFeaturesOrder } from "../../services/utils/keyFeatures/keyFeatures.enums"
import { RateCode, RateIcons } from "../utils/amenities/amenities.enums"
import { galleryMapper, getDefaultUiImageProps, roomCardmediaMapper } from "../utils/media/media.mapper"
import { MatchedPair } from "./offer.interface"
import { ROOM_CLASS_TITLES } from "./offer.constants"
import { RateCardProps } from "../../components/StayView/RateCard/interface"
import { i18n } from "../../i18n"
import { offerPricingMapper } from "../utils/pricing/pricing.mapper"
import { roomCardKeyFeaturesMapper } from "../utils/keyFeatures/keyFeatures.mapper"
import { roundedPriceHelper } from "../../helpers/roundedPriceHelper"
import { truncateApiTitle } from "@shared/utils"
import { useUserPOS } from "../../composables/useUserPOS"

const { global } = i18n

export function offerMapper(
  offer: Offer,
  stayLength: number,
  countryMarket: string,
  isLogged: boolean
): OfferMappedType {
  const roomImages = offer.product.media ? roomCardmediaMapper(offer.product.media.medias, offer.product.label) : []

  const amenitiesMappedAndSorted = roomCardKeyFeaturesMapper(offer.product.bedding, offer.product.keyFeatures)
    .filter((feature) => KeyFeaturesOrder.includes(feature.code as KeyFeaturesCode))
    .sort(
      (a, b) =>
        KeyFeaturesOrder.indexOf(a.code as KeyFeaturesCode) - KeyFeaturesOrder.indexOf(b.code as KeyFeaturesCode)
    )

  return {
    id: offer.id,
    rateId: offer.rate.id,
    roomCard: {
      amenities: amenitiesMappedAndSorted,
      bedding: offer.product.bedding?.details?.[0]?.label || "",
      gallery: galleryMapper(offer.product.media?.count || 1, roomImages),
      image: roomImages.length ? roomImages[0] : getDefaultUiImageProps("")[0],
      isOpen: false,
      pricing: offerPricingMapper(offer.pricing, stayLength, countryMarket, isLogged),
      productCode: offer.product.id,
      title: truncateApiTitle(offer.product.label)
    }
  }
}

export function pricingConditionMapper(
  pricingCondition: ApiPricingCondition,
  isLoggedIn: boolean,
  expediaCompliant: boolean
) {
  const { main, alternative } = pricingCondition.offer.pricing

  let memberPrice: string | undefined
  let publicPrice: string

  // Determine prices based on login status and compliance
  if (!isLoggedIn) {
    if (expediaCompliant) {
      publicPrice = main.formattedAmount
    } else {
      const alternativeIsMemberRate = alternative?.categories.includes(PricingCategory.MEMBER_RATE)
      memberPrice = alternativeIsMemberRate ? alternative?.formattedAmount : undefined
      publicPrice = main.formattedAmount
    }
  } else {
    // User is logged in
    const isMemberRate = main.categories.includes(PricingCategory.MEMBER_RATE)
    memberPrice = isMemberRate ? main.formattedAmount : undefined
    publicPrice = isMemberRate ? alternative?.formattedAmount : main.formattedAmount
  }

  return {
    dailyRates: dailyRatesMapper(pricingCondition.perUnit, pricingCondition.taxesAndFees),
    isLoggedIn,
    memberPrice,
    policies: policiesMapper(pricingCondition.policies),
    publicPrice,
    rate: {
      description: pricingCondition.offer.rate.description,
      title: pricingCondition.offer.rate.label
    },
    taxLabel: pricingCondition.offer.pricing.formattedTaxType
  }
}

export function rateCardMapper(
  offers: Offer[],
  isExpediaCompliant: boolean,
  isUserLoggedIn: boolean,
  isMultiNights: boolean,
  isUSLocation: boolean
): RateCardProps[] {
  return offers.map((offer) => {
    const rateCardAmounts = getRateCardPricingInfos(
      offer.pricing,
      isUserLoggedIn,
      isMultiNights,
      isExpediaCompliant,
      isUSLocation
    )

    let rateCardId = offer.id

    // TODO check alternative id usage
    if (!isUserLoggedIn && offer.pricing.alternative) {
      rateCardId = offer.pricing.alternative.id
    }

    return {
      apiOffer: offer,
      content: {
        ...rateCardAmounts,
        amenities: rateCardAmenitiesMapper(
          offer.pricing.deduction,
          offer.pricing.main.simplifiedPolicies,
          offer.mealPlan,
          offer.pricing.main,
          isExpediaCompliant,
          isUserLoggedIn
        ),
        formattedTaxType: offer.pricing.formattedTaxType,
        rateCardId, // TODO clean this to be more precise (as it's not the rateCardId but the offerId)
        title: offer.rate.label
      },
      productCode: offer.product.id,
      rateCode: offer.rate.id, // TODO rename rateCode to rateId
      rateId: offer.id // TODO rename rateId to offerId
    }
  })
}

export function policiesMapper(policies: Policy[]): PoliciesType[] {
  return policies.map((policy) => ({
    label: policy.label,
    text: policy.description
  }))
}

export function dailyRatesMapper(perUnit: PerUnit[], taxesAndFees: TaxesAndFees) {
  const fee = taxesAndFees.included[0] || { label: "" }

  return perUnit.map((unit) => ({
    date: unit.from,
    fee,
    price: unit.formattedAmount
  }))
}

export function getRateCardPricingInfos(
  pricing: Offer["pricing"],
  isUserLoggedIn: boolean,
  isMultiNights: boolean,
  isExpediaCompliant: boolean,
  isUSLocation: boolean
): {
  aggregationLabel: string
  currency: string
  formattedTaxType?: string
  prominentMemberPrice?: string
  publicPrice: string
  secondaryMemberPrice?: string
  secondaryPublicPrice?: string
  publicPriceDisplay?: rateCardPriceDisplay
  memberPriceDisplay?: rateCardPriceDisplay
} {
  const { userPOS } = useUserPOS()
  const { main, alternative } = pricing
  const totalForYourStayLabel = global.t("components.stay_view.rate_card.price_section.total_for_your_stay")
  const averagePerNightLabel = global.t("components.stay_view.rate_card.price_section.average_per_night")

  const formatPrice = (amount: number) => roundedPriceHelper(amount, userPOS.fullLocale, pricing.currency)

  const isMemberRate = main.categories.includes(PricingCategory.MEMBER_RATE)

  let publicPrice, secondaryPublicPrice, prominentMemberPrice, secondaryMemberPrice, aggregationLabel

  if (isMemberRate) {
    if (isMultiNights) {
      if (isUSLocation) {
        publicPrice = formatPrice(alternative.amount)
        aggregationLabel = totalForYourStayLabel

        if (!isUserLoggedIn) {
          secondaryPublicPrice = formatPrice(alternative.average.amount)
        } else {
          prominentMemberPrice = formatPrice(main.amount)
        }
      } else {
        publicPrice = formatPrice(alternative.average.amount)
        aggregationLabel = averagePerNightLabel

        if (!isUserLoggedIn) {
          secondaryPublicPrice = formatPrice(alternative.amount)
        } else {
          prominentMemberPrice = formatPrice(main.average.amount)
        }
      }
    } else {
      publicPrice = formatPrice(alternative.amount)
      aggregationLabel = totalForYourStayLabel

      if (isUserLoggedIn) {
        prominentMemberPrice = formatPrice(main.amount)
      }
    }

    const publicPriceDisplay = isUserLoggedIn
      ? rateCardPriceDisplay.STRIKETHROUGH
      : rateCardPriceDisplay.REGULAR_WITH_LABEL
    const memberPriceDisplay =
      !isUserLoggedIn && isExpediaCompliant ? rateCardPriceDisplay.REGULAR_WITH_LABEL : undefined

    return {
      aggregationLabel,
      currency: pricing.currency,
      formattedTaxType: pricing.formattedTaxType,
      memberPriceDisplay,
      prominentMemberPrice,
      publicPrice,
      publicPriceDisplay,
      secondaryMemberPrice,
      secondaryPublicPrice
    }
  } else {
    if (isMultiNights) {
      if (isUSLocation) {
        publicPrice = formatPrice(main.amount)
        aggregationLabel = totalForYourStayLabel

        if (!isUserLoggedIn) {
          secondaryPublicPrice = formatPrice(main.average.amount)
        }
      } else {
        publicPrice = formatPrice(main.average.amount)
        aggregationLabel = averagePerNightLabel

        if (!isUserLoggedIn) {
          secondaryPublicPrice = formatPrice(main.amount)
        }
      }
    } else {
      publicPrice = formatPrice(main.amount)
      aggregationLabel = totalForYourStayLabel
    }

    return {
      aggregationLabel,
      currency: pricing.currency,
      formattedTaxType: pricing.formattedTaxType,
      publicPrice,
      publicPriceDisplay: rateCardPriceDisplay.REGULAR_WITH_LABEL,
      secondaryPublicPrice
    }
  }
}

export function cancellationPoliciesMapper(
  policies: ApiPricingCondition["policies"],
  simplifiedPoliciesArray: ApiPricingCondition["offer"]["pricing"]["main"]["simplifiedPolicies"][]
): UiCancellationPolicyProps | null {
  if (!simplifiedPoliciesArray?.length) return null

  const canpolPolicy = policies.find((policy) => policy.code === CodePolicy.CANPOL)

  if (!canpolPolicy?.description) return null

  const roomsCancellationPolicy = simplifiedPoliciesArray
    .map((simplifiedPolicies, index) => {
      if (!simplifiedPolicies) return null

      const cancellationIcon = mapAmenitiesIcons(simplifiedPolicies.cancellation?.code)
      if (!cancellationIcon) return null

      const color =
        simplifiedPolicies.cancellation.code.toString() === RateCode.FREE_CANCELLATION
          ? AmenitiesColor.GREEN_500
          : AmenitiesColor.CAVIAR_BLACK_700

      return {
        cancellationPolicy: {
          accessibilityIconLabel: cancellationIcon.accessibilityIconLabel,
          iconColor: color,
          iconName: cancellationIcon.iconName,
          label: simplifiedPolicies.cancellation.label,
          labelColor: color
        },
        description: canpolPolicy.description,
        id: (index + 1).toString()
      }
    })
    .filter(Boolean) as RoomCancellationPolicy[]

  if (!roomsCancellationPolicy.length) return null

  return { roomsCancellationPolicy }
}

function createAmenityDetail(
  icon: { accessibilityIconLabel: string; iconName: Icon } | null,
  label: string,
  color: AmenitiesColor = AmenitiesColor.CAVIAR_BLACK_700
): UiAmenityProps | null {
  if (!icon) return null

  return {
    accessibilityIconLabel: icon.accessibilityIconLabel,
    iconColor: color,
    iconName: icon.iconName,
    label,
    labelColor: color
  }
}

function getGuaranteeAmenity(
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"]
): UiAmenityProps | null {
  const guaranteeIcon = mapAmenitiesIcons(simplifiedPolicies.guarantee?.code)
  return createAmenityDetail(guaranteeIcon, simplifiedPolicies.guarantee?.label)
}

function getCancellationAmenity(
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"]
): UiAmenityProps | null {
  const cancellationIcon = mapAmenitiesIcons(simplifiedPolicies.cancellation?.code)
  if (!cancellationIcon) return null

  const color =
    simplifiedPolicies.cancellation.code.toString() === RateCode.FREE_CANCELLATION
      ? AmenitiesColor.GREEN_500
      : AmenitiesColor.CAVIAR_BLACK_700

  return createAmenityDetail(cancellationIcon, simplifiedPolicies.cancellation.label, color)
}

function getMealPlanAmenity(mealPlan: Offer["mealPlan"]): UiAmenityProps | null {
  const mealPlanIcon = mapAmenitiesIcons(mealPlan?.code)
  return mealPlanIcon && mealPlan?.label ? createAmenityDetail(mealPlanIcon, mealPlan.label) : null
}

function getMemberRateAmenity(
  mainPrice: Offer["pricing"]["main"],
  deduction: Offer["pricing"]["deduction"],
  isUserLogged: boolean,
  isExpediaCompliant: boolean
): UiAmenityProps | null {
  const isMainPriceMemberRate = mainPrice.categories.includes(PricingCategory.MEMBER_RATE)
  const allIcon = mapAmenitiesIcons(RateCode.ALL_ICON)

  if (!isMainPriceMemberRate || !allIcon) return null

  let label
  if (isUserLogged) {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_logged")
  } else if (isExpediaCompliant) {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_compliance", {
      amount: deduction[0].formattedAmount
    })
  } else {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_unlogged")
  }

  return createAmenityDetail(allIcon, label, AmenitiesColor.STRATOS_BLUE_800)
}

export function rateCardAmenitiesMapper(
  deduction: Offer["pricing"]["deduction"],
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"],
  mealPlan: Offer["mealPlan"],
  mainPrice: Offer["pricing"]["main"],
  isExpediaCompliant: boolean,
  isUserLogged: boolean
): UiAmenityProps[] {
  const amenities = [
    getGuaranteeAmenity(simplifiedPolicies),
    getCancellationAmenity(simplifiedPolicies),
    getMealPlanAmenity(mealPlan),
    getMemberRateAmenity(mainPrice, deduction, isUserLogged, isExpediaCompliant)
  ]

  return amenities.filter((amenity): amenity is UiAmenityProps => amenity !== null)
}

function mapAmenitiesIcons(code: string): { accessibilityIconLabel: string; iconName: Icon } | null {
  switch (code) {
    case RateCode.NO_PREPAY:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.guarantee"),
        iconName: RateIcons.CHECK
      }
    case RateCode.PREPAID:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.guarantee"),
        iconName: RateIcons.CREDIT_CARD
      }
    case RateCode.RESTRICTED_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.TIME
      }
    case RateCode.FREE_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.TIME
      }
    case RateCode.NO_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.NOT_REFUNDABLE
      }
    case RateCode.FULL_BOARD:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.HALF_BOARD:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.ALL_INCLUSIVE:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.BED_AND_BREAKFAST:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.BREAKFAST
      }
    case RateCode.ALL_ICON:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.loyalty_discount_description"
        ),
        iconName: RateIcons.ALL_ICON
      }
    default:
      return null
  }
}

export function normalizeRoomClassCode(code?: RoomClassCode): RoomClassCode {
  return code && code in RoomClassCode ? code : RoomClassCode.OTHER
}

export function buildMatchedPairs(
  offers: Offer[],
  countryMarket: string,
  nbNights: number,
  isLogged: boolean
): MatchedPair[] {
  return offers.map((offer) => {
    const offerMapped = offerMapper(offer, nbNights, countryMarket, isLogged)

    return { offerMapped }
  })
}

export function mapAndGroupByRoomClass(
  offers: Offer[],
  countryMarket: string,
  nbNights: number,
  isLogged: boolean,
  groupBy: ApiBestOffers["groupBy"]
) {
  const pairs = buildMatchedPairs(offers, countryMarket, nbNights, isLogged)

  const groupedRoomClasses = groupBy.roomClasses.reduce(
    (acc, roomClass) => {
      const code = normalizeRoomClassCode(roomClass.code)
      if (!acc[code]) {
        acc[code] = {
          bestOfferIds: [],
          code,
          title: ROOM_CLASS_TITLES[code]
        }
      }
      acc[code].bestOfferIds.push(...roomClass.bestOfferIds)
      return acc
    },
    {} as Record<RoomClassCode, { code: RoomClassCode; bestOfferIds: string[]; title: string }>
  )

  return Object.values(groupedRoomClasses).map(({ code, bestOfferIds, title }) => {
    const sectionPairs = pairs.filter((p) => bestOfferIds.includes(p.offerMapped.id))
    return { code, pairs: sectionPairs, title }
  })
}
