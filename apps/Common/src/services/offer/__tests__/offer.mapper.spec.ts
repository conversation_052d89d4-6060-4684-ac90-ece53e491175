import { CancellationPolicy, CodePolicy, PolicyGuarantee } from "../offer.enums"
import { describe, expect, it } from "vitest"
import { Policy } from "../offer.types"
import { RoomCancellationPolicy } from "design-system"
import { cancellationPoliciesMapper } from "../offer.mapper"

// Define proper types for the simplified policies
type SimplifiedPolicies = {
  cancellation: {
    code: CancellationPolicy
    label: string
  }
  guarantee: {
    code: PolicyGuarantee
    label: string
  }
}

describe("cancellationPoliciesMapper", () => {
  const mockPolicies: Policy[] = [
    {
      code: CodePolicy.CANPOL,
      description:
        "Free cancellation until 6 PM on the day of arrival. After this time, a one-night penalty will apply.",
      label: "Cancellation Policy"
    },
    {
      code: CodePolicy.GUAPOL,
      description: "Credit card guarantee required to hold this reservation.",
      label: "Guarantee Policy"
    }
  ]

  const mockSimplifiedPolicies: SimplifiedPolicies = {
    cancellation: {
      code: CancellationPolicy.FREE_CANCELLATION,
      label: "Free cancellation until 6 PM"
    },
    guarantee: {
      code: PolicyGuarantee.NO_PREPAY,
      label: "No prepayment required"
    }
  }

  describe("valid inputs", () => {
    it("should return cancellation policy for free cancellation", () => {
      const result = cancellationPoliciesMapper(mockPolicies, [mockSimplifiedPolicies])

      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(1)

      const policy = result?.roomsCancellationPolicy[0]
      expect(policy?.id).toBe("1")
      expect(policy?.description).toBe(
        "Free cancellation until 6 PM on the day of arrival. After this time, a one-night penalty will apply."
      )
      expect(policy?.cancellationPolicy.label).toBe("Free cancellation until 6 PM")
      expect(policy?.cancellationPolicy.iconName).toBe("time")
      expect(policy?.cancellationPolicy.iconColor).toBe("green-500")
      expect(policy?.cancellationPolicy.labelColor).toBe("green-500")
    })

    it("should return cancellation policy for restricted cancellation", () => {
      const restrictedSimplifiedPolicies: SimplifiedPolicies = {
        ...mockSimplifiedPolicies,
        cancellation: {
          code: CancellationPolicy.RESTRICTED_CANCELLATION,
          label: "Restricted cancellation - 48 hours notice required"
        }
      }

      const result = cancellationPoliciesMapper(mockPolicies, [restrictedSimplifiedPolicies])

      expect(result).not.toBeNull()
      const policy = result?.roomsCancellationPolicy[0]
      expect(policy?.cancellationPolicy.label).toBe("Restricted cancellation - 48 hours notice required")
      expect(policy?.cancellationPolicy.iconName).toBe("time")
      expect(policy?.cancellationPolicy.iconColor).toBe("caviarBlack-700")
      expect(policy?.cancellationPolicy.labelColor).toBe("caviarBlack-700")
    })

    it("should return cancellation policy for no cancellation", () => {
      const noCancellationSimplifiedPolicies: SimplifiedPolicies = {
        ...mockSimplifiedPolicies,
        cancellation: {
          code: CancellationPolicy.NO_CANCELLATION,
          label: "Non-refundable"
        }
      }

      const result = cancellationPoliciesMapper(mockPolicies, [noCancellationSimplifiedPolicies])

      expect(result).not.toBeNull()
      const policy = result?.roomsCancellationPolicy[0]
      expect(policy?.cancellationPolicy.label).toBe("Non-refundable")
      expect(policy?.cancellationPolicy.iconName).toBe("notRefundable")
      expect(policy?.cancellationPolicy.iconColor).toBe("caviarBlack-700")
      expect(policy?.cancellationPolicy.labelColor).toBe("caviarBlack-700")
    })

    it("should handle multiple simplified policies", () => {
      const simplifiedPolicies2: SimplifiedPolicies = {
        ...mockSimplifiedPolicies,
        cancellation: {
          code: CancellationPolicy.NO_CANCELLATION,
          label: "Non-refundable rate"
        }
      }

      const result = cancellationPoliciesMapper(mockPolicies, [mockSimplifiedPolicies, simplifiedPolicies2])

      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(2)

      const policy1 = result?.roomsCancellationPolicy[0]
      expect(policy1?.id).toBe("1")
      expect(policy1?.cancellationPolicy.label).toBe("Free cancellation until 6 PM")

      const policy2 = result?.roomsCancellationPolicy[1]
      expect(policy2?.id).toBe("2")
      expect(policy2?.cancellationPolicy.label).toBe("Non-refundable rate")
    })

    it("should set correct accessibility labels", () => {
      const result = cancellationPoliciesMapper(mockPolicies, [mockSimplifiedPolicies])

      expect(result).not.toBeNull()
      const policy = result?.roomsCancellationPolicy[0]
      expect(policy?.cancellationPolicy.accessibilityIconLabel).toBe("Cancellation Policy:")
    })
  })

  describe("edge cases", () => {
    it("should return null when simplifiedPoliciesArray is empty", () => {
      const result = cancellationPoliciesMapper(mockPolicies, [])

      expect(result).toBeNull()
    })

    it("should return null when simplifiedPoliciesArray is null/undefined", () => {
      const result1 = cancellationPoliciesMapper(mockPolicies, null as unknown as SimplifiedPolicies[])
      expect(result1).toBeNull()

      const result2 = cancellationPoliciesMapper(mockPolicies, undefined as unknown as SimplifiedPolicies[])
      expect(result2).toBeNull()
    })

    it("should return null when no CANPOL policy is found", () => {
      const policiesWithoutCanpol: Policy[] = [
        {
          code: CodePolicy.GUAPOL,
          description: "Credit card guarantee required.",
          label: "Guarantee Policy"
        }
      ]

      const result = cancellationPoliciesMapper(policiesWithoutCanpol, [mockSimplifiedPolicies])

      expect(result).toBeNull()
    })

    it("should return null when CANPOL policy has no description", () => {
      const policiesWithoutDescription: Policy[] = [
        {
          code: CodePolicy.CANPOL,
          description: "",
          label: "Cancellation Policy"
        }
      ]

      const result = cancellationPoliciesMapper(policiesWithoutDescription, [mockSimplifiedPolicies])

      expect(result).toBeNull()
    })

    it("should filter out null simplified policies", () => {
      const mixedSimplifiedPolicies: (SimplifiedPolicies | null)[] = [
        mockSimplifiedPolicies,
        null,
        {
          ...mockSimplifiedPolicies,
          cancellation: {
            code: CancellationPolicy.NO_CANCELLATION,
            label: "Non-refundable"
          }
        }
      ]

      const result = cancellationPoliciesMapper(mockPolicies, mixedSimplifiedPolicies as SimplifiedPolicies[])

      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(2) // Should skip the null entry
    })

    it("should return null when no valid simplified policies remain after filtering", () => {
      const invalidSimplifiedPolicies = [null, undefined]

      const result = cancellationPoliciesMapper(
        mockPolicies,
        invalidSimplifiedPolicies as unknown as SimplifiedPolicies[]
      )

      expect(result).toBeNull()
    })

    it("should handle unknown cancellation codes gracefully", () => {
      const unknownCancellationSimplifiedPolicies: SimplifiedPolicies = {
        ...mockSimplifiedPolicies,
        cancellation: {
          code: "UNKNOWN_CODE" as CancellationPolicy,
          label: "Unknown cancellation policy"
        }
      }

      const result = cancellationPoliciesMapper(mockPolicies, [unknownCancellationSimplifiedPolicies])

      // Should return null because mapAmenitiesIcons returns null for unknown codes
      expect(result).toBeNull()
    })

    it("should handle missing cancellation in simplified policies", () => {
      const simplifiedPoliciesWithoutCancellation = {
        guarantee: {
          code: PolicyGuarantee.NO_PREPAY,
          label: "No prepayment required"
        }
      } as unknown as SimplifiedPolicies

      const result = cancellationPoliciesMapper(mockPolicies, [simplifiedPoliciesWithoutCancellation])

      expect(result).toBeNull()
    })
  })

  describe("data consistency", () => {
    it("should maintain same description across all rooms from same CANPOL policy", () => {
      const multipleSimplifiedPolicies: SimplifiedPolicies[] = [
        mockSimplifiedPolicies,
        {
          ...mockSimplifiedPolicies,
          cancellation: {
            code: CancellationPolicy.RESTRICTED_CANCELLATION,
            label: "Different label but same description source"
          }
        }
      ]

      const result = cancellationPoliciesMapper(mockPolicies, multipleSimplifiedPolicies)

      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(2)

      // Both should have the same description from the same CANPOL policy
      const policy1 = result?.roomsCancellationPolicy[0]
      const policy2 = result?.roomsCancellationPolicy[1]
      expect(policy1?.description).toBe(policy2?.description)
      expect(policy1?.description).toBe(
        "Free cancellation until 6 PM on the day of arrival. After this time, a one-night penalty will apply."
      )
    })

    it("should generate sequential IDs for multiple rooms", () => {
      const multipleSimplifiedPolicies = Array(5).fill(mockSimplifiedPolicies) as SimplifiedPolicies[]

      const result = cancellationPoliciesMapper(mockPolicies, multipleSimplifiedPolicies)

      expect(result).not.toBeNull()
      expect(result?.roomsCancellationPolicy).toHaveLength(5)

      result?.roomsCancellationPolicy.forEach((policy: RoomCancellationPolicy, index: number) => {
        expect(policy.id).toBe((index + 1).toString())
      })
    })
  })
})
