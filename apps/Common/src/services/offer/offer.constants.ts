import { RoomClassCode } from "./offer.enums"
import { i18n } from "../../i18n"

const { global } = i18n

export const ROOM_CLASS_TITLES: Record<RoomClassCode, string> = {
  [RoomClassCode.APT]: global.t("components.stay_view.room_section.rooms_label.apt"),
  [RoomClassCode.APV]: global.t("components.stay_view.room_section.rooms_label.apv"),
  [RoomClassCode.BEA]: global.t("components.stay_view.room_section.rooms_label.bea"),
  [RoomClassCode.BEL]: global.t("components.stay_view.room_section.rooms_label.bel"),
  [RoomClassCode.CLU]: global.t("components.stay_view.room_section.rooms_label.clu"),
  [RoomClassCode.CON]: global.t("components.stay_view.room_section.rooms_label.con"),
  [RoomClassCode.DHV]: global.t("components.stay_view.room_section.rooms_label.dhv"),
  [RoomClassCode.DLF]: global.t("components.stay_view.room_section.rooms_label.dlf"),
  [RoomClassCode.DLL]: global.t("components.stay_view.room_section.rooms_label.dll"),
  [RoomClassCode.DLN]: global.t("components.stay_view.room_section.rooms_label.dln"),
  [RoomClassCode.DLS]: global.t("components.stay_view.room_section.rooms_label.dls"),
  [RoomClassCode.DLT]: global.t("components.stay_view.room_section.rooms_label.dlt"),
  [RoomClassCode.DLV]: global.t("components.stay_view.room_section.rooms_label.dlv"),
  [RoomClassCode.DLX]: global.t("components.stay_view.room_section.rooms_label.dlx"),
  [RoomClassCode.DLXB]: global.t("components.stay_view.room_section.rooms_label.dlxb"),
  [RoomClassCode.DMV]: global.t("components.stay_view.room_section.rooms_label.dmv"),
  [RoomClassCode.DPV]: global.t("components.stay_view.room_section.rooms_label.dpv"),
  [RoomClassCode.DPX]: global.t("components.stay_view.room_section.rooms_label.dpx"),
  [RoomClassCode.DSV]: global.t("components.stay_view.room_section.rooms_label.dsv"),
  [RoomClassCode.DUN]: global.t("components.stay_view.room_section.rooms_label.dun"),
  [RoomClassCode.DVR]: global.t("components.stay_view.room_section.rooms_label.dvr"),
  [RoomClassCode.DVS]: global.t("components.stay_view.room_section.rooms_label.dvs"),
  [RoomClassCode.DXX]: global.t("components.stay_view.room_section.rooms_label.dxx"),
  [RoomClassCode.EST]: global.t("components.stay_view.room_section.rooms_label.est"),
  [RoomClassCode.EXC]: global.t("components.stay_view.room_section.rooms_label.exc"),
  [RoomClassCode.EXE]: global.t("components.stay_view.room_section.rooms_label.exe"),
  [RoomClassCode.EXS]: global.t("components.stay_view.room_section.rooms_label.exs"),
  [RoomClassCode.FAM]: global.t("components.stay_view.room_section.rooms_label.fam"),
  [RoomClassCode.FAR]: global.t("components.stay_view.room_section.rooms_label.far"),
  [RoomClassCode.FAS]: global.t("components.stay_view.room_section.rooms_label.fas"),
  [RoomClassCode.FCO]: global.t("components.stay_view.room_section.rooms_label.fco"),
  [RoomClassCode.FG]: global.t("components.stay_view.room_section.rooms_label.fg"),
  [RoomClassCode.FGS]: global.t("components.stay_view.room_section.rooms_label.fgs"),
  [RoomClassCode.FHF]: global.t("components.stay_view.room_section.rooms_label.fhf"),
  [RoomClassCode.FLL]: global.t("components.stay_view.room_section.rooms_label.fll"),
  [RoomClassCode.FLX]: global.t("components.stay_view.room_section.rooms_label.flx"),
  [RoomClassCode.FME]: global.t("components.stay_view.room_section.rooms_label.fme"),
  [RoomClassCode.FMF]: global.t("components.stay_view.room_section.rooms_label.fmf"),
  [RoomClassCode.FMS]: global.t("components.stay_view.room_section.rooms_label.fms"),
  [RoomClassCode.FMT]: global.t("components.stay_view.room_section.rooms_label.fmt"),
  [RoomClassCode.FMV]: global.t("components.stay_view.room_section.rooms_label.fmv"),
  [RoomClassCode.FPR]: global.t("components.stay_view.room_section.rooms_label.fpr"),
  [RoomClassCode.FSU]: global.t("components.stay_view.room_section.rooms_label.fsu"),
  [RoomClassCode.FVS]: global.t("components.stay_view.room_section.rooms_label.fvs"),
  [RoomClassCode.GBS]: global.t("components.stay_view.room_section.rooms_label.gbs"),
  [RoomClassCode.GDN]: global.t("components.stay_view.room_section.rooms_label.gdn"),
  [RoomClassCode.GGS]: global.t("components.stay_view.room_section.rooms_label.ggs"),
  [RoomClassCode.GLD]: global.t("components.stay_view.room_section.rooms_label.gld"),
  [RoomClassCode.GLJ]: global.t("components.stay_view.room_section.rooms_label.glj"),
  [RoomClassCode.GLL]: global.t("components.stay_view.room_section.rooms_label.gll"),
  [RoomClassCode.GLN]: global.t("components.stay_view.room_section.rooms_label.gln"),
  [RoomClassCode.GLS]: global.t("components.stay_view.room_section.rooms_label.gls"),
  [RoomClassCode.GLV]: global.t("components.stay_view.room_section.rooms_label.glv"),
  [RoomClassCode.GRN]: global.t("components.stay_view.room_section.rooms_label.grn"),
  [RoomClassCode.GSG]: global.t("components.stay_view.room_section.rooms_label.gsg"),
  [RoomClassCode.GSO]: global.t("components.stay_view.room_section.rooms_label.gso"),
  [RoomClassCode.GSS]: global.t("components.stay_view.room_section.rooms_label.gss"),
  [RoomClassCode.GST]: global.t("components.stay_view.room_section.rooms_label.gst"),
  [RoomClassCode.HIG]: global.t("components.stay_view.room_section.rooms_label.hig"),
  [RoomClassCode.HNV]: global.t("components.stay_view.room_section.rooms_label.hnv"),
  [RoomClassCode.HPR]: global.t("components.stay_view.room_section.rooms_label.hpr"),
  [RoomClassCode.HTL]: global.t("components.stay_view.room_section.rooms_label.htl"),
  [RoomClassCode.JDX]: global.t("components.stay_view.room_section.rooms_label.jdx"),
  [RoomClassCode.JNC]: global.t("components.stay_view.room_section.rooms_label.jnc"),
  [RoomClassCode.JNL]: global.t("components.stay_view.room_section.rooms_label.jnl"),
  [RoomClassCode.JNR]: global.t("components.stay_view.room_section.rooms_label.jnr"),
  [RoomClassCode.JNRR]: global.t("components.stay_view.room_section.rooms_label.jnrr"),
  [RoomClassCode.JNS]: global.t("components.stay_view.room_section.rooms_label.jns"),
  [RoomClassCode.JSG]: global.t("components.stay_view.room_section.rooms_label.jsg"),
  [RoomClassCode.JSU]: global.t("components.stay_view.room_section.rooms_label.jsu"),
  [RoomClassCode.KIL]: global.t("components.stay_view.room_section.rooms_label.kil"),
  [RoomClassCode.LDX]: global.t("components.stay_view.room_section.rooms_label.ldx"),
  [RoomClassCode.LGS]: global.t("components.stay_view.room_section.rooms_label.lgs"),
  [RoomClassCode.LUX]: global.t("components.stay_view.room_section.rooms_label.lux"),
  [RoomClassCode.LUXR]: global.t("components.stay_view.room_section.rooms_label.luxr"),
  [RoomClassCode.MAN]: global.t("components.stay_view.room_section.rooms_label.man"),
  [RoomClassCode.MDS]: global.t("components.stay_view.room_section.rooms_label.mds"),
  [RoomClassCode.MLB]: global.t("components.stay_view.room_section.rooms_label.mlb"),
  [RoomClassCode.MOD]: global.t("components.stay_view.room_section.rooms_label.mod"),
  [RoomClassCode.MTN]: global.t("components.stay_view.room_section.rooms_label.mtn"),
  [RoomClassCode.OBS]: global.t("components.stay_view.room_section.rooms_label.obs"),
  [RoomClassCode.OCV]: global.t("components.stay_view.room_section.rooms_label.ocv"),
  [RoomClassCode.OST]: global.t("components.stay_view.room_section.rooms_label.ost"),
  [RoomClassCode.OTHER]: global.t("components.stay_view.room_section.rooms_label.other"),
  [RoomClassCode.PEN]: global.t("components.stay_view.room_section.rooms_label.pen"),
  [RoomClassCode.PLV]: global.t("components.stay_view.room_section.rooms_label.plv"),
  [RoomClassCode.PNT]: global.t("components.stay_view.room_section.rooms_label.pnt"),
  [RoomClassCode.POV]: global.t("components.stay_view.room_section.rooms_label.pov"),
  [RoomClassCode.PRE]: global.t("components.stay_view.room_section.rooms_label.pre"),
  [RoomClassCode.PRM]: global.t("components.stay_view.room_section.rooms_label.prm"),
  [RoomClassCode.PRS]: global.t("components.stay_view.room_section.rooms_label.prs"),
  [RoomClassCode.PRSR]: global.t("components.stay_view.room_section.rooms_label.prsr"),
  [RoomClassCode.PRV]: global.t("components.stay_view.room_section.rooms_label.prv"),
  [RoomClassCode.PS]: global.t("components.stay_view.room_section.rooms_label.ps"),
  [RoomClassCode.PST]: global.t("components.stay_view.room_section.rooms_label.pst"),
  [RoomClassCode.RDV]: global.t("components.stay_view.room_section.rooms_label.rdv"),
  [RoomClassCode.RED]: global.t("components.stay_view.room_section.rooms_label.red"),
  [RoomClassCode.RES]: global.t("components.stay_view.room_section.rooms_label.res"),
  [RoomClassCode.ROY]: global.t("components.stay_view.room_section.rooms_label.roy"),
  [RoomClassCode.RTE]: global.t("components.stay_view.room_section.rooms_label.rte"),
  [RoomClassCode.SAL]: global.t("components.stay_view.room_section.rooms_label.sal"),
  [RoomClassCode.SAS]: global.t("components.stay_view.room_section.rooms_label.sas"),
  [RoomClassCode.SDL]: global.t("components.stay_view.room_section.rooms_label.sdl"),
  [RoomClassCode.SGO]: global.t("components.stay_view.room_section.rooms_label.sgo"),
  [RoomClassCode.SGS]: global.t("components.stay_view.room_section.rooms_label.sgs"),
  [RoomClassCode.SI2]: global.t("components.stay_view.room_section.rooms_label.si2"),
  [RoomClassCode.SIG]: global.t("components.stay_view.room_section.rooms_label.sig"),
  [RoomClassCode.SOF]: global.t("components.stay_view.room_section.rooms_label.sof"),
  [RoomClassCode.SPS]: global.t("components.stay_view.room_section.rooms_label.sps"),
  [RoomClassCode.SPV]: global.t("components.stay_view.room_section.rooms_label.spv"),
  [RoomClassCode.SSD]: global.t("components.stay_view.room_section.rooms_label.ssd"),
  [RoomClassCode.SSE]: global.t("components.stay_view.room_section.rooms_label.sse"),
  [RoomClassCode.SST]: global.t("components.stay_view.room_section.rooms_label.sst"),
  [RoomClassCode.STA]: global.t("components.stay_view.room_section.rooms_label.sta"),
  [RoomClassCode.STB]: global.t("components.stay_view.room_section.rooms_label.stb"),
  [RoomClassCode.STC]: global.t("components.stay_view.room_section.rooms_label.stc"),
  [RoomClassCode.STE]: global.t("components.stay_view.room_section.rooms_label.ste"),
  [RoomClassCode.STE1]: global.t("components.stay_view.room_section.rooms_label.ste1"),
  [RoomClassCode.STE1G]: global.t("components.stay_view.room_section.rooms_label.ste1g"),
  [RoomClassCode.STE2]: global.t("components.stay_view.room_section.rooms_label.ste2"),
  [RoomClassCode.STE2G]: global.t("components.stay_view.room_section.rooms_label.ste2g"),
  [RoomClassCode.STER]: global.t("components.stay_view.room_section.rooms_label.ster"),
  [RoomClassCode.STF]: global.t("components.stay_view.room_section.rooms_label.stf"),
  [RoomClassCode.STG]: global.t("components.stay_view.room_section.rooms_label.stg"),
  [RoomClassCode.STH]: global.t("components.stay_view.room_section.rooms_label.sth"),
  [RoomClassCode.STI]: global.t("components.stay_view.room_section.rooms_label.sti"),
  [RoomClassCode.STJ]: global.t("components.stay_view.room_section.rooms_label.stj"),
  [RoomClassCode.STK]: global.t("components.stay_view.room_section.rooms_label.stk"),
  [RoomClassCode.STL]: global.t("components.stay_view.room_section.rooms_label.stl"),
  [RoomClassCode.STM]: global.t("components.stay_view.room_section.rooms_label.stm"),
  [RoomClassCode.STS]: global.t("components.stay_view.room_section.rooms_label.sts"),
  [RoomClassCode.STV]: global.t("components.stay_view.room_section.rooms_label.stv"),
  [RoomClassCode.SUI]: global.t("components.stay_view.room_section.rooms_label.sui"),
  [RoomClassCode.SUP]: global.t("components.stay_view.room_section.rooms_label.sup"),
  [RoomClassCode.SUT]: global.t("components.stay_view.room_section.rooms_label.sut"),
  [RoomClassCode.V2B]: global.t("components.stay_view.room_section.rooms_label.v2b"),
  [RoomClassCode.V3B]: global.t("components.stay_view.room_section.rooms_label.v3b"),
  [RoomClassCode.VIL]: global.t("components.stay_view.room_section.rooms_label.vil"),
  [RoomClassCode.WHA]: global.t("components.stay_view.room_section.rooms_label.wha"),
  [RoomClassCode.XVS]: global.t("components.stay_view.room_section.rooms_label.xvs")
}
