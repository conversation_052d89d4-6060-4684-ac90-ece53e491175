<script setup lang="ts">
import { ErrorTypes, useHTTPError } from "./composables/useHTTPError"
import { Semaphores, useApiSemaphore } from "./composables/useApiSemaphore"
import { computed, onMounted, watch } from "vue"
import {
  useCommerceTracking,
  useCurrentWindowSize,
  useLoader,
  useModal,
  usePosCurrencyHandler,
  usePosLanguageHandler,
  useResetProcess,
  useSearch
} from "./composables"
import { useRoute, useRouter } from "vue-router"
import AllHotelRedirectModal from "./components/AllHotelRedirectModal/AllHotelRedirectModal.vue"
import { AllHotels } from "./global/consts"
import { Currency } from "./global/enums"
import Footer from "./components/Footer/Footer.vue"
import Header from "./components/Header/Header.vue"
import SearchEngineDesktop from "./components/SearchEngine/SearchEngineDesktop/SearchEngineDesktop.vue"
import SearchEngineModal from "./components/SearchEngine/SearchEngineModal/SearchEngineModal.vue"
import Sidebar from "./components/Sidebar/Sidebar.vue"
import Stepper from "./components/Stepper/Stepper.vue"
import TravelProRedirectModal from "./components/TravelProRedirectModal/TravelProRedirectModal.vue"
import { fetchCountries } from "./services/referentials/referentials.fetch"
import { fetchHotelsByIds } from "./services/hotels/hotels.fetch"
import { getGlobalConfig } from "./global/config"
import { getUserDetails } from "./services/users/users.fetch"
import { hotelsIdByRegion } from "./services/hotels/hotels.consts"
import { replaceUriParams } from "./helpers/uriHelper"

import { updateCurrencyCookies } from "./services/pos/pos.fetch"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useIdentificationPlainText } from "./composables/useIdentificationPlainText"
import { useIdentificationRefresher } from "./composables/useIdentificationRefresher"
import { useOffersAndAccommodations } from "./composables/useOffersAndAccommodations"
import { usePosStore } from "./stores/pos/pos"
import { useReferentialsStore } from "@stores/referentials/referentials"
import { useSearchQueryParams } from "./composables/useSearchQueryParams"
import { useUserPOS } from "./composables/useUserPOS"
import { useUserStore } from "./stores/user/user"

const config = getGlobalConfig()

const router = useRouter()
const { updateIsLoading, updateHotelsList } = useHotelsStore()
const { updateCountries } = useReferentialsStore()
const { loggedUser } = useUserStore()
const cookies = useCookies()
const { hotel, iataCode } = useSearch()
const { isDesktop } = useCurrentWindowSize()
const { refreshIdentification } = useIdentificationPlainText()
const { fetchCommerceTracking } = useCommerceTracking()
const { fetchOffers } = useOffersAndAccommodations()
const { resetProcess } = useResetProcess()
useIdentificationRefresher()
const { queryParams } = useSearchQueryParams()
const { setSemaphore } = useApiSemaphore()

const {
  fetchPosCountriesGroupedByContinentInStore,
  findContinentCodeByCountry,
  findContinentCodeByCurrencyCode,
  findCountryLabelByCountryCodeAndLocale,
  findCurrencyObjectByAccorCurrencyCode
} = usePosStore()

const { countriesGeographicalArea, countryRegionLabel } = usePosLanguageHandler()
const { currenciesGeographicalArea, currencyCode, resetStateCurrency } = usePosCurrencyHandler()

const { userPOS, refreshPOS } = useUserPOS()

const route = useRoute()
const layout = computed(() => route.meta.layout || "div")
const { locale } = useI18n()
const { error: hotelsError, generateError } = useHTTPError("hotels")
const { error: countriesError } = useHTTPError("countries")

const homepageUri = computed(() => {
  return replaceUriParams(config.legacy.homePage, "", locale.value)
})

async function getHotels() {
  updateIsLoading(true)
  hotelsError.value = false

  const hotelIds = [
    ...hotelsIdByRegion.africa,
    ...hotelsIdByRegion.asia,
    ...hotelsIdByRegion.europe,
    ...hotelsIdByRegion.northAmerica,
    ...hotelsIdByRegion.southAmerica
  ]

  try {
    const hotelsResult = await fetchHotelsByIds(hotelIds)

    updateHotelsList({
      africa: hotelsResult.results
        .filter((result) => hotelsIdByRegion.africa.includes(result.hotel.id))
        .map((result) => result.hotel),
      asia: hotelsResult.results
        .filter((result) => hotelsIdByRegion.asia.includes(result.hotel.id))
        .map((result) => result.hotel),
      europe: hotelsResult.results
        .filter((result) => hotelsIdByRegion.europe.includes(result.hotel.id))
        .map((result) => result.hotel),
      northAmerica: hotelsResult.results
        .filter((result) => hotelsIdByRegion.northAmerica.includes(result.hotel.id))
        .map((result) => result.hotel),
      southAmerica: hotelsResult.results
        .filter((result) => hotelsIdByRegion.southAmerica.includes(result.hotel.id))
        .map((result) => result.hotel)
    })
  } catch {
    fetchHotelError()
  } finally {
    updateIsLoading(false)
  }
}

function fetchHotelError() {
  hotelsError.value = generateError(ErrorTypes.GENERIC)
  return { results: [] }
}

async function getCountries() {
  try {
    countriesError.value = false
    const countries = await fetchCountries()

    updateCountries(countries)
  } catch {
    countriesError.value = generateError(ErrorTypes.GENERIC)
  }
}

const { openModal: openHotelModal } = useModal("hotel")
const isHotelInBlacklist = () => {
  if (hotel.value?.id && AllHotels.includes(hotel.value?.id)) {
    openHotelModal()
    return true
  }

  return false
}

const { openModal: openTravelProModal } = useModal("travel-pro")
const hasRedirectIataCode = () => {
  if (iataCode.value) {
    openTravelProModal()
    return true
  }
}

const { loading } = useLoader("search")

async function handleSearch() {
  if (hasRedirectIataCode() || isHotelInBlacklist()) return

  loading.value = true

  resetProcess()

  try {
    await router.push({
      ...route,
      query: {
        ...queryParams.value
      },
      replace: true
    })

    await fetchOffers()
  } finally {
    loading.value = false
  }
}

watch(locale, () => {
  document.documentElement.setAttribute("lang", locale.value)
})

onMounted(async () => {
  const expires = new Date()
  expires.setTime(expires.getTime() + 60 * 1000 * 60 * 24 * 365) // 1 year
  const appDomain = config.appDomain

  cookies.set("nga", true, { domain: appDomain, path: "/" })
  cookies.set("app_domain", appDomain, { domain: appDomain, expires: expires, path: "/" })
  cookies.set("app_base_url", window.location.origin, { domain: appDomain, expires: expires, path: "/" })

  const oidcUserLogged = cookies.get("oidc_user_logged")

  if (oidcUserLogged && !loggedUser?.id) {
    await getUserDetails()
  } else {
    setSemaphore(Semaphores.userDetails, false)
  }
  fetchCommerceTracking(appDomain)
  refreshIdentification()

  getHotels()
  getCountries()

  // init pos country data from store
  await fetchPosCountriesGroupedByContinentInStore()

  const userLocalization = cookies.get("userLocalization")
  const userLang = cookies.get("userLang")

  countriesGeographicalArea.value = findContinentCodeByCountry(userLocalization)
  countryRegionLabel.value = findCountryLabelByCountryCodeAndLocale(userLocalization, userLang)

  const userCurrency = cookies.get("userCurrency")

  const currencyExists = findCurrencyObjectByAccorCurrencyCode(userCurrency)

  // only if bad currency is set in qp
  if (!currencyExists) {
    const currencyObject = findCurrencyObjectByAccorCurrencyCode(Currency.EUR)

    if (!currencyObject) return

    await updateCurrencyCookies(currencyObject)
    refreshPOS()
    resetStateCurrency()
  }
  /**
   *  If user don't have the cookies set, that will not work and create an infinite loading in POS header.
   *  In this case the watcher in userPosCurrencyHandler will do the job after POS auto set
   */
  if (userLocalization && userCurrency) {
    currenciesGeographicalArea.value = await findContinentCodeByCurrencyCode(userCurrency, userLocalization)
  }

  currencyCode.value = userPOS.currency
})
</script>

<template>
  <component :is="layout">
    <template #header>
      <Header :logo="config.logo" :logo-link="homepageUri" />
    </template>

    <template #subheader>
      <div class="Subheader">
        <div class="Subheader__content">
          <template v-if="route.name === 'stay'">
            <SearchEngineDesktop v-if="isDesktop" :loading="loading" @search-engine-desktop::check="handleSearch" />
            <template v-else>
              <SearchEngineModal :loading="loading" @search-engine-modal::check="handleSearch" />
            </template>
          </template>
        </div>
      </div>
    </template>

    <template #main-content>
      <Stepper />
      <AllHotelRedirectModal />
      <TravelProRedirectModal />
      <RouterView />
    </template>

    <!-- Step 5 (summary) has no sidebar -->
    <template v-if="route.name !== 'summary'" #sidebar-content>
      <Sidebar />
    </template>

    <template #footer>
      <Footer />
    </template>
  </component>
</template>
