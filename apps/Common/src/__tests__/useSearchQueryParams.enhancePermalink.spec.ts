import { beforeEach, describe, expect, it, vi } from "vitest"
import { PricingCategory } from "../services/offer/offer.enums"
import { ref } from "vue"

// Mock the dependencies
const mockConfig = {
  step3Permalink: {
    client: "test-client",
    host: "https://test.example.com",
    path: "/test-path"
  }
}

const mockUserPOS = {
  currency: "EUR",
  locale: "en"
}

// Define the type for mock offers
type MockOffer = {
  id: string
  rate: {
    id: string
  }
  pricing: {
    main: {
      categories: PricingCategory[]
    }
  }
}

const mockLoggedUser = ref({ id: "" })
const mockSelectedOffers = ref<MockOffer[]>([])
const mockRooms = ref([
  {
    accessibility: false,
    adults: 2,
    children: 0,
    childrenAges: [],
    id: 0,
    rateCode: "test-rate-id",
    rateId: "test-rate-id"
  }
])

vi.mock("../global/config", () => ({
  getGlobalConfig: () => mockConfig
}))

vi.mock("../composables/useUserPOS", () => ({
  useUserPOS: () => ({
    userPOS: mockUserPOS
  })
}))

vi.mock("../stores/user/user", () => ({
  useUserStore: () => ({
    loggedUser: mockLoggedUser.value
  })
}))

vi.mock("../composables/useBasket", () => ({
  useBasket: () => ({
    selectedOffers: mockSelectedOffers
  })
}))

// Mock useSearch to return test data with dynamic rooms
vi.mock("../composables/useSearch", () => ({
  useSearch: () => ({
    dateIn: ref(new Date("2024-01-01")),
    hotelCode: ref("TEST_HOTEL"),
    iataCode: ref("TEST_IATA"),
    lengthOfStay: ref(3),
    promoCode: ref("TEST_PROMO"),
    rooms: mockRooms,
    specialRate: ref("NONE")
  })
}))

// Mock vue-router
vi.mock("vue-router", () => ({
  useRouter: () => ({
    beforeEach: vi.fn(),
    currentRoute: {
      value: {
        query: {}
      }
    }
  })
}))

describe("enhancePermalink - Rate Code DR Addition", () => {
  const mockOffer: MockOffer = {
    id: "test-offer-id",
    pricing: {
      main: {
        categories: [PricingCategory.MEMBER_RATE]
      }
    },
    rate: {
      id: "test-rate-id"
    }
  }

  const mockOfferNonMember: MockOffer = {
    id: "test-offer-id-non-member",
    pricing: {
      main: {
        categories: [PricingCategory.CORPORATE_NEGOTIATED_RATE]
      }
    },
    rate: {
      id: "test-rate-id-non-member"
    }
  }

  beforeEach(() => {
    // Reset mock values
    mockLoggedUser.value = { id: "" }
    mockSelectedOffers.value = []
    mockRooms.value = [
      {
        accessibility: false,
        adults: 2,
        children: 0,
        childrenAges: [],
        id: 0,
        rateCode: "test-rate-id",
        rateId: "test-rate-id"
      }
    ]
  })

  it("should not append '-DR' when user is not logged in", async () => {
    // User is not logged in
    mockLoggedUser.value = { id: "" }
    mockSelectedOffers.value = []

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id")
  })

  it("should not append '-DR' when user is logged in but no matching offer found", async () => {
    // User is logged in but no offers
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = []

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id")
  })

  it("should not append '-DR' when user is logged in and offer found but not member rate", async () => {
    // User is logged in with non-member rate offer
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = [mockOfferNonMember]

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id")
  })

  it("should append '-DR' when user is logged in and offer found with member rate", async () => {
    // User is logged in with member rate offer
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = [mockOffer]

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id-DR")
  })

  it("should handle multiple rooms with different rate codes correctly", async () => {
    // Set up multiple rooms with different rate codes
    mockRooms.value = [
      {
        accessibility: false,
        adults: 2,
        children: 0,
        childrenAges: [],
        id: 0,
        rateCode: "test-rate-id",
        rateId: "test-rate-id"
      },
      {
        accessibility: false,
        adults: 1,
        children: 0,
        childrenAges: [],
        id: 1,
        rateCode: "test-rate-id-non-member",
        rateId: "test-rate-id-non-member"
      }
    ]

    // User is logged in with both member and non-member rate offers
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = [mockOffer, mockOfferNonMember]

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    // The first room should have the member rate with -DR appended
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id-DR")
    // The second room should have the non-member rate without -DR
    expect(enhancePermalink.value?.searchParams.get("product[1][rateCode]")).toBe("test-rate-id-non-member")
  })

  it("should handle multiple rooms where one has no matching offer", async () => {
    // Set up multiple rooms with different rate codes
    mockRooms.value = [
      {
        accessibility: false,
        adults: 2,
        children: 0,
        childrenAges: [],
        id: 0,
        rateCode: "test-rate-id",
        rateId: "test-rate-id"
      },
      {
        accessibility: false,
        adults: 1,
        children: 0,
        childrenAges: [],
        id: 1,
        rateCode: "unmapped-rate-id",
        rateId: "unmapped-rate-id"
      }
    ]

    // User is logged in with only one matching offer
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = [mockOffer] // Only first room has matching offer

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    // The first room should have the member rate with -DR appended
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id-DR")
    // The second room should have the original rate code (no matching offer)
    expect(enhancePermalink.value?.searchParams.get("product[1][rateCode]")).toBe("unmapped-rate-id")
  })

  it("should include all required parameters in the enhanced permalink", async () => {
    // User is logged in with member rate offer
    mockLoggedUser.value = { id: "user-123" }
    mockSelectedOffers.value = [mockOffer]

    const { useSearchQueryParams } = await import("../composables/useSearchQueryParams")
    const { enhancePermalink } = useSearchQueryParams()

    expect(enhancePermalink.value).toBeDefined()
    expect(enhancePermalink.value?.searchParams.get("client")).toBe("test-client")
    expect(enhancePermalink.value?.searchParams.get("languageCode")).toBe("en")
    expect(enhancePermalink.value?.searchParams.get("currencyCode")).toBe("EUR")
    expect(enhancePermalink.value?.searchParams.get("dateIn")).toBe("2024-01-01")
    expect(enhancePermalink.value?.searchParams.get("lengthOfStayValue")).toBe("3")
    expect(enhancePermalink.value?.searchParams.get("hotelCodes")).toBe("TEST_HOTEL")
    expect(enhancePermalink.value?.searchParams.get("product[0][rateCode]")).toBe("test-rate-id-DR")
  })
})
