import { BrandForm } from "@shared/types/FormStructure"
import { i18n } from "./i18n"

export default <BrandForm>{
  lockFurtherSteps: true,
  sections: [
    {
      actions: [{ type: "update-basket-summary" }],
      fields: [
        { title: i18n.global.t("form.get_acquainted"), type: "text" },
        { keys: ["mainBeneficiaryTitle"], type: "fields" },
        { keys: ["mainBeneficiaryFirstName"], type: "fields" },
        { keys: ["mainBeneficiaryLastName"], type: "fields" },
        { description: i18n.global.t("form.nationality_helper"), keys: ["nationality"], type: "fields" },
        { title: i18n.global.t("form.how_to_contact_you"), type: "text" },
        {
          description: i18n.global.t("form.phone_helper"),
          label: i18n.global.t("form.phone"),
          type: "helper"
        },
        { keys: ["phoneCountryCode", "phoneNumber"], type: "fields" },
        { description: i18n.global.t("form.email_helper"), keys: ["emailAddress"], type: "fields" },
        { title: i18n.global.t("form.where_do_you_live"), type: "text" },
        { keys: ["country"], type: "fields" },
        { type: "loyalty" },
        {
          description: i18n.global.t("form.stay_preference_helper"),
          fields: [
            {
              optional: true,
              title: i18n.global.t("form.purpose_of_stay"),
              type: "text"
            },
            { keys: ["purposeOfStay"], type: "fields" },
            {
              optional: true,
              subtitle: i18n.global.t("form.special_request_subtitle"),
              title: i18n.global.t("form.special_request"),
              type: "text"
            },
            {
              description: i18n.global.t("form.special_request_helper"),
              keys: ["messageToHotel"],
              label: i18n.global.t("form.custom_message"),
              type: "fields"
            },
            {
              optional: true,
              subtitle: i18n.global.t("form.estimated_time_of_arrival_helper"),
              title: i18n.global.t("form.estimated_time_of_arrival"),
              type: "text"
            },
            { keys: ["arrivalHour"], type: "fields" }
          ],
          key: "stay-preference",
          optional: true,
          title: i18n.global.t("form.stay_preference"),
          type: "accordion"
        }
      ],
      key: "guest-informations",
      subtitle: i18n.global.t("form.guest_informations_subtitle"),
      title: i18n.global.t("form.guest_informations")
    },
    {
      fields: [],
      key: "payment",
      title: i18n.global.t("form.guarantee_your_reservation")
    }
  ]
}
