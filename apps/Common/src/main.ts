// eslint-disable-next-line
import "design-system/main.scss"
import "@accor/business-referer"
import { loadGlobalConfig, setGlobalConfig } from "./global/config"
import App from "./App.vue"
import { BrandInitConfig } from "@shared/types"
import { createApp } from "vue"
import { createHead } from "@vueuse/head"
import { createPinia } from "pinia"
import { defineAdsTheme } from "@accor/ads-components"
import { i18n } from "./i18n"
import { initSplunk } from "./services/utils/tracking/tracking.splunk"
import router from "./router"
import { vueLogger } from "./services/logger"

const envConf = await loadGlobalConfig()

//manage page title
// eslint-disable-next-line @typescript-eslint/no-explicit-any
router.afterEach((to: any) => {
  document.title = to.meta.title || import.meta.env.VITE_APP_HEADER_TITLE
})

export const initializeApp = async (config: BrandInitConfig) => {
  const hostName = window.location.hostname
  const parts = hostName.split(".")

  // Take only the last two parts and join them with a dot
  const appDomain = "." + parts.slice(-2).join(".")

  // Prepare env variables
  envConf.appDomain = appDomain

  //Init RUM script before loading app
  initSplunk(envConf.splunk)

  const app = createApp(App) // Pass configuration as props

  // Store updated global config
  setGlobalConfig(envConf)

  app.use(vueLogger)
  const head = createHead()
  app.use(head)

  //Update conf from init
  envConf.logo = config.logo
  envConf.logoInvert = config.logoInvert
  envConf.theme = config.theme

  app.use(router) // Common router

  app.use(i18n)

  const pinia = createPinia()
  app.use(pinia)

  // --- Inject Kameleoon Script ---
  if (envConf.kameleoonId) {
    const kameleoonLoaderScript = document.createElement("script")
    kameleoonLoaderScript.type = "text/javascript"
    kameleoonLoaderScript.textContent = `
    // Duration in milliseconds to wait while the Kameleoon application file is loaded
    var kameleoonLoadingTimeout = 1000;

    window.kameleoonQueue = window.kameleoonQueue || [];
    window.kameleoonStartLoadTime = new Date().getTime();
    if (! document.getElementById("kameleoonLoadingStyleSheet") && ! window.kameleoonDisplayPageTimeOut) {
      var kameleoonS = document.getElementsByTagName("script")[0];
      var kameleoonCc = "* { visibility: hidden !important; background-image: none !important; }";
      var kameleoonStn = document.createElement("style");
      kameleoonStn.type = "text/css";
      kameleoonStn.id = "kameleoonLoadingStyleSheet";
      if (kameleoonStn.styleSheet) {
        kameleoonStn.styleSheet.cssText = kameleoonCc;
      } else {
        kameleoonStn.appendChild(document.createTextNode(kameleoonCc));
      }
      kameleoonS.parentNode.insertBefore(kameleoonStn, kameleoonS);
      window.kameleoonDisplayPage = function(fromEngine) {
        if (!fromEngine) {
          window.kameleoonTimeout = true;
        }
        if (kameleoonStn.parentNode) {
          kameleoonStn.parentNode.removeChild(kameleoonStn);
        }
      };
      window.kameleoonDisplayPageTimeOut = window.setTimeout(window.kameleoonDisplayPage, kameleoonLoadingTimeout);
    }`

    const kameleoonScript = document.createElement("script")
    kameleoonScript.src = `//${envConf.kameleoonId}.kameleoon.eu/kameleoon.js`
    kameleoonScript.setAttribute("fetchpriority", "high")
    kameleoonScript.async = true

    document.head.append(kameleoonLoaderScript, kameleoonScript)
  }

  // --- Inject OneTrust Script ---
  if (envConf.oneTrustId) {
    const oneTrustLoaderScript = document.createElement("script")
    oneTrustLoaderScript.src = "https://cdn.cookielaw.org/scripttemplates/otSDKStub.js"
    oneTrustLoaderScript.setAttribute("data-document-language", "true")
    oneTrustLoaderScript.setAttribute("type", "text/javascript")
    oneTrustLoaderScript.setAttribute("charset", "UTF-8")
    oneTrustLoaderScript.setAttribute("data-domain-script", envConf.oneTrustId)

    const oneTrustScript = document.createElement("script")

    // --- Inject GTM Script if C0004 is enabled ---
    oneTrustScript.innerHTML = `
      function OptanonWrapper() {
      const optanonConsent = document.cookie.match(/OptanonConsent=([^;]+)/)?.[1];
      const decoded = decodeURIComponent(optanonConsent || "");

      const groupString = decoded.match(/groups=([^&]+)/)?.[1];

      if (groupString) {
        const groups = groupString.split(",");

        const hasC0004 = groups.some(group => group === "C0004:1");

        if (hasC0004) {
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://sgtm.fairmont.com/qg2bvifzdqcxwc2.js?aw='+i.replace(/^GTM-/, '')+dl;
            f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${envConf.gtmId}');
        }
      }
    }`

    document.head.append(oneTrustLoaderScript, oneTrustScript)
  }

  defineAdsTheme(envConf.theme)

  return app
}
