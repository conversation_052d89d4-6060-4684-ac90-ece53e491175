import { AvailableLocales, i18n } from "../../i18n"
import { NavigationGuard } from "vue-router"

export const guardLocale: NavigationGuard = (to) => {
  const locale = Array.isArray(to.params.locale) ? to.params.locale[0] : to.params.locale

  if (!i18n.global.availableLocales.includes(locale as AvailableLocales)) {
    window.location.href = "/en/404-page.html"

    return
  }

  i18n.global.locale.value = locale as AvailableLocales
}
