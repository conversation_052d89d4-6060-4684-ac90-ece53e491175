import { AvailableLocales, i18n } from "../../i18n"
import { NavigationGuard } from "vue-router"
import { getGlobalConfig } from "../../global/config"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"

export const guardQueryParametersPos: NavigationGuard = async (to) => {
  const config = getGlobalConfig()

  const { set: setCookie } = useCookies()
  const query = { ...to.query }
  const params = { ...to.params }

  const expires = new Date()
  expires.setTime(expires.getTime() + 60 * 1000 * 60 * 24 * 365) // 1 year

  const cookieOptions = {
    domain: config.appDomain,
    expires,
    path: "/",
    sameSite: "lax" as const,
    secure: true
  }

  if (to.query.currency) {
    setCookie("userCurrency", to.query.currency, cookieOptions)
    delete query.currency
  }

  if (to.query.code_langue) {
    const userLang = to.query.code_langue as AvailableLocales
    const isValidLocale = userLang && i18n.global.availableLocales.includes(userLang)

    if (!isValidLocale) return

    setCookie("userLang", userLang, cookieOptions)
    delete query.code_langue
    params.locale = userLang
  }

  const locale = Array.isArray(to.params.locale) ? to.params.locale[0] : to.params.locale

  if (Object.keys(query).length !== Object.keys(to.query).length || params.locale !== locale) {
    return {
      ...to,
      params,
      query,
      replace: true
    }
  }
}
