import { LocationQuery, LocationQueryValue, NavigationGuard } from "vue-router"
import { fetchHotel } from "../../services/hotels/hotels.fetch"
import { fetchLists } from "../../services/referentials/referentials.fetch"

const removeRoomFromQP = (query: LocationQuery, index: number) => {
  let replace = false

  const newQP = Object.keys(query).reduce(
    (acc, key) => {
      if (!key.startsWith(`product[${index}]`)) return acc

      delete acc[key]
      replace = true

      return acc
    },
    { ...query }
  )

  return replace ? newQP : undefined
}

const fetchConstraints = async (hotelId?: LocationQueryValue) => {
  const sessionStorageKey = hotelId ? `guard-constraints-${hotelId}` : `guard-constraints`

  if (window.sessionStorage.getItem(sessionStorageKey)) {
    return JSON.parse(window.sessionStorage.getItem(sessionStorageKey)!)
  }

  const constraints = {
    maxAdult: undefined,
    maxChild: undefined,
    maxChildAge: undefined,
    maxPax: undefined,
    maxRoom: undefined
  }

  try {
    const globalConstraints = await fetchLists(["room-occupancy"])
    Object.assign(constraints, globalConstraints["room-occupancy"])

    const hotel = hotelId ? await fetchHotel(hotelId) : undefined
    if (hotel) {
      Object.assign(constraints, hotel.roomOccupancy)
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    /* empty */
  }

  window.sessionStorage.setItem(sessionStorageKey, JSON.stringify(constraints))

  return constraints
}

export const guardQueryParametersRooms: NavigationGuard = async (to) => {
  const hotelId = Array.isArray(to.query.hotelCodes) ? to.query.hotelCodes[0] : to.query.hotelCodes
  const occupancyConstraints = await fetchConstraints(hotelId)

  const hasRooms = Object.keys(to.query).some((key) => key.startsWith("product["))

  const { maxPax, maxAdult, maxChild, maxChildAge, maxRoom } = occupancyConstraints

  if (!hasRooms) return

  const roomIndices = Object.keys(to.query).reduce((set, key) => {
    const match = /^product\[(\d+)\]/.exec(key)

    if (match?.[1]) set.add(Number(match[1]))

    return set
  }, new Set<number>())

  const rooms = []
  for (const roomIndex of roomIndices.values()) {
    const adults = Number(to.query[`product[${roomIndex}][adultNumber]`])
    const childrenAge = Object.keys(to.query)
      .filter((key) => key.startsWith(`product[${roomIndex}][childrenAge]`))
      .map((key) => Number(to.query[key]))
      .filter((age) => Number.isSafeInteger(age))

    rooms.push({
      adults,
      children: childrenAge.length,
      childrenAge,
      index: roomIndex
    })
  }

  if (Number.isSafeInteger(maxRoom) && rooms.length > maxRoom!) {
    let query = { ...to.query }

    for (const { index } of rooms) {
      const tmpQuery = removeRoomFromQP(query, index)

      if (tmpQuery) {
        query = tmpQuery
      }
    }

    return {
      ...to,
      name: "search",
      query,
      replace: true
    }
  }

  for (const room of rooms) {
    const maxPaxError = (Number.isSafeInteger(maxPax) && room.adults > maxPax!) || room.adults <= 0
    const maxAdultsError = (Number.isSafeInteger(maxAdult) && room.adults > maxAdult!) || room.adults <= 0
    const maxChildrenError = Number.isSafeInteger(maxChild) && room.children > maxChild!
    const maxChildAgeError =
      (Number.isSafeInteger(maxChildAge) && Math.max(...room.childrenAge) > maxChildAge!) ||
      (room.childrenAge.length > 0 && Math.max(...room.childrenAge) < 0)

    if (maxPaxError || maxAdultsError || maxChildrenError || maxChildAgeError) {
      const query = removeRoomFromQP(to.query, room.index)

      if (query) {
        return {
          ...to,
          name: "search",
          query,
          replace: true
        }
      }
    }
  }
}
