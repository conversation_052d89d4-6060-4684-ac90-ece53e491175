import { NavigationGuard } from "vue-router"
import { differenceInCalendarDays } from "date-fns"
import { useSearchConstraints } from "../../composables"

export const guardQueryParametersDates: NavigationGuard = async (to) => {
  const { calendarConstraints } = useSearchConstraints()

  const dateIn = Array.isArray(to.query.dateIn) ? to.query.dateIn[0] : to.query.dateIn
  const lengthOfStayValue = Array.isArray(to.query.lengthOfStayValue)
    ? to.query.lengthOfStayValue[0]
    : to.query.lengthOfStayValue

  if (dateIn) {
    const today = new Date().toISOString().slice(0, 10)

    if (differenceInCalendarDays(dateIn, today) < 0 || !Number.isSafeInteger(new Date(dateIn).getTime())) {
      const query = { ...to.query }
      delete query.dateIn

      return {
        ...to,
        name: "search",
        query,
        replace: true
      }
    }
  }

  if (lengthOfStayValue) {
    const nbNights = Number(lengthOfStayValue)

    if (
      !Number.isSafeInteger(nbNights) ||
      nbNights <= 0 ||
      (calendarConstraints.value?.maxLengthOfStay && nbNights > calendarConstraints.value?.maxLengthOfStay)
    ) {
      const query = { ...to.query }
      delete query.lengthOfStayValue

      return {
        ...to,
        name: "search",
        query,
        replace: true
      }
    }
  }
}
