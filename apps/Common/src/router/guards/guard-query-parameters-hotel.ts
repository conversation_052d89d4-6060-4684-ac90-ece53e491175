import { NavigationGuard } from "vue-router"
import { fetchHotelsByIds } from "../../services/hotels/hotels.fetch"
import { hotelsIdByRegion } from "../../services/hotels/hotels.consts"
import { useHotelsStore } from "../../stores/hotels/hotels"

type HotelsList = ReturnType<typeof useHotelsStore>["hotelsList"]
type UpdateHotelsList = ReturnType<typeof useHotelsStore>["updateHotelsList"]

const hydrateHotels = async (hotelsList: HotelsList, updateHotelsList: UpdateHotelsList) => {
  const alreadyFetched = Object.values(hotelsList).some((region) => region.length > 0)

  if (alreadyFetched) return

  const hotelIds = [
    ...hotelsIdByRegion.africa,
    ...hotelsIdByRegion.asia,
    ...hotelsIdByRegion.europe,
    ...hotelsIdByRegion.northAmerica,
    ...hotelsIdByRegion.southAmerica
  ]

  const hotelsResult = await fetchHotelsByIds(hotelIds)

  updateHotelsList({
    africa: hotelsResult.results
      .filter((result) => hotelsIdByRegion.africa.includes(result.hotel.id))
      .map((result) => result.hotel),
    asia: hotelsResult.results
      .filter((result) => hotelsIdByRegion.asia.includes(result.hotel.id))
      .map((result) => result.hotel),
    europe: hotelsResult.results
      .filter((result) => hotelsIdByRegion.europe.includes(result.hotel.id))
      .map((result) => result.hotel),
    northAmerica: hotelsResult.results
      .filter((result) => hotelsIdByRegion.northAmerica.includes(result.hotel.id))
      .map((result) => result.hotel),
    southAmerica: hotelsResult.results
      .filter((result) => hotelsIdByRegion.southAmerica.includes(result.hotel.id))
      .map((result) => result.hotel)
  })
}

export const guardQueryParametersHotel: NavigationGuard = async (to) => {
  const { updateHotelsList, hotelsList } = useHotelsStore()

  if (!to.query.hotelCodes) return

  const hotelCode = Array.isArray(to.query.hotelCodes) ? to.query.hotelCodes[0] : to.query.hotelCodes

  if (!hotelCode) return

  await hydrateHotels(hotelsList, updateHotelsList)

  const allHotelIds = Object.values(hotelsList).flatMap((hotelsInRegion) => hotelsInRegion.map((hotel) => hotel.id))
  const hotelCodeExists = allHotelIds.includes(hotelCode)

  if (!hotelCodeExists) {
    const query = { ...to.query }
    delete query.hotelCodes

    return { ...to, name: "search", query, replace: true }
  }
}
