import { NavigationGuard, NavigationHookAfter } from "vue-router"
import { guardLocale } from "./guard-locale"
import { guardQueryParametersDates } from "./guard-query-parameters-dates"
import { guardQueryParametersHotel } from "./guard-query-parameters-hotel"
import { guardQueryParametersPos } from "./guard-query-parameters-pos"
import { guardQueryParametersRooms } from "./guard-query-parameters-rooms"
import { guardScrollToTop } from "./guard-scroll-to-top"
import { guardTrailingSlash } from "./guard-trailing-slash"

type GlobalNavigationGuards = {
  afterEach: NavigationHookAfter[]
  beforeEach: NavigationGuard[]
}

export const navigationGuards: GlobalNavigationGuards = {
  afterEach: [guardScrollToTop],
  beforeEach: [
    guardTrailingSlash,
    guardQueryParametersPos,
    guardLocale,
    guardQueryParametersHotel,
    guardQueryParametersDates,
    guardQueryParametersRooms
  ]
}
