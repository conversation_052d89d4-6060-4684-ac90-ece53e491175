import { type AvailableLocales, i18n } from "../i18n"
import { createRouter, createWebHistory } from "vue-router"
import MainLayout from "../layouts/MainLayout/MainLayout.vue"
import { navigationGuards } from "./guards"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"

const base_url = import.meta.env.VITE_BASE_URL || import.meta.env.BASE_URL

const router = createRouter({
  history: createWebHistory(base_url),
  routes: [
    // Redirect root to locale-based search
    {
      path: "/",
      redirect: () => {
        const { get: getCookies } = useCookies()
        const userLang = getCookies("userLang")

        const isValidLocale = userLang && i18n.global.availableLocales.includes(userLang as AvailableLocales)
        const defaultLocale = isValidLocale ? userLang : i18n.global.locale.value || "en"

        return {
          name: "search",
          params: {
            locale: defaultLocale
          }
        }
      }
    },
    {
      children: [
        {
          // Dynamic meta data, defined in SearchView.vue
          component: () => import("../views/SearchView.vue"),
          name: "search",
          path: "search"
        },
        {
          // Dynamic meta data, defined in StayView.vue
          component: () => import("../views/StayView.vue"),
          name: "stay",
          path: "stay"
        },
        {
          // Dynamic meta data, defined in EnhanceView.vue
          component: () => import("../views/EnhanceView.vue"),
          name: "enhance",
          path: "enhance"
        },
        {
          // Dynamic meta data, defined in CompleteView.vue
          component: () => import("../views/CompleteView.vue"),
          name: "complete",
          path: "complete"
        },
        {
          // Dynamic meta data, defined in SummaryView.vue
          component: () => import("../views/SummaryView.vue"),
          name: "summary",
          path: "summary"
        },
        {
          component: () => import("../views/AboutView.vue"),
          meta: {
            title: `About ${import.meta.env.VITE_APP_BRAND_NAME}`
          },
          name: "about",
          path: "about"
        }
      ],
      meta: {
        layout: MainLayout,
        title: `Home - ${import.meta.env.VITE_APP_BRAND_NAME}`
      },
      name: "home",
      path: "/:locale/",
      redirect: () => ({
        name: "search"
      })
    },
    {
      path: "/:pathMatch(.*)*",
      redirect: () => {
        window.location.href = "/en/404-page.html"

        return ""
      }
    }
  ]
})

/**
 * Apply navigation guards
 */
for (const beforeEachGuard of navigationGuards.beforeEach) {
  router.beforeEach(beforeEachGuard)
}

for (const afterEachGuard of navigationGuards.afterEach) {
  router.afterEach(afterEachGuard)
}

export default router
