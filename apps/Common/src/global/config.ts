import { BrandConfiguration } from "@shared/types"
import defaultConfig from "./consts"

let globalConfig: BrandConfiguration | null = null

export function setGlobalConfig(config: BrandConfiguration) {
  globalConfig = config
}

async function fetchJson(url: string): Promise<BrandConfiguration | null> {
  try {
    const res = await fetch(url, { cache: "no-store" })
    if (!res.ok) throw new Error(`HTTP ${res.status}`)
    return await res.json()
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn(`[Config] Could not load ${url}`, e)
    return null
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function deepMerge(target: Record<string, any>, source: Record<string, any>): BrandConfiguration {
  const result = { ...target }
  for (const key of Object.keys(source)) {
    if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
      result[key] = deepMerge(target[key], source[key])
    } else {
      result[key] = source[key]
    }
  }
  return result as BrandConfiguration
}

export async function loadGlobalConfig(): Promise<BrandConfiguration> {
  const baseUrl = import.meta.env.BASE_URL
  const envUrl = `${baseUrl}/config.json` //config.json

  //global is default config
  globalConfig = defaultConfig

  //Overriding global conf by json conf
  const envConfig = await fetchJson(envUrl)
  if (envConfig) {
    globalConfig = deepMerge(defaultConfig, envConfig)
  }

  return globalConfig!
}

export function getGlobalConfig(): BrandConfiguration {
  if (!globalConfig) {
    throw new Error("[Config] brandConfig is not available. Did you forget to provide it?")
  }

  return globalConfig
}
