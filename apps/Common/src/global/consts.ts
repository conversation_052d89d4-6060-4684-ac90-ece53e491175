import { BrandConfiguration } from "@shared/types"
import brandFormConfig from "../brandForm.config"

const defaultConfig: BrandConfiguration = {
  all: {
    account: import.meta.env.VITE_LOYALTY_ACCOUNT_REDIRECTION_URI,
    bestPriceConditions: import.meta.env.VITE_APP_ALL_BEST_PRICE_GUARANTEE_CONDITIONS,
    help: import.meta.env.VITE_APP_ALL_HELP,
    helpAndContact: import.meta.env.VITE_APP_ALL_HELP_CONTACT,
    hostname: import.meta.env.VITE_APP_ALL_HOSTNAME,
    privacyPolicy: import.meta.env.VITE_APP_ALL_PRIVACY_POLICY,
    redirection: import.meta.env.VITE_APP_ALL_HOTEL_REDIRECTION_URL,
    travelProUrl: import.meta.env.VITE_APP_TRAVEL_PRO_REDIRECTION_URL
  },
  apiHeaders: {
    clientId: import.meta.env.VITE_APP_CLIENT_ID
  },
  apiUrl: import.meta.env.VITE_APP_API_BASE_URL,
  app: {
    //basic data for the app
    baseUrl: import.meta.env.VITE_BASE_URL,
    legalEmail: import.meta.env.VITE_APP_LEGAL_MAIL,
    payApiKey: import.meta.env.VITE_APP_PAYMENT_API_KEY,
    payModule: import.meta.env.VITE_APP_PAYMENT_IFRAME_MODULE
  },
  appDomain: window.location.hostname,
  brandCode: import.meta.env.VITE_APP_BRAND_CODE,
  formFieldStructure: brandFormConfig,
  gtmId: import.meta.env.VITE_APP_GTM_ID,
  kameleoonId: import.meta.env.VITE_APP_KAMELEOON_ID,
  legacy: {
    endpoints: {
      moreNumbers: import.meta.env.VITE_APP_LEGACY_MORE_NUMBERS,
      sitemap: import.meta.env.VITE_APP_LEGACY_SITEMAP,
      termsAndConditions: import.meta.env.VITE_APP_LEGACY_TERMS_AND_CONDITIONS,
      webAccessibility: import.meta.env.VITE_APP_LEGACY_WEB_ACCESSIBILITY
    },
    homePage: import.meta.env.VITE_APP_LEGACY_HOMEPAGE
  },
  logo: "",
  logoInvert: "",
  myBookingsUrl: import.meta.env.VITE_APP_MY_BOOKINGS_URL,
  name: import.meta.env.VITE_APP_BRAND_NAME,
  oidc: {
    clientId: import.meta.env.VITE_APP_LOGIN_CLIENT_ID,
    logo: import.meta.env.VITE_APP_LOGO_PATH,
    params: import.meta.env.VITE_APP_OIDC_PARAMS,
    ui: import.meta.env.VITE_APP_OIDC_BRAND_UI,
    url: import.meta.env.VITE_APP_OIDC_PING_URI
  },
  oneTrustId: import.meta.env.VITE_APP_ONE_TRUST_ID,
  paymentModuleApiKey: import.meta.env.VITE_APP_PAYMENT_API_KEY,
  paymentModuleUrl: import.meta.env.VITE_APP_PAYMENT_IFRAME_MODULE,
  siteCode: import.meta.env.VITE_APP_SITE_CODE,
  splunk: {
    appName: import.meta.env.VITE_APP_SPLUNK_APPNAME,
    critical: import.meta.env.VITE_APP_SPLUNK_CRITICAL,
    domain: import.meta.env.VITE_APP_SPLUNK_DOMAIN,
    env: import.meta.env.VITE_APP_SPLUNK_ENV,
    itDepartment: import.meta.env.VITE_APP_SPLUNK_ITDEPARTEMENT,
    itDomain: import.meta.env.VITE_APP_SPLUNK_ITDOMAIN,
    leanixId: import.meta.env.VITE_APP_SPLUNK_LEANIXID,
    pci: import.meta.env.VITE_APP_SPLUNK_PCI,
    realm: import.meta.env.VITE_APP_SPLUNK_REALM,
    snowId: import.meta.env.VITE_APP_SPLUNK_SNOWID,
    token: import.meta.env.VITE_APP_SPLUNK_ACCESSTOKEN
  },
  step3Permalink: {
    client: import.meta.env.VITE_APP_STEP_3_CLIENT,
    host: import.meta.env.VITE_APP_STEP_3_PERMALINK_HOST,
    path: import.meta.env.VITE_APP_STEP_3_PERMALINK_PATH
  },
  theme: {}
}

export const AccorApiEndpoints = {
  accommodations: "/catalog/v1/hotels/{hotelId}/products/accommodations",
  commerceTracking: "/referential/v1/best-visit/cookie-attribution",
  contactMe: "/customer/v1/contacts/me",
  countries: "/referentials/v1/countries",
  createBasket: "/basket/webdirectchannels/v1/baskets",
  eligibility: "/contacts/v1.0/account/eligibility",
  facilities: (hotelId: string) => `/catalog/v1/hotels/${hotelId}/facilities/global`,
  getBasket: (basketId: string) => `/basket/webdirectchannels/v1/baskets/${basketId}`,
  hotel: (hotelId: string) => `/catalog/v1/hotels/${hotelId}`,
  hotels: "/catalog/v1/hotels",
  identification: "/availability/v3/identification",
  loyaltyEnroll: "/auth/v1/loyalty/enroll",
  medias: (hotelId: string) => `/catalog/v1/hotels/${hotelId}/medias`,
  offers: (hotelId: string) => `/businessoffers/v1/hotels/${hotelId}/accommodations/best-offers`,
  posCountriesGroupedByContinent: (siteCode: string) => `/referential/v1/pos/countries?siteCode=${siteCode}`,
  posCountryLanguageAuto: (siteCode: string, isoCountryCode: string, languageCode: string, currencyCode?: string) =>
    `/referential/v1/pos/countryLanguageAuto?usecase=externalLink&siteCode=${siteCode}&isoCountryCode=${isoCountryCode}&languageCode=${languageCode}${currencyCode ? `&currencyCode=${currencyCode}` : ""}`,
  posCountryLanguageManual: (siteCode: string, countryCode: string, languageCode: string) =>
    `/referential/v1/pos/countryLanguageManual?siteCode=${siteCode}&accorCountryCode=${countryCode}&languageCode=${languageCode}`,
  posCurrenciesGroupedByContinent: "/referential/v1/pos/currencies?groupByContinent=true",
  posCurrencyManual: (currencyCode: string) => `/referential/v1/pos/currencyManual?currencyCode=${currencyCode}`,
  pricing: "/businessoffers/v1/offers/pricing-conditions",
  productOffer: (hotelId: string) => `/businessoffers/v1/hotels/${hotelId}/products/offers`,
  referentialList: "/referentials/v1/list",
  refreshToken: import.meta.env.VITE_OIDC_REFRESH_TOKEN_URI,
  updateBasket: (basketId: string) => `/basket/webdirectchannels/v1/baskets/${basketId}/items`,
  updateBasketSummary: (basketId: string) => `/basket/webdirectchannels/v1/baskets/${basketId}/summary`
}

export const AllHotels: string[] = ["1988", "A5A5", "B773"]

export default defaultConfig
