{"global": {"account": "Account", "apply": "Apply changes", "become_member": "Become a member", "brand_logo": "Logo {brand}", "continue": "Continue", "go_back": "Go back", "go_to_homepage": "Go to homepage", "log_out": "Log out", "my_bookings": "My Bookings", "sign_in": "Sign in", "today": "Today", "tomorrow": "Tomorrow", "loading": "Loading"}, "seo": {"step_1_title": "Search for Your Next Stay {'|'} Fairmont Hotels & Resorts", "step_1_description": "Welcome to Fairmont Hotels & Resorts. Search for your next stay and experience exceptional comfort with spa and wellness facilities, family stays and more.", "step_2_title": "Choose Your Room {'|'} {hotel} {'|'} Fairmont Hotels & Resorts", "step_2_title_no_hotel": "Choose Your Room {'|'} Fairmont Hotels & Resorts", "step_2_description": "Choose your accommodation at {hotel}. For business or leisure, our rooms offer modern comfort and access to premium facilities.", "step_2_description_no_hotel": "Choose your accommodation. For business or leisure, our rooms offer modern comfort and access to premium facilities.", "step_3_title": "Enhance Your Stay {'|'} {hotel} {'|'} Fairmont Hotels & Resorts", "step_3_description": "Enhance your stay at {hotel} with Fairmont's world-class luxury. <PERSON><PERSON> added comforts including valet parking, airport transfers, exceptional breakfasts, and more.", "step_4_title": "Complete Your Reservation | {hotel} {'|'} Fairmont Hotels & Resorts", "step_4_description": "Complete your reservation and prepare for an unforgettable stay at {hotel}. Indulge in luxury, relaxation, and convenient meeting spaces.", "step_5_title": "Reservation Confirmed {'|'} {hotel} {'|'} Fairmont Hotels & Resorts ", "step_5_description": "Your reservation is confirmed! Enjoy exceptional comfort, spa experiences, and more. We look forward to welcoming you at {hotel}."}, "components": {"all_hotel_redirect_modal": {"all_logo": "All logo", "brand_logo": "{brand} logo", "close": "Close", "infos": "Fairmont is part of Accor. We are taking you to our platform to book your stay.", "redirect": "Continue booking"}, "language_modal": {"confirm": "Confirm", "geographical_area": "Geographical Area", "language": "Country/Region - Language", "select_language": "Select your location and language below"}, "currency_modal": {"confirm": "Confirm my currency", "currency": "<PERSON><PERSON><PERSON><PERSON>", "geographical_area": "Geographical Area", "select_currency": "Select your currency below"}, "footer": {"copyright": "Fairmont is part of Accor ©", "help_section": {"title": "Need help?", "main_content": "Toll Free room reservations only", "secondary_content": "+************** (Canada & U.S.)", "link": "More numbers"}, "links": {"terms_conditions": "Terms & Conditions", "privacy_policy": "Privacy Policy", "do_not_sell": "Do Not Sell My Personal Information", "cookies_preferences": "Cookies Preferences", "sitemap": "Sitemap", "accessibility": "Web accessibility"}}, "header": {"brand_logo": "Fairmont logo"}, "input_password": {"no_lowercase": "Lowercase letters (a-z)", "no_number_or_symbol": "Numbers (0-9) or special characters ({'#'}, {'@'}, {'&'} ...)", "no_uppercase": "Uppercase letters (A-Z)", "password_criterias": "Your password must contain:", "too_short_or_long": "Between 8 and 20 characters"}, "legal_text": {"legal": {"first_paragraph": "The collected data is processed by Accor S.A. for the purposes of managing your bookings and stays, getting to know you better, improving the quality of service and customer experience and sending you information on Accor products and services. The data is intended for Accor S.A., Accor legal entities, hotels and Accor S.A. service providers. In particular, the data related to your stays, preferences, satisfaction and, if the case may be, your loyalty program are shared between the Accor hotels in order to improve the quality of service and your experience in each of these hotels. You may at any time object to the sharing of this data between the hotels of the Group by writing to {link}. The data may be transferred outside the European Union where this is necessary for implementing your booking or managing pre-contractual measures intended to fulfil the booking or where appropriate and suitable safeguards have been provided. You have the right to request access to and rectification or erasure of your data, to object to processing as well as the right to issue instructions on how this data is to be treated after your death (hopefully as late as possible!) by writing to {link}.", "second_paragraph": "Every hotel, as data controller, processes your personal data for the purposes of booking and guest management, billing and payment, canvassing, special offers and sales events, commercial surveys and satisfaction enquiries. The data is intended for use by the hotel as well as its service providers and, in the case of hotels operating under one of the Accor brands, for the various Accor S.A. departments, as far as each is concerned. You have the right to request access and rectification or erasure of your data and to object to processing. You can exercise such right by writing to the hotel management at the above-mentioned address.", "third_paragraph": "For more information about the protection of your personal data, please see our Personal Data Protection Charter."}}, "logout_modal": {"are_you_sure": "Are you sure you want to log out ? Logging out will result in losing your member discount and potential point earnings."}, "travel_pro_redirect_modal": {"all_logo": "All logo", "brand_logo": "{brand} logo", "infos": "We are taking you to Accor TravelPros, the dedicated website for Travel Advisors to book their clients’ stays.", "redirect": "Continue booking", "travel_pro_logo": "Travel Pro logo"}, "search_criteria": {"date_picker": {"title": "Dates", "choose_your_dates": "Choose your dates", "edit_end_date": "Edit end date of stay", "edit_start_date": "Edit start date of stay"}, "destination": {"destination": "Destination"}, "special_rates": {"dropdown": {"name": "Special rates"}, "section_title": "Your Special Rates"}, "rooms_and_guests": {"button_value_guests": "1 Guest | {count} Guests", "button_value_rooms": "1 Room | {count} Rooms", "dropdown": {"name": "Rooms & Guests"}, "section_title": "Your Rooms & Guests"}, "your_trip": "Your Trip"}, "search_engine": {"modify_your_search": "Modify your search", "update_search": "Update search", "check_rates": "Check rates"}, "sidebar": {"accessible_rooms_title": "Accessible rooms", "accessible_rooms_text": "This hotel has accessible rooms and assistive devices", "accessible_rooms_button_text": "Learn more about accessibility", "best_price_title": "Best price guarantee", "best_price_text": "Found a lower rate?\nWe’ll match it and give you 10% off. ", "best_price_top_link_text": "See conditions", "media_alt": "{label} room image", "need_help_title": "Need help ?", "need_help_text_1": "A representative for room reservations is here to help", "need_help_text_2": "+ ************** (Canada & US)", "need_help_top_link_text": "More numbers", "need_help_bottom_link_text": "FAQ’s"}, "stay_view": {"no_results": "We apologize, there is no availability for your search. Please try adjusting your dates or exploring other destinations for your stay.", "room_card": {"gallery": "Room gallery", "key_features": {"accessibility": "Accessible room:", "bed": "Type of bed:", "occupant": "Number of occupants:", "size": "Room size:", "view": "View type:"}, "media": {"no_image": "{label} image not available", "alt": "{label} room image"}, "price_section": {"per_stay": "for your stay", "per_night": "average per night"}, "rate_title": "Choose your rate"}, "room_details_modal": {"aditional_payment_label": "Additional charge", "key_features": {"accessibility": "Accessibilitty", "bathroom_facilities": "Bathroom", "bed": "Bed type", "max_pax": "Maximum capacity", "surface_area": "Size", "views": "View"}}, "rate_card": {"amenities": {"accessibility_icon_label": {"guarantee": "Guarantee Policy:", "cancellation": "Cancellation Policy:", "meal_plan": "Meal Plan:", "loyalty_discount_description": "Loyalty Discount Description"}, "member_rate_deduction_compliance": "Join for free or sign in to save up to {amount} on this booking.", "member_rate_deduction_unlogged": "Sign in or join to book the member rate", "member_rate_deduction_logged": "You benefit from your member discount on this rate", "sign_in_link": "sign in"}, "price_section": {"total_for_your_stay": "total for your stay", "average_per_night": "average per night"}}, "room_section": {"other_room_label": "See {count} other room | See {count} other rooms", "reduce_label": "Reduce", "rooms_label": {"apt": "Apartments-Residences", "apv": "Apartments-Residences View", "bea": "Villa", "bel": "Unique", "clu": "Executive", "con": "Unique", "dhv": "Deluxe Harbour", "dlf": "Deluxe", "dll": "Deluxe", "dln": "Deluxe", "dls": "Deluxe View", "dlt": "Deluxe", "dlv": "Deluxe View", "dlx": "Deluxe", "dlxb": "Deluxe View", "dmv": "Deluxe", "dpv": "Deluxe View", "dpx": "Deluxe", "dsv": "Deluxe Seaview", "dun": "Unique", "dvr": "Unique", "dvs": "Deluxe View", "dxx": "Deluxe Newly Renovated", "est": "Estate", "exc": "Executive", "exe": "Executive", "exs": "Executive", "fam": "Family", "far": "Family", "fas": "Family", "fco": "Fairmont", "fg": "Fairmont Gold", "fgs": "Fairmont Gold Suite", "fhf": "Fairmont", "fll": "Fairmont View", "flx": "Fairmont Luxury", "fme": "Fairmont", "fmf": "Fairmont View", "fms": "FMS", "fmt": "Fairmont", "fmv": "Fairmont View", "fpr": "Premier", "fsu": "Fairmont Sea View Room", "fvs": "Fairmont View", "gbs": "Fairmont Gold Signature", "gdn": "Fairmont View", "ggs": "Suite", "gld": "Fairmont Gold", "glj": "Fairmont Gold Jr. Suite", "gll": "Fairmont Gold Suite", "gln": "Fairmont Gold", "gls": "Fairmont Gold", "glv": "Fairmont Gold View", "grn": "Suite", "gsg": "Fairmont Gold Signature", "gso": "GSO", "gss": "Fairmont Gold Signature", "gst": "Fairmont Gold Suite", "hig": "Heritage Suite", "hnv": "Unique", "hpr": "Apartment-Residences", "htl": "Hotel Rooms", "jdx": "Junior Suite", "jnc": "Junior Suite", "jnl": "Junior Suite View", "jnr": "Junior Suite", "jnrr": "Junior Suite", "jns": "Junior Suite", "jsg": "Fairmont Gold Suite", "jsu": "Junior Suite", "kil": "Kilohana Corner Suites", "ldx": "Signature", "lgs": "Luxury Suite", "lux": "Luxury", "luxr": "Luxury", "man": "Unique", "mds": "Luxury Suites", "mlb": "Fairmont", "mod": "Moderate", "mtn": "Fairmont View", "obs": "Suite", "ocv": "Oceanview", "ost": "1 Bedroom Suite", "other": "Other Rooms", "pen": "Penthouse", "plv": "Poolside", "pnt": "Suite", "pov": "Villa View", "pre": "Premier", "prm": "Premier", "prs": "Suite", "prsr": "Suite", "prv": "Premier View", "ps": "Suite", "pst": "Suite", "rdv": "Apartments-Residences", "red": "Studio", "res": "Suite", "roy": "Suite", "rte": "Suite", "sal": "Salon", "sas": "Suite", "sdl": "Deluxe Two Bedroom Suite", "sgo": "Signature", "sgs": "Suite", "si2": "Suite", "sig": "Signature", "sof": "Suite", "sps": "Suite", "spv": "Suite", "ssd": "Fairmont Gold Suite", "sse": "Suite", "sst": "Suite", "sta": "Standard", "stb": "Suite", "stc": "Suite", "ste": "Suite", "ste1": "Suite", "ste1g": "Fairmont Gold Suite", "ste2": "Suite", "ste2g": "Fairmont Gold Suite", "ster": "Suite", "stf": "Deluxe", "stg": "Suite", "sth": "Suite", "sti": "Suite", "stj": "Suite", "stk": "Suite", "stl": "Suite", "stm": "Suite", "sts": "STS", "stv": "Classic View", "sui": "Suite", "sup": "State Room", "sut": "Suite", "v2b": "Overwater Residence with Pool area - Sunrise Side", "v3b": "Overwater Residence with Pool area - Sunset Side", "vil": "Villa", "wha": "Disabled Fairmont", "xvs": "Deluxe View"}, "subtitle": "{count} Type of room |{count} Types of rooms"}}}, "form": {"custom_message": "Your message will be sent to the hotel when your booking is complete. We cannot guarantee that your requests will be met.", "email_helper": "This is where we will send the confirmation email.", "estimated_time_of_arrival": "Estimated time of arrival", "estimated_time_of_arrival_helper": "Please let us know what time you expect to arrive at the hotel.", "optional": "(Optional)", "get_acquainted": "Let's get acquainted", "get_acquainted_submit": "Confirm", "guest_informations": "Guest Informations", "guest_informations_subtitle": "All fields are mandatory, except those marked as optional", "how_to_contact_you": "How to contact you ?", "nationality_helper": "To help the hotelier to welcome you", "phone": "Phone", "phone_helper": "We'll only call you if there are questions about your booking.", "purpose_of_stay": "What is the reason for your stay?", "purpose_of_stay_helper": "Are there any other additional requests that you would like to add to your profile so that you have an enjoyable stay each time you stay at Fairmont Hotels & Resorts?", "guarantee_your_reservation": "Guarantee your reservation", "russian_consent": "I authorize Accor to transfer my data to a foreign territory and to third parties (hotels).*", "special_request": "Do you have any requests or special ?", "special_request_helper": "Your message will be sent to the hotel when your booking is complete. We cannot guarantee that your requests will be met.", "special_request_subtitle": "Are there any other additional requests that you would like to add to your profile so that you have an enjoyable stay each time you stay at Fairmont Hotels & Resorts?", "stay_preference": "Stay preference", "submit": "Submit", "where_do_you_live": "Where do you live"}, "errors": {"stay": {"no_accessibility_room": "We were unable to find rooms that matched the Accessibility preference you requested. Please try adjusting your dates or exploring other destinations for your stay.", "no_special_offers": "We apologize, the selected special rate is not available. Explore available rates below, or try adjusting your search."}, "child_age": "Please select the child age", "date_limit": "Please enter a date between today and 405 days from today", "end_date_format": "Please enter a valid check-out date (E.g: <PERSON>, Jan 01).", "invalid_iata_format": "Invalid IATA Code format (e.g: 123XXXXX)", "invalid_promo_code": "Your Promo Code is not valid, please check and re-enter your details", "max_nights": "It is not possible to book online more than 30 nights.", "no_property": "Sorry, there is no property in this area yet. Please select your choice from the list.", "select_property": "Please select a property.", "start_date_format": "Please enter a valid check-in date (E.g: <PERSON>, Jan 01).", "best_offers": {"max_adults_exceeded": {"title": "Room capacity exceeded", "description": "Rooms cannot accommodate more than {maxAdult} adults"}, "max_child_exceeded": {"title": "Maximum number of children exceeded", "description": "Rooms cannot accommodate more than {maxChild} children."}, "max_pax_exceeded": {"title": "Maximum number of guests exceeded", "description": "Rooms cannot accommodate more than {maxPax} persons."}, "max_room_exceeded": {"title": "Maximum number of rooms exceeded", "description": "Sorry, you cannot book more than {maxRoom} rooms at once."}, "input_problem": {"title": "Input problem", "description": "There seems to be an issue with your information. Please check or try a new search.", "cta": "Back to homepage"}, "selected_offer_unavailable": {"title": "The room/rate is no longer available", "description": "Please search again or select another offer."}}, "generic": {"title": "Sorry, a technical error has occured", "description": "Could you please try again or come back later?", "cta": "Back to homepage"}, "invalid_hotel_id": {"title": "Sorry, the selected hotel could not be found", "description": "Please check your hotel selection or start a new search."}, "ident_pmid_not_equal_to_bearer_pmid": {"title": "You have been disconnected", "description": "Please log in again to continue your search", "cta": "Log me in"}}, "page": {"complete_view": {"title": {"left": "complete ", "middle": "your ", "right": "booking"}}, "enhance_view": {"title": {"left": "enhance ", "middle": "your ", "right": "stay"}}, "stay_view": {"title": {"down_multiroom": "Room {room} of {total}", "left": "choose ", "middle": "your ", "right": "room", "right_multiroom": "room :"}}, "search_view": {"title": {"left": "search ", "middle": "your ", "right": "hotel"}}}, "regions": {"africa": "Middle East and Africa", "asia": "Asia Pacific", "europe": "Europe", "northAmerica": "North America", "southAmerica": "South America"}, "routes": {"search": "search", "stay": "stay", "enhance": "enhance", "complete": "complete"}}