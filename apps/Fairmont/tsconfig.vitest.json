{"extends": "./tsconfig.app.json", "include": ["src/**/__tests__/*", "env.d.ts"], "exclude": [], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.vitest.tsbuildinfo", "lib": [], "types": ["node", "jsdom"], "baseUrl": "./", "paths": {"@/*": ["src/*"], "@stores/*": ["../Common/src/stores/*"], "@sb/*": ["../../storybook/src/*"], "@sb-assets/*": ["../../storybook/src/assets/*"], "@sb-base/*": ["../../storybook/src/styles/base/*"], "@sb-components/*": ["../../storybook/src/components/*"], "@sb-composables/*": ["../../storybook/src/composables/*"], "@sb-config/*": ["../../storybook/src/styles/config/*"], "@sb-fonts/*": ["../../storybook/public/fonts/*"], "@sb-i18n/*": ["../../storybook/src/i18n/*"], "@sb-helpers/*": ["../../storybook/src/helpers/*"], "@sb-styles/*": ["../../storybook/src/styles/*"], "@sb-utilities/*": ["../../storybook/src/styles/utilities/*"]}}}