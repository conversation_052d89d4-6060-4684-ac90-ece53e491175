import { defineConfig, mergeConfig } from "vite"
import commonConfig from "../Common/vite.config"
import fs from "fs"
import path from "path"
import { viteStaticCopy } from "vite-plugin-static-copy"

const env = process.env.NODE_ENV == "development" ? "dev" : null
const configFileName = `config-${env}.json`
const configSource = path.resolve(__dirname, "config", configFileName)

const plugins = []

if (env === "dev" && fs.existsSync(configSource)) {
  //copy config only for dev env, other envs are managed by CD
  plugins.push(
    viteStaticCopy({
      targets: [
        {
          // Copy to dist/{base}/config.json
          dest: "", // put directly at BASE_URL/config.json
          rename: "config.json",
          src: configSource
        }
      ]
    })
  )
}

// https://vite.dev/config/
const config = defineConfig({
  plugins: plugins,
  server: {
    allowedHosts: ["local.booking.fairmont.com", "booking.fairmont.local"] //Please keep old domain for oidc compatibility
  }
})

export default defineConfig((configEnv) => mergeConfig(commonConfig(configEnv), config))
