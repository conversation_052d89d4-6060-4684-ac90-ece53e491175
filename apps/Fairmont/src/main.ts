import { AdsTheme } from "@accor/ads-components"
import { BrandInitConfig } from "@shared/types"
import { initializeApp } from "@apps/common/src/main"
import logoInvertSrc from "./assets/brand-logo-invert.svg?url"
import logoSrc from "./assets/brand-logo.svg?url"

// Define brand-specific configuration
const theme = {
  colors: {
    accent: {
      base: {
        dark: "#FFFFFF",
        light: "#2F2F2F"
      },
      hover: {
        light: "#FFFFFF"
      },
      pressed: {
        light: "#3A3A3A"
      }
    },
    bg: {
      min: {
        base: {
          dark: "white",
          light: "black"
        }
      }
    },
    "on-accent": {
      base: {
        light: "white"
      }
    },
    primary: {
      base: {
        dark: "black",
        light: "white"
      },
      hover: {
        light: "black"
      },
      pressed: {
        light: "black"
      }
    },
    "surface-container": {
      low: {
        base: {
          light: "black"
        }
      }
    }
  },
  radii: {
    button: "1px"
  },
  space: {
    px: "4px"
  }
} as AdsTheme

const brandConfig: BrandInitConfig = {
  logo: logoSrc,
  logoInvert: logoInvertSrc,
  theme: theme
}

// Initialize the app
initializeApp(brandConfig).then((app) => {
  // Mount the app to the DOM
  app.mount("#app")
})
