# Release

### DEV

- https://int-www.fairmont.com/booking/
- https://dev-booking-fairmont.aws.accor.com/booking/

The dev and int environment are automatically deployed when new commit are pushed on the `develop` branch

Temporary env can be deployed for each merge request in the CI with the `deploy-branch` job

- https://JIRA-ID.dev-booking-fairmont.aws.accor.com/booking/

### REC

- https://rec-www.fairmont.com/booking/
- https://rc-0-0-0.dev-booking-fairmont.aws.accor.com/booking/

To update REC environment you need to execute the job `release-branch-auto` in the `tag` stage of the wanted `develop` pipeline, that will trigger the following action:

- Creation of a new branch corresponding to the current version `release/X.X.X`
- Creation of MR from the new branch to main
- Update the `release/0.0.0`
- Update of the `package.json` and `pnpm workspace` version
- Manually create the next release in Jira

Then the pipeline of the new release update the remote env

### PREPROD

- https://pre-www.fairmont.com/booking/
- https://oat-booking-fairmont.aws.accor.com/booking/

- merge wanted release into main
- trigger job `release-tag`" in the `tag` stage in the pipeline, this will create a tag with the correct version and generate the release in git

- in the [infra repository](https://gitlab.softfactory-accor.net/rbac/cd/gdp/brands/brandsbooking/infrastructure/booking-fairmont/-/tree/dev) manually create a new pipeline
- choose `oat` branch
- add variable `TF_VAR_artifact_version_spa` with the version number to release (format : x.x.x)
- add variable `TF_VAR_artifact_version_auth` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_oidc` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_api` with the version number to release (in case of lambda update)(format : x.x.x)

### PROD

- https://www.fairmont.com/booking/
- https://booking-fairmont.aws.accor.com/booking/

@TODO confirm process with Abdellatif during first prod release

- merge wanted release into main
- trigger job `release-tag`" in the `tag` stage in the pipeline, this will create a tag with the correct version and generate the release in git

- fill release form and wait for validation from Accor tech team
- in the [infra repository](https://gitlab.softfactory-accor.net/rbac/cd/gdp/brands/brandsbooking/infrastructure/booking-fairmont/-/tree/dev) manually create a new pipeline
- choose `pro` branch
- add variable `TF_VAR_artifact_version_spa` with the version number to release (format : x.x.x)
- add variable `TF_VAR_artifact_version_auth` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_oidc` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_api` with the version number to release (in case of lambda update)(format : x.x.x)

### Rollback process

@TODO test this rollback process

- fill release form and wait for validation from Accor tech team (@TODO confirm that we need to do this for rollbacks)
- in the [infra repository](https://gitlab.softfactory-accor.net/rbac/cd/gdp/brands/brandsbooking/infrastructure/booking-fairmont/-/tree/dev) manually create a new pipeline with a previous version number
- choose `oat` or `pro` branch
- add variable `TF_VAR_artifact_version_spa` with the version number to release (format : x.x.x)
- add variable `TF_VAR_artifact_version_auth` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_oidc` with the version number to release (in case of lambda update)(format : x.x.x)
- add variable `TF_VAR_artifact_version_api` with the version number to release (in case of lambda update)(format : x.x.x)
