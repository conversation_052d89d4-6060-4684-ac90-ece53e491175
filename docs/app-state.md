# App state

Since we must support interoperability throughout the funnel, app state is managed through differents layers and can be explained visually thanks to the graph below.

![Global app state](./assets/app-state.png "Global app state")

## Navigation / first app loading and behavior

Each time the user is navigating throughout the funnel (either on its initial loading or through actions throughout the app), navigation guards are triggered to ensure data consistency.

Every source of data is held by the query parameters.

### Data priority

Data part can have predececence over other data part and the priority is as follow :

1. Basket
2. Query parameters

Which mean that if a data in the `query parameter` doesn't match the same value describe in the `basket`, it'll be overriden by the `basket` value

### Technical flow

- User reach the app
- [Router guards](../apps/Common/src/router/guards/index.ts) are triggered in the following order :
  1. [guardBasket](../apps/Common/src/router/guards/guard-basket.ts) - ensure that query parameters are matching the value held in the other query parameters - **only if a `basketId` query parameter is defined**
  2. [guardQueryParametersHotel](../apps/Common/src/router/guards/guard-query-parameters-hotel.ts) - ensure that the hotel defined exists, otherwise go to the search page
  3. [guardQueryParametersDates](../apps/Common/src/router/guards/guard-query-parameters-dates.ts) - ensure that the dates defined respect the business constraints (no booking prior to today and such)
  4. [guardQueryParametersRooms](../apps/Common/src/router/guards/guard-query-parameters-rooms.ts) - ensure that each room define respect the API constraints regarding the occupancy and so on
- User navigation is completed and the page load
- [Composables](../apps/Common/src/composables/index.ts) who held the application state/data are hydrated through the query parameter - **and not directly the API basket**
- User update the composables values through its interactions, and the redirection link to the next step/page is updated with updated query parameters - and potentially, before navigation, basket updates

## User interaction

When the user do given actions (search, picking a room and so on), it must be reflected on either the basket or the associated query parameter.

> Beware of the validation process, if you update query parameters without updating the basket when you have one, the navigation guard will remove those.

## Helpers and developer experience

To ease this process throughout the development, multiples helpers are available :

- [useBasket](../apps/Common/src/composables/useBasket.ts) to access helper function to create or update the user basket `upsertBasket`
- [useSearch](../apps/Common/src/composables/useSearch.ts) to manipulate the "real-time" data for the component
- [useSearchQueryParams](../apps/Common/src/composables/useSearchQueryParams.ts) handle the synchronization from the query parameters to the `useSearch` composable data. It also expose a reactive `queryParameters` value which is synchronized with the `useSearch` composable value to not have to compute the query parameters for each redirection
