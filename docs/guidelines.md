# ♻️ Guidelines

### 🎨 Frontend

Our Kaliop frontend team has created a documentation with guidelines to follow. Please refer to [this Netlify link](https://kaliop-frontend-guidelines.netlify.app/general/) !

### 👾 SVG Icons

All SVG icons are mostly stored in `storybook/src/assets/icons/`.

If you need to add icons, pass them through [SVGOMG](https://svgomg.net/) to clean them.

For the SVGOMG settings, everything should be enabled **EXCEPT** :

- Remove xmlns
- Style to attributes
- Remove raster images
- Round/rewrite numbers list
- Remove viewbox

To manage the color of your icons: **Do not use inline color** (unless the colors are fixed, like in a brand logo). Instead, use `fill="currentColor"` on paths that need color.

### 🔣 Translations keys naming

Use snake_case to name translations keys (except for ADS components override, that used kebab-case)

```json
{
  "ads": {
    ...
    "input": {
      "show-password": "Show password", // kebab-case, as its an override of ADS Input translations
      "hide-password": "Hide password"
    }
  },
  ...
  "ui_room": { // snake_case, as its our own translations
    "accessibility_label": "Accessible room required",
    "accessibility_section": "Accessibility preferences",
    "adults_label": "Adults",
    "child_age": "Child {number} age",
    "children_label": "Children",
    "children_sublabel": "Child until {age} years old",
    "room": "Room {roomNumber}"
  }
}
```

### 🧩 Components naming

As strongly recommended by Vue [here](https://vuejs.org/style-guide/rules-strongly-recommended.html#component-name-casing-in-templates), in most projects, component names should always be PascalCase.

PascalCase has a few advantages over kebab-case:

- `<MyComponent>` is more visually distinct from a single-word HTML element than `<my-component>`, because there are two character differences (the two capitals), rather than just one (a hyphen).
- If you use any non-Vue custom elements in your templates, such as a web component, PascalCase ensures that your Vue components remain distinctly visible.

```html
<!-- Good naming -->
<UiButton text="Good" />

<!-- Bad naming -->
<ui-button text="Bad" />
```
