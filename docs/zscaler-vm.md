# 🔳 ZScaler VM

If you need to use the ZScaler VM, you need to follow [this documentation](https://docs.google.com/document/d/1no6uKni5onQyWO5_g2aisRvr9tawG40TSBUm8lScCTg) and these steps next :

### 🛠️ Setup

Clone the [accor-vpn-setup](https://git.kaliop.net/customers/accor/accor-vpn-setup) repository.

### ⚙️ Run

1. start the VM
2. authenticate you from ZScaler (even if it's tell you are already logged in)
3. if not already done, add the following alias in your `~/.bash_aliases` file :

```
## to add automatically all Accor hosts in your /etc/hosts and generate bridges from the VM to your local
alias accorzscalerip="sudo ip a a *************/24 dev vboxnet0 && sudo ip link set vboxnet0 up && cd [your-usual-workspace]/accor-vpn-setup/ && ./routing.sh && cd -"

## to refresh automatically all Accor hosts in your /etc/hosts
alias accorrouting="cd [your-usual-workspace]/accor-vpn-setup/ && ./routing.sh && cd -"
```

4. don't forget to update `[your-usual-workspace]` by your usual workspace (e.g. `/home/<USER>/www`)
5. run command `source ~/.bashrc`
6. run command `accorzscalerip`
7. check bridges exist with `ip r` command, you should see a lot of them like this :

```
$ ip r
...
...
********** via ************* dev vboxnet0
********** via ************* dev vboxnet0
********** via ************* dev vboxnet0
********** via ************* dev vboxnet0
********** via ************* dev vboxnet0
...
...
```

8. check in your /etc/hosts, you should see a lot of new hosts at the end too :

```
## accor script start
********** registry.softfactory-accor.net
********** gitlab.softfactory-accor.net
********** devops.pages.softfactory-accor.net
********** evylnxnusksc01.accor.net
********** nexus.softfactory-accor.net
********** ecom.pages.softfactory-accor.net
********** projects.pages.softfactory-accor.net
********** hotel-description-uat2.accor.net
**********0 rec1-all.accor.com
**********1 rec2-all.accor.com
************* rec-login.accor.com
************* backend-dev.all-inclusive-dev.lzdev.cloud
************* backend-dev.all-inclusive-dev.lzdev.cloud
************** backend-dev.all-inclusive-dev.lzdev.cloud
************* backend-dev.all-inclusive-dev.lzdev.cloud
*********** backend-rec.all-inclusive-rec.lzdev.cloud
*********** backend-rec.all-inclusive-rec.lzdev.cloud
...
## accor script end
```

> ℹ️ Note : in case of issues when you try to access Accor tools in local, restart the VM and redo the authentication etc
>
> 💡 Tips : accessing Accor Gitlab or Nexus registry from the VM can sometimes fix the problem to access them in local
