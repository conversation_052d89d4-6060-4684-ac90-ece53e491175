# Persisting for store

### Params

**propName**: description -> **_default value_**

**key**: Key used to reference the stored deserialized data in the storage. -> **_store.$id_**
**storage**: Storage to persist the data to -> **_localStorage_**
**serializer**: Custom serializer to serialize data before persisted and deserialize data before rehydrating the store -> **_JSON.stringify/destr_**
**pick**: Array of dot-notation paths to pick what should be persisted. [] means no state is persisted and undefined means the whole state is persisted -> **_undefined_**
**omit**: Array of dot-notation paths to omit from what should be persisted. [] or undefined means the whole state persisted (nothing is omitted) -> **_undefined_**
**beforeHydrate** -> Hook function run before hydrating a store state with persisted data. The hook gives access to the whole PiniaPluginContext. This can be used to enforce specific actions before hydration -> **_undefined_**
**afterHydrate** -> Hook function run after rehydrating a persisted state. The hook gives access to the whole PiniaPluginContext. This can be used to enforce specific actions after hydration -> **_undefined_**
**debug**: When set to true, any error that may occur while persisting/hydrating stores will be logged with console.error -> **_false_**
