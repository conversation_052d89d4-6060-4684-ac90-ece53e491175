# 💡 Good to know

### ⚛️ Storybook (v8.5.3)

In storybook 8.5.3, Storybook does not give the priority to the `vite.config.ts` config file since it overrides the entry for his own configuration (@see https://github.com/storybookjs/storybook/blob/v8.5.3/code/builders/builder-vite/src/vite-server.ts#L15)

You can configure basically all the properties you want in the `vite.config.ts` beside `server`, `appType` and `optimizeDeps`.

For those properties, you must go through viteFinal in the `main.ts` file in `.storybook` folder.
